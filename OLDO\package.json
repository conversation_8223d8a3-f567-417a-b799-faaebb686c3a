{"private": true, "scripts": {"dev": "npm run development", "development": "mix", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "prod": "npm run production", "production": "mix --production"}, "devDependencies": {"@popperjs/core": "^2.10.2", "autoprefixer": "^10.4.14", "axios": "^1.1.2", "bootstrap": "^5.2.1", "laravel-mix": "^6.0.15", "lodash": "^4.17.19", "postcss": "^8.4.25", "sass": "^1.32.11", "tailwindcss": "^3.3.2", "vite": "^3.0.0", "vue": "^3.2.37", "vue-loader": "^17.0.0"}, "dependencies": {"@sipec/vue3-tags-input": "^3.0.4", "@vuepic/vue-datepicker": "^3.6.3", "@vueuse/head": "^2.0.0", "babel-loader": "^9.1.0", "firebase": "^10.5.0", "laravel-vue-pagination": "^4.1.1", "swiper": "^10.2.0", "vue-element-loading": "^3.0.1", "vue-i18n": "^9.2.2", "vue-inner-image-zoom": "^2.0.0", "vue-next-select": "^2.10.5", "vue-router": "^4.1.6", "vue-simple-range-slider": "^1.1.0", "vue-star-rating": "^2.1.0", "vue-toastification": "^2.0.0-rc.5", "vue3-apexcharts": "^1.5.3", "vue3-print-nb": "^0.1.4", "vue3-quill": "^0.3.0", "vue3-simple-alert": "^1.0.4", "vuex": "^4.1.0", "vuex-persistedstate": "^4.1.0"}}