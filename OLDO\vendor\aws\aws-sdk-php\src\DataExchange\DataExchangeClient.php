<?php
namespace Aws\DataExchange;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AWS Data Exchange** service.
 * @method \Aws\Result acceptDataGrant(array $args = [])
 * @method \GuzzleHttp\Promise\Promise acceptDataGrantAsync(array $args = [])
 * @method \Aws\Result cancelJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise cancelJobAsync(array $args = [])
 * @method \Aws\Result createDataGrant(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createDataGrantAsync(array $args = [])
 * @method \Aws\Result createDataSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createDataSetAsync(array $args = [])
 * @method \Aws\Result createEventAction(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createEventActionAsync(array $args = [])
 * @method \Aws\Result createJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createJobAsync(array $args = [])
 * @method \Aws\Result createRevision(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createRevisionAsync(array $args = [])
 * @method \Aws\Result deleteAsset(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteAssetAsync(array $args = [])
 * @method \Aws\Result deleteDataGrant(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteDataGrantAsync(array $args = [])
 * @method \Aws\Result deleteDataSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteDataSetAsync(array $args = [])
 * @method \Aws\Result deleteEventAction(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteEventActionAsync(array $args = [])
 * @method \Aws\Result deleteRevision(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteRevisionAsync(array $args = [])
 * @method \Aws\Result getAsset(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getAssetAsync(array $args = [])
 * @method \Aws\Result getDataGrant(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getDataGrantAsync(array $args = [])
 * @method \Aws\Result getDataSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getDataSetAsync(array $args = [])
 * @method \Aws\Result getEventAction(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getEventActionAsync(array $args = [])
 * @method \Aws\Result getJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getJobAsync(array $args = [])
 * @method \Aws\Result getReceivedDataGrant(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getReceivedDataGrantAsync(array $args = [])
 * @method \Aws\Result getRevision(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getRevisionAsync(array $args = [])
 * @method \Aws\Result listDataGrants(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDataGrantsAsync(array $args = [])
 * @method \Aws\Result listDataSetRevisions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDataSetRevisionsAsync(array $args = [])
 * @method \Aws\Result listDataSets(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDataSetsAsync(array $args = [])
 * @method \Aws\Result listEventActions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listEventActionsAsync(array $args = [])
 * @method \Aws\Result listJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listJobsAsync(array $args = [])
 * @method \Aws\Result listReceivedDataGrants(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listReceivedDataGrantsAsync(array $args = [])
 * @method \Aws\Result listRevisionAssets(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listRevisionAssetsAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result revokeRevision(array $args = [])
 * @method \GuzzleHttp\Promise\Promise revokeRevisionAsync(array $args = [])
 * @method \Aws\Result sendApiAsset(array $args = [])
 * @method \GuzzleHttp\Promise\Promise sendApiAssetAsync(array $args = [])
 * @method \Aws\Result sendDataSetNotification(array $args = [])
 * @method \GuzzleHttp\Promise\Promise sendDataSetNotificationAsync(array $args = [])
 * @method \Aws\Result startJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startJobAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateAsset(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateAssetAsync(array $args = [])
 * @method \Aws\Result updateDataSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateDataSetAsync(array $args = [])
 * @method \Aws\Result updateEventAction(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateEventActionAsync(array $args = [])
 * @method \Aws\Result updateRevision(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateRevisionAsync(array $args = [])
 */
class DataExchangeClient extends AwsClient {}
