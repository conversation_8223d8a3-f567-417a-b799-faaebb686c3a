<?php
namespace Aws\Efs;

use Aws\AwsClient;

/**
 * This client is used to interact with **Amazon EFS**.
 *
 * @method \Aws\Result createAccessPoint(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createAccessPointAsync(array $args = [])
 * @method \Aws\Result createFileSystem(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createFileSystemAsync(array $args = [])
 * @method \Aws\Result createMountTarget(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createMountTargetAsync(array $args = [])
 * @method \Aws\Result createReplicationConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createReplicationConfigurationAsync(array $args = [])
 * @method \Aws\Result createTags(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createTagsAsync(array $args = [])
 * @method \Aws\Result deleteAccessPoint(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteAccessPointAsync(array $args = [])
 * @method \Aws\Result deleteFileSystem(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteFileSystemAsync(array $args = [])
 * @method \Aws\Result deleteFileSystemPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteFileSystemPolicyAsync(array $args = [])
 * @method \Aws\Result deleteMountTarget(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteMountTargetAsync(array $args = [])
 * @method \Aws\Result deleteReplicationConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteReplicationConfigurationAsync(array $args = [])
 * @method \Aws\Result deleteTags(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteTagsAsync(array $args = [])
 * @method \Aws\Result describeAccessPoints(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeAccessPointsAsync(array $args = [])
 * @method \Aws\Result describeAccountPreferences(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeAccountPreferencesAsync(array $args = [])
 * @method \Aws\Result describeBackupPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeBackupPolicyAsync(array $args = [])
 * @method \Aws\Result describeFileSystemPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeFileSystemPolicyAsync(array $args = [])
 * @method \Aws\Result describeFileSystems(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeFileSystemsAsync(array $args = [])
 * @method \Aws\Result describeLifecycleConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeLifecycleConfigurationAsync(array $args = [])
 * @method \Aws\Result describeMountTargetSecurityGroups(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeMountTargetSecurityGroupsAsync(array $args = [])
 * @method \Aws\Result describeMountTargets(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeMountTargetsAsync(array $args = [])
 * @method \Aws\Result describeReplicationConfigurations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeReplicationConfigurationsAsync(array $args = [])
 * @method \Aws\Result describeTags(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeTagsAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result modifyMountTargetSecurityGroups(array $args = [])
 * @method \GuzzleHttp\Promise\Promise modifyMountTargetSecurityGroupsAsync(array $args = [])
 * @method \Aws\Result putAccountPreferences(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putAccountPreferencesAsync(array $args = [])
 * @method \Aws\Result putBackupPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putBackupPolicyAsync(array $args = [])
 * @method \Aws\Result putFileSystemPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putFileSystemPolicyAsync(array $args = [])
 * @method \Aws\Result putLifecycleConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putLifecycleConfigurationAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateFileSystem(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateFileSystemAsync(array $args = [])
 * @method \Aws\Result updateFileSystemProtection(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateFileSystemProtectionAsync(array $args = [])
 */
class EfsClient extends AwsClient {}
