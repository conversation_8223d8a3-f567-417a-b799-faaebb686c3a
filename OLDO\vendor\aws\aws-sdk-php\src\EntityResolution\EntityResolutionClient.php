<?php
namespace Aws\EntityResolution;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AWS EntityResolution** service.
 * @method \Aws\Result addPolicyStatement(array $args = [])
 * @method \GuzzleHttp\Promise\Promise addPolicyStatementAsync(array $args = [])
 * @method \Aws\Result batchDeleteUniqueId(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchDeleteUniqueIdAsync(array $args = [])
 * @method \Aws\Result createIdMappingWorkflow(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createIdMappingWorkflowAsync(array $args = [])
 * @method \Aws\Result createIdNamespace(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createIdNamespaceAsync(array $args = [])
 * @method \Aws\Result createMatchingWorkflow(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createMatchingWorkflowAsync(array $args = [])
 * @method \Aws\Result createSchemaMapping(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createSchemaMappingAsync(array $args = [])
 * @method \Aws\Result deleteIdMappingWorkflow(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteIdMappingWorkflowAsync(array $args = [])
 * @method \Aws\Result deleteIdNamespace(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteIdNamespaceAsync(array $args = [])
 * @method \Aws\Result deleteMatchingWorkflow(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteMatchingWorkflowAsync(array $args = [])
 * @method \Aws\Result deletePolicyStatement(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deletePolicyStatementAsync(array $args = [])
 * @method \Aws\Result deleteSchemaMapping(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteSchemaMappingAsync(array $args = [])
 * @method \Aws\Result getIdMappingJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getIdMappingJobAsync(array $args = [])
 * @method \Aws\Result getIdMappingWorkflow(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getIdMappingWorkflowAsync(array $args = [])
 * @method \Aws\Result getIdNamespace(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getIdNamespaceAsync(array $args = [])
 * @method \Aws\Result getMatchId(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getMatchIdAsync(array $args = [])
 * @method \Aws\Result getMatchingJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getMatchingJobAsync(array $args = [])
 * @method \Aws\Result getMatchingWorkflow(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getMatchingWorkflowAsync(array $args = [])
 * @method \Aws\Result getPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getPolicyAsync(array $args = [])
 * @method \Aws\Result getProviderService(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getProviderServiceAsync(array $args = [])
 * @method \Aws\Result getSchemaMapping(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getSchemaMappingAsync(array $args = [])
 * @method \Aws\Result listIdMappingJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listIdMappingJobsAsync(array $args = [])
 * @method \Aws\Result listIdMappingWorkflows(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listIdMappingWorkflowsAsync(array $args = [])
 * @method \Aws\Result listIdNamespaces(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listIdNamespacesAsync(array $args = [])
 * @method \Aws\Result listMatchingJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listMatchingJobsAsync(array $args = [])
 * @method \Aws\Result listMatchingWorkflows(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listMatchingWorkflowsAsync(array $args = [])
 * @method \Aws\Result listProviderServices(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listProviderServicesAsync(array $args = [])
 * @method \Aws\Result listSchemaMappings(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listSchemaMappingsAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result putPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putPolicyAsync(array $args = [])
 * @method \Aws\Result startIdMappingJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startIdMappingJobAsync(array $args = [])
 * @method \Aws\Result startMatchingJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startMatchingJobAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateIdMappingWorkflow(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateIdMappingWorkflowAsync(array $args = [])
 * @method \Aws\Result updateIdNamespace(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateIdNamespaceAsync(array $args = [])
 * @method \Aws\Result updateMatchingWorkflow(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateMatchingWorkflowAsync(array $args = [])
 * @method \Aws\Result updateSchemaMapping(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateSchemaMappingAsync(array $args = [])
 */
class EntityResolutionClient extends AwsClient {}
