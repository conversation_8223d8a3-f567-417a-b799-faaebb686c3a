<?php
namespace Aws\ForecastQueryService;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Amazon Forecast Query Service** service.
 * @method \Aws\Result queryForecast(array $args = [])
 * @method \GuzzleHttp\Promise\Promise queryForecastAsync(array $args = [])
 * @method \Aws\Result queryWhatIfForecast(array $args = [])
 * @method \GuzzleHttp\Promise\Promise queryWhatIfForecastAsync(array $args = [])
 */
class ForecastQueryServiceClient extends AwsClient {}
