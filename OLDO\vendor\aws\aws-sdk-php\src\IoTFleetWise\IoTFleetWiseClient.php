<?php
namespace Aws\IoTFleetWise;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AWS IoT FleetWise** service.
 * @method \Aws\Result associateVehicleFleet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise associateVehicleFleetAsync(array $args = [])
 * @method \Aws\Result batchCreateVehicle(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchCreateVehicleAsync(array $args = [])
 * @method \Aws\Result batchUpdateVehicle(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchUpdateVehicleAsync(array $args = [])
 * @method \Aws\Result createCampaign(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createCampaignAsync(array $args = [])
 * @method \Aws\Result createDecoderManifest(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createDecoderManifestAsync(array $args = [])
 * @method \Aws\Result createFleet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createFleetAsync(array $args = [])
 * @method \Aws\Result createModelManifest(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createModelManifestAsync(array $args = [])
 * @method \Aws\Result createSignalCatalog(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createSignalCatalogAsync(array $args = [])
 * @method \Aws\Result createStateTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createStateTemplateAsync(array $args = [])
 * @method \Aws\Result createVehicle(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createVehicleAsync(array $args = [])
 * @method \Aws\Result deleteCampaign(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteCampaignAsync(array $args = [])
 * @method \Aws\Result deleteDecoderManifest(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteDecoderManifestAsync(array $args = [])
 * @method \Aws\Result deleteFleet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteFleetAsync(array $args = [])
 * @method \Aws\Result deleteModelManifest(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteModelManifestAsync(array $args = [])
 * @method \Aws\Result deleteSignalCatalog(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteSignalCatalogAsync(array $args = [])
 * @method \Aws\Result deleteStateTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteStateTemplateAsync(array $args = [])
 * @method \Aws\Result deleteVehicle(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteVehicleAsync(array $args = [])
 * @method \Aws\Result disassociateVehicleFleet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disassociateVehicleFleetAsync(array $args = [])
 * @method \Aws\Result getCampaign(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getCampaignAsync(array $args = [])
 * @method \Aws\Result getDecoderManifest(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getDecoderManifestAsync(array $args = [])
 * @method \Aws\Result getEncryptionConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getEncryptionConfigurationAsync(array $args = [])
 * @method \Aws\Result getFleet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getFleetAsync(array $args = [])
 * @method \Aws\Result getLoggingOptions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getLoggingOptionsAsync(array $args = [])
 * @method \Aws\Result getModelManifest(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getModelManifestAsync(array $args = [])
 * @method \Aws\Result getRegisterAccountStatus(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getRegisterAccountStatusAsync(array $args = [])
 * @method \Aws\Result getSignalCatalog(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getSignalCatalogAsync(array $args = [])
 * @method \Aws\Result getStateTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getStateTemplateAsync(array $args = [])
 * @method \Aws\Result getVehicle(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getVehicleAsync(array $args = [])
 * @method \Aws\Result getVehicleStatus(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getVehicleStatusAsync(array $args = [])
 * @method \Aws\Result importDecoderManifest(array $args = [])
 * @method \GuzzleHttp\Promise\Promise importDecoderManifestAsync(array $args = [])
 * @method \Aws\Result importSignalCatalog(array $args = [])
 * @method \GuzzleHttp\Promise\Promise importSignalCatalogAsync(array $args = [])
 * @method \Aws\Result listCampaigns(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listCampaignsAsync(array $args = [])
 * @method \Aws\Result listDecoderManifestNetworkInterfaces(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDecoderManifestNetworkInterfacesAsync(array $args = [])
 * @method \Aws\Result listDecoderManifestSignals(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDecoderManifestSignalsAsync(array $args = [])
 * @method \Aws\Result listDecoderManifests(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDecoderManifestsAsync(array $args = [])
 * @method \Aws\Result listFleets(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listFleetsAsync(array $args = [])
 * @method \Aws\Result listFleetsForVehicle(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listFleetsForVehicleAsync(array $args = [])
 * @method \Aws\Result listModelManifestNodes(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listModelManifestNodesAsync(array $args = [])
 * @method \Aws\Result listModelManifests(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listModelManifestsAsync(array $args = [])
 * @method \Aws\Result listSignalCatalogNodes(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listSignalCatalogNodesAsync(array $args = [])
 * @method \Aws\Result listSignalCatalogs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listSignalCatalogsAsync(array $args = [])
 * @method \Aws\Result listStateTemplates(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listStateTemplatesAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result listVehicles(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listVehiclesAsync(array $args = [])
 * @method \Aws\Result listVehiclesInFleet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listVehiclesInFleetAsync(array $args = [])
 * @method \Aws\Result putEncryptionConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putEncryptionConfigurationAsync(array $args = [])
 * @method \Aws\Result putLoggingOptions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putLoggingOptionsAsync(array $args = [])
 * @method \Aws\Result registerAccount(array $args = [])
 * @method \GuzzleHttp\Promise\Promise registerAccountAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateCampaign(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateCampaignAsync(array $args = [])
 * @method \Aws\Result updateDecoderManifest(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateDecoderManifestAsync(array $args = [])
 * @method \Aws\Result updateFleet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateFleetAsync(array $args = [])
 * @method \Aws\Result updateModelManifest(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateModelManifestAsync(array $args = [])
 * @method \Aws\Result updateSignalCatalog(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateSignalCatalogAsync(array $args = [])
 * @method \Aws\Result updateStateTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateStateTemplateAsync(array $args = [])
 * @method \Aws\Result updateVehicle(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateVehicleAsync(array $args = [])
 */
class IoTFleetWiseClient extends AwsClient {}
