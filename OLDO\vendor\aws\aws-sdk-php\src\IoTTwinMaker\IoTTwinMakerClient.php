<?php
namespace Aws\IoTTwinMaker;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AWS IoT TwinMaker** service.
 * @method \Aws\Result batchPutPropertyValues(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchPutPropertyValuesAsync(array $args = [])
 * @method \Aws\Result cancelMetadataTransferJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise cancelMetadataTransferJobAsync(array $args = [])
 * @method \Aws\Result createComponentType(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createComponentTypeAsync(array $args = [])
 * @method \Aws\Result createEntity(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createEntityAsync(array $args = [])
 * @method \Aws\Result createMetadataTransferJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createMetadataTransferJobAsync(array $args = [])
 * @method \Aws\Result createScene(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createSceneAsync(array $args = [])
 * @method \Aws\Result createSyncJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createSyncJobAsync(array $args = [])
 * @method \Aws\Result createWorkspace(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createWorkspaceAsync(array $args = [])
 * @method \Aws\Result deleteComponentType(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteComponentTypeAsync(array $args = [])
 * @method \Aws\Result deleteEntity(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteEntityAsync(array $args = [])
 * @method \Aws\Result deleteScene(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteSceneAsync(array $args = [])
 * @method \Aws\Result deleteSyncJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteSyncJobAsync(array $args = [])
 * @method \Aws\Result deleteWorkspace(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteWorkspaceAsync(array $args = [])
 * @method \Aws\Result executeQuery(array $args = [])
 * @method \GuzzleHttp\Promise\Promise executeQueryAsync(array $args = [])
 * @method \Aws\Result getComponentType(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getComponentTypeAsync(array $args = [])
 * @method \Aws\Result getEntity(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getEntityAsync(array $args = [])
 * @method \Aws\Result getMetadataTransferJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getMetadataTransferJobAsync(array $args = [])
 * @method \Aws\Result getPricingPlan(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getPricingPlanAsync(array $args = [])
 * @method \Aws\Result getPropertyValue(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getPropertyValueAsync(array $args = [])
 * @method \Aws\Result getPropertyValueHistory(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getPropertyValueHistoryAsync(array $args = [])
 * @method \Aws\Result getScene(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getSceneAsync(array $args = [])
 * @method \Aws\Result getSyncJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getSyncJobAsync(array $args = [])
 * @method \Aws\Result getWorkspace(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getWorkspaceAsync(array $args = [])
 * @method \Aws\Result listComponentTypes(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listComponentTypesAsync(array $args = [])
 * @method \Aws\Result listComponents(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listComponentsAsync(array $args = [])
 * @method \Aws\Result listEntities(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listEntitiesAsync(array $args = [])
 * @method \Aws\Result listMetadataTransferJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listMetadataTransferJobsAsync(array $args = [])
 * @method \Aws\Result listProperties(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listPropertiesAsync(array $args = [])
 * @method \Aws\Result listScenes(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listScenesAsync(array $args = [])
 * @method \Aws\Result listSyncJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listSyncJobsAsync(array $args = [])
 * @method \Aws\Result listSyncResources(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listSyncResourcesAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result listWorkspaces(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listWorkspacesAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateComponentType(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateComponentTypeAsync(array $args = [])
 * @method \Aws\Result updateEntity(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateEntityAsync(array $args = [])
 * @method \Aws\Result updatePricingPlan(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updatePricingPlanAsync(array $args = [])
 * @method \Aws\Result updateScene(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateSceneAsync(array $args = [])
 * @method \Aws\Result updateWorkspace(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateWorkspaceAsync(array $args = [])
 */
class IoTTwinMakerClient extends AwsClient {}
