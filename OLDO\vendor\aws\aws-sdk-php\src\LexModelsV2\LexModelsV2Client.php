<?php
namespace Aws\LexModelsV2;

use Aws\AwsClient;
use Aws\CommandInterface;
use Psr\Http\Message\RequestInterface;

/**
 * This client is used to interact with the **Amazon Lex Model Building V2** service.
 * @method \Aws\Result batchCreateCustomVocabularyItem(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchCreateCustomVocabularyItemAsync(array $args = [])
 * @method \Aws\Result batchDeleteCustomVocabularyItem(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchDeleteCustomVocabularyItemAsync(array $args = [])
 * @method \Aws\Result batchUpdateCustomVocabularyItem(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchUpdateCustomVocabularyItemAsync(array $args = [])
 * @method \Aws\Result buildBotLocale(array $args = [])
 * @method \GuzzleHttp\Promise\Promise buildBotLocaleAsync(array $args = [])
 * @method \Aws\Result createBot(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createBotAsync(array $args = [])
 * @method \Aws\Result createBotAlias(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createBotAliasAsync(array $args = [])
 * @method \Aws\Result createBotLocale(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createBotLocaleAsync(array $args = [])
 * @method \Aws\Result createBotReplica(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createBotReplicaAsync(array $args = [])
 * @method \Aws\Result createBotVersion(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createBotVersionAsync(array $args = [])
 * @method \Aws\Result createExport(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createExportAsync(array $args = [])
 * @method \Aws\Result createIntent(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createIntentAsync(array $args = [])
 * @method \Aws\Result createResourcePolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createResourcePolicyAsync(array $args = [])
 * @method \Aws\Result createResourcePolicyStatement(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createResourcePolicyStatementAsync(array $args = [])
 * @method \Aws\Result createSlot(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createSlotAsync(array $args = [])
 * @method \Aws\Result createSlotType(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createSlotTypeAsync(array $args = [])
 * @method \Aws\Result createTestSetDiscrepancyReport(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createTestSetDiscrepancyReportAsync(array $args = [])
 * @method \Aws\Result createUploadUrl(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createUploadUrlAsync(array $args = [])
 * @method \Aws\Result deleteBot(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteBotAsync(array $args = [])
 * @method \Aws\Result deleteBotAlias(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteBotAliasAsync(array $args = [])
 * @method \Aws\Result deleteBotLocale(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteBotLocaleAsync(array $args = [])
 * @method \Aws\Result deleteBotReplica(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteBotReplicaAsync(array $args = [])
 * @method \Aws\Result deleteBotVersion(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteBotVersionAsync(array $args = [])
 * @method \Aws\Result deleteCustomVocabulary(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteCustomVocabularyAsync(array $args = [])
 * @method \Aws\Result deleteExport(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteExportAsync(array $args = [])
 * @method \Aws\Result deleteImport(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteImportAsync(array $args = [])
 * @method \Aws\Result deleteIntent(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteIntentAsync(array $args = [])
 * @method \Aws\Result deleteResourcePolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteResourcePolicyAsync(array $args = [])
 * @method \Aws\Result deleteResourcePolicyStatement(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteResourcePolicyStatementAsync(array $args = [])
 * @method \Aws\Result deleteSlot(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteSlotAsync(array $args = [])
 * @method \Aws\Result deleteSlotType(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteSlotTypeAsync(array $args = [])
 * @method \Aws\Result deleteTestSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteTestSetAsync(array $args = [])
 * @method \Aws\Result deleteUtterances(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteUtterancesAsync(array $args = [])
 * @method \Aws\Result describeBot(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeBotAsync(array $args = [])
 * @method \Aws\Result describeBotAlias(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeBotAliasAsync(array $args = [])
 * @method \Aws\Result describeBotLocale(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeBotLocaleAsync(array $args = [])
 * @method \Aws\Result describeBotRecommendation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeBotRecommendationAsync(array $args = [])
 * @method \Aws\Result describeBotReplica(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeBotReplicaAsync(array $args = [])
 * @method \Aws\Result describeBotResourceGeneration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeBotResourceGenerationAsync(array $args = [])
 * @method \Aws\Result describeBotVersion(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeBotVersionAsync(array $args = [])
 * @method \Aws\Result describeCustomVocabularyMetadata(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeCustomVocabularyMetadataAsync(array $args = [])
 * @method \Aws\Result describeExport(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeExportAsync(array $args = [])
 * @method \Aws\Result describeImport(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeImportAsync(array $args = [])
 * @method \Aws\Result describeIntent(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeIntentAsync(array $args = [])
 * @method \Aws\Result describeResourcePolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeResourcePolicyAsync(array $args = [])
 * @method \Aws\Result describeSlot(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeSlotAsync(array $args = [])
 * @method \Aws\Result describeSlotType(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeSlotTypeAsync(array $args = [])
 * @method \Aws\Result describeTestExecution(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeTestExecutionAsync(array $args = [])
 * @method \Aws\Result describeTestSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeTestSetAsync(array $args = [])
 * @method \Aws\Result describeTestSetDiscrepancyReport(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeTestSetDiscrepancyReportAsync(array $args = [])
 * @method \Aws\Result describeTestSetGeneration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeTestSetGenerationAsync(array $args = [])
 * @method \Aws\Result generateBotElement(array $args = [])
 * @method \GuzzleHttp\Promise\Promise generateBotElementAsync(array $args = [])
 * @method \Aws\Result getTestExecutionArtifactsUrl(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getTestExecutionArtifactsUrlAsync(array $args = [])
 * @method \Aws\Result listAggregatedUtterances(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAggregatedUtterancesAsync(array $args = [])
 * @method \Aws\Result listBotAliasReplicas(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listBotAliasReplicasAsync(array $args = [])
 * @method \Aws\Result listBotAliases(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listBotAliasesAsync(array $args = [])
 * @method \Aws\Result listBotLocales(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listBotLocalesAsync(array $args = [])
 * @method \Aws\Result listBotRecommendations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listBotRecommendationsAsync(array $args = [])
 * @method \Aws\Result listBotReplicas(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listBotReplicasAsync(array $args = [])
 * @method \Aws\Result listBotResourceGenerations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listBotResourceGenerationsAsync(array $args = [])
 * @method \Aws\Result listBotVersionReplicas(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listBotVersionReplicasAsync(array $args = [])
 * @method \Aws\Result listBotVersions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listBotVersionsAsync(array $args = [])
 * @method \Aws\Result listBots(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listBotsAsync(array $args = [])
 * @method \Aws\Result listBuiltInIntents(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listBuiltInIntentsAsync(array $args = [])
 * @method \Aws\Result listBuiltInSlotTypes(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listBuiltInSlotTypesAsync(array $args = [])
 * @method \Aws\Result listCustomVocabularyItems(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listCustomVocabularyItemsAsync(array $args = [])
 * @method \Aws\Result listExports(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listExportsAsync(array $args = [])
 * @method \Aws\Result listImports(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listImportsAsync(array $args = [])
 * @method \Aws\Result listIntentMetrics(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listIntentMetricsAsync(array $args = [])
 * @method \Aws\Result listIntentPaths(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listIntentPathsAsync(array $args = [])
 * @method \Aws\Result listIntentStageMetrics(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listIntentStageMetricsAsync(array $args = [])
 * @method \Aws\Result listIntents(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listIntentsAsync(array $args = [])
 * @method \Aws\Result listRecommendedIntents(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listRecommendedIntentsAsync(array $args = [])
 * @method \Aws\Result listSessionAnalyticsData(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listSessionAnalyticsDataAsync(array $args = [])
 * @method \Aws\Result listSessionMetrics(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listSessionMetricsAsync(array $args = [])
 * @method \Aws\Result listSlotTypes(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listSlotTypesAsync(array $args = [])
 * @method \Aws\Result listSlots(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listSlotsAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result listTestExecutionResultItems(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTestExecutionResultItemsAsync(array $args = [])
 * @method \Aws\Result listTestExecutions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTestExecutionsAsync(array $args = [])
 * @method \Aws\Result listTestSetRecords(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTestSetRecordsAsync(array $args = [])
 * @method \Aws\Result listTestSets(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTestSetsAsync(array $args = [])
 * @method \Aws\Result listUtteranceAnalyticsData(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listUtteranceAnalyticsDataAsync(array $args = [])
 * @method \Aws\Result listUtteranceMetrics(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listUtteranceMetricsAsync(array $args = [])
 * @method \Aws\Result searchAssociatedTranscripts(array $args = [])
 * @method \GuzzleHttp\Promise\Promise searchAssociatedTranscriptsAsync(array $args = [])
 * @method \Aws\Result startBotRecommendation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startBotRecommendationAsync(array $args = [])
 * @method \Aws\Result startBotResourceGeneration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startBotResourceGenerationAsync(array $args = [])
 * @method \Aws\Result startImport(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startImportAsync(array $args = [])
 * @method \Aws\Result startTestExecution(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startTestExecutionAsync(array $args = [])
 * @method \Aws\Result startTestSetGeneration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startTestSetGenerationAsync(array $args = [])
 * @method \Aws\Result stopBotRecommendation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise stopBotRecommendationAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateBot(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateBotAsync(array $args = [])
 * @method \Aws\Result updateBotAlias(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateBotAliasAsync(array $args = [])
 * @method \Aws\Result updateBotLocale(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateBotLocaleAsync(array $args = [])
 * @method \Aws\Result updateBotRecommendation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateBotRecommendationAsync(array $args = [])
 * @method \Aws\Result updateExport(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateExportAsync(array $args = [])
 * @method \Aws\Result updateIntent(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateIntentAsync(array $args = [])
 * @method \Aws\Result updateResourcePolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateResourcePolicyAsync(array $args = [])
 * @method \Aws\Result updateSlot(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateSlotAsync(array $args = [])
 * @method \Aws\Result updateSlotType(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateSlotTypeAsync(array $args = [])
 * @method \Aws\Result updateTestSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateTestSetAsync(array $args = [])
 */
class LexModelsV2Client extends AwsClient {}
