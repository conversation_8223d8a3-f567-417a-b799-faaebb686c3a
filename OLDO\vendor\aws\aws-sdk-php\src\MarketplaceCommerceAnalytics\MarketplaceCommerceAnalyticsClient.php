<?php
namespace Aws\MarketplaceCommerceAnalytics;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AWS Marketplace Commerce Analytics** service.
 *
 * @method \Aws\Result generateDataSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise generateDataSetAsync(array $args = [])
 * @method \Aws\Result startSupportDataExport(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startSupportDataExportAsync(array $args = [])
 */
class MarketplaceCommerceAnalyticsClient extends AwsClient {}
