<?php
namespace Aws\MigrationHubOrchestrator;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AWS Migration Hub Orchestrator** service.
 * @method \Aws\Result createTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createTemplateAsync(array $args = [])
 * @method \Aws\Result createWorkflow(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createWorkflowAsync(array $args = [])
 * @method \Aws\Result createWorkflowStep(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createWorkflowStepAsync(array $args = [])
 * @method \Aws\Result createWorkflowStepGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createWorkflowStepGroupAsync(array $args = [])
 * @method \Aws\Result deleteTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteTemplateAsync(array $args = [])
 * @method \Aws\Result deleteWorkflow(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteWorkflowAsync(array $args = [])
 * @method \Aws\Result deleteWorkflowStep(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteWorkflowStepAsync(array $args = [])
 * @method \Aws\Result deleteWorkflowStepGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteWorkflowStepGroupAsync(array $args = [])
 * @method \Aws\Result getTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getTemplateAsync(array $args = [])
 * @method \Aws\Result getTemplateStep(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getTemplateStepAsync(array $args = [])
 * @method \Aws\Result getTemplateStepGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getTemplateStepGroupAsync(array $args = [])
 * @method \Aws\Result getWorkflow(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getWorkflowAsync(array $args = [])
 * @method \Aws\Result getWorkflowStep(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getWorkflowStepAsync(array $args = [])
 * @method \Aws\Result getWorkflowStepGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getWorkflowStepGroupAsync(array $args = [])
 * @method \Aws\Result listPlugins(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listPluginsAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result listTemplateStepGroups(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTemplateStepGroupsAsync(array $args = [])
 * @method \Aws\Result listTemplateSteps(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTemplateStepsAsync(array $args = [])
 * @method \Aws\Result listTemplates(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTemplatesAsync(array $args = [])
 * @method \Aws\Result listWorkflowStepGroups(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listWorkflowStepGroupsAsync(array $args = [])
 * @method \Aws\Result listWorkflowSteps(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listWorkflowStepsAsync(array $args = [])
 * @method \Aws\Result listWorkflows(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listWorkflowsAsync(array $args = [])
 * @method \Aws\Result retryWorkflowStep(array $args = [])
 * @method \GuzzleHttp\Promise\Promise retryWorkflowStepAsync(array $args = [])
 * @method \Aws\Result startWorkflow(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startWorkflowAsync(array $args = [])
 * @method \Aws\Result stopWorkflow(array $args = [])
 * @method \GuzzleHttp\Promise\Promise stopWorkflowAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateTemplateAsync(array $args = [])
 * @method \Aws\Result updateWorkflow(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateWorkflowAsync(array $args = [])
 * @method \Aws\Result updateWorkflowStep(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateWorkflowStepAsync(array $args = [])
 * @method \Aws\Result updateWorkflowStepGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateWorkflowStepGroupAsync(array $args = [])
 */
class MigrationHubOrchestratorClient extends AwsClient {}
