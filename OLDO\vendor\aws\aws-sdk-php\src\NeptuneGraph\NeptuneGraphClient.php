<?php
namespace Aws\NeptuneGraph;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Amazon Neptune Graph** service.
 * @method \Aws\Result cancelExportTask(array $args = [])
 * @method \GuzzleHttp\Promise\Promise cancelExportTaskAsync(array $args = [])
 * @method \Aws\Result cancelImportTask(array $args = [])
 * @method \GuzzleHttp\Promise\Promise cancelImportTaskAsync(array $args = [])
 * @method \Aws\Result cancelQuery(array $args = [])
 * @method \GuzzleHttp\Promise\Promise cancelQueryAsync(array $args = [])
 * @method \Aws\Result createGraph(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createGraphAsync(array $args = [])
 * @method \Aws\Result createGraphSnapshot(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createGraphSnapshotAsync(array $args = [])
 * @method \Aws\Result createGraphUsingImportTask(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createGraphUsingImportTaskAsync(array $args = [])
 * @method \Aws\Result createPrivateGraphEndpoint(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createPrivateGraphEndpointAsync(array $args = [])
 * @method \Aws\Result deleteGraph(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteGraphAsync(array $args = [])
 * @method \Aws\Result deleteGraphSnapshot(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteGraphSnapshotAsync(array $args = [])
 * @method \Aws\Result deletePrivateGraphEndpoint(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deletePrivateGraphEndpointAsync(array $args = [])
 * @method \Aws\Result executeQuery(array $args = [])
 * @method \GuzzleHttp\Promise\Promise executeQueryAsync(array $args = [])
 * @method \Aws\Result getExportTask(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getExportTaskAsync(array $args = [])
 * @method \Aws\Result getGraph(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getGraphAsync(array $args = [])
 * @method \Aws\Result getGraphSnapshot(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getGraphSnapshotAsync(array $args = [])
 * @method \Aws\Result getGraphSummary(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getGraphSummaryAsync(array $args = [])
 * @method \Aws\Result getImportTask(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getImportTaskAsync(array $args = [])
 * @method \Aws\Result getPrivateGraphEndpoint(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getPrivateGraphEndpointAsync(array $args = [])
 * @method \Aws\Result getQuery(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getQueryAsync(array $args = [])
 * @method \Aws\Result listExportTasks(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listExportTasksAsync(array $args = [])
 * @method \Aws\Result listGraphSnapshots(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listGraphSnapshotsAsync(array $args = [])
 * @method \Aws\Result listGraphs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listGraphsAsync(array $args = [])
 * @method \Aws\Result listImportTasks(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listImportTasksAsync(array $args = [])
 * @method \Aws\Result listPrivateGraphEndpoints(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listPrivateGraphEndpointsAsync(array $args = [])
 * @method \Aws\Result listQueries(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listQueriesAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result resetGraph(array $args = [])
 * @method \GuzzleHttp\Promise\Promise resetGraphAsync(array $args = [])
 * @method \Aws\Result restoreGraphFromSnapshot(array $args = [])
 * @method \GuzzleHttp\Promise\Promise restoreGraphFromSnapshotAsync(array $args = [])
 * @method \Aws\Result startExportTask(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startExportTaskAsync(array $args = [])
 * @method \Aws\Result startImportTask(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startImportTaskAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateGraph(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateGraphAsync(array $args = [])
 */
class NeptuneGraphClient extends AwsClient {}
