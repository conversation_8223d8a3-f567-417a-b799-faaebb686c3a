<?php
namespace Aws\Neptunedata;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Amazon NeptuneData** service.
 * @method \Aws\Result cancelGremlinQuery(array $args = [])
 * @method \GuzzleHttp\Promise\Promise cancelGremlinQueryAsync(array $args = [])
 * @method \Aws\Result cancelLoaderJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise cancelLoaderJobAsync(array $args = [])
 * @method \Aws\Result cancelMLDataProcessingJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise cancelMLDataProcessingJobAsync(array $args = [])
 * @method \Aws\Result cancelMLModelTrainingJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise cancelMLModelTrainingJobAsync(array $args = [])
 * @method \Aws\Result cancelMLModelTransformJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise cancelMLModelTransformJobAsync(array $args = [])
 * @method \Aws\Result cancelOpenCypherQuery(array $args = [])
 * @method \GuzzleHttp\Promise\Promise cancelOpenCypherQueryAsync(array $args = [])
 * @method \Aws\Result createMLEndpoint(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createMLEndpointAsync(array $args = [])
 * @method \Aws\Result deleteMLEndpoint(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteMLEndpointAsync(array $args = [])
 * @method \Aws\Result deletePropertygraphStatistics(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deletePropertygraphStatisticsAsync(array $args = [])
 * @method \Aws\Result deleteSparqlStatistics(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteSparqlStatisticsAsync(array $args = [])
 * @method \Aws\Result executeFastReset(array $args = [])
 * @method \GuzzleHttp\Promise\Promise executeFastResetAsync(array $args = [])
 * @method \Aws\Result executeGremlinExplainQuery(array $args = [])
 * @method \GuzzleHttp\Promise\Promise executeGremlinExplainQueryAsync(array $args = [])
 * @method \Aws\Result executeGremlinProfileQuery(array $args = [])
 * @method \GuzzleHttp\Promise\Promise executeGremlinProfileQueryAsync(array $args = [])
 * @method \Aws\Result executeGremlinQuery(array $args = [])
 * @method \GuzzleHttp\Promise\Promise executeGremlinQueryAsync(array $args = [])
 * @method \Aws\Result executeOpenCypherExplainQuery(array $args = [])
 * @method \GuzzleHttp\Promise\Promise executeOpenCypherExplainQueryAsync(array $args = [])
 * @method \Aws\Result executeOpenCypherQuery(array $args = [])
 * @method \GuzzleHttp\Promise\Promise executeOpenCypherQueryAsync(array $args = [])
 * @method \Aws\Result getEngineStatus(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getEngineStatusAsync(array $args = [])
 * @method \Aws\Result getGremlinQueryStatus(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getGremlinQueryStatusAsync(array $args = [])
 * @method \Aws\Result getLoaderJobStatus(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getLoaderJobStatusAsync(array $args = [])
 * @method \Aws\Result getMLDataProcessingJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getMLDataProcessingJobAsync(array $args = [])
 * @method \Aws\Result getMLEndpoint(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getMLEndpointAsync(array $args = [])
 * @method \Aws\Result getMLModelTrainingJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getMLModelTrainingJobAsync(array $args = [])
 * @method \Aws\Result getMLModelTransformJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getMLModelTransformJobAsync(array $args = [])
 * @method \Aws\Result getOpenCypherQueryStatus(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getOpenCypherQueryStatusAsync(array $args = [])
 * @method \Aws\Result getPropertygraphStatistics(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getPropertygraphStatisticsAsync(array $args = [])
 * @method \Aws\Result getPropertygraphStream(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getPropertygraphStreamAsync(array $args = [])
 * @method \Aws\Result getPropertygraphSummary(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getPropertygraphSummaryAsync(array $args = [])
 * @method \Aws\Result getRDFGraphSummary(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getRDFGraphSummaryAsync(array $args = [])
 * @method \Aws\Result getSparqlStatistics(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getSparqlStatisticsAsync(array $args = [])
 * @method \Aws\Result getSparqlStream(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getSparqlStreamAsync(array $args = [])
 * @method \Aws\Result listGremlinQueries(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listGremlinQueriesAsync(array $args = [])
 * @method \Aws\Result listLoaderJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listLoaderJobsAsync(array $args = [])
 * @method \Aws\Result listMLDataProcessingJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listMLDataProcessingJobsAsync(array $args = [])
 * @method \Aws\Result listMLEndpoints(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listMLEndpointsAsync(array $args = [])
 * @method \Aws\Result listMLModelTrainingJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listMLModelTrainingJobsAsync(array $args = [])
 * @method \Aws\Result listMLModelTransformJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listMLModelTransformJobsAsync(array $args = [])
 * @method \Aws\Result listOpenCypherQueries(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listOpenCypherQueriesAsync(array $args = [])
 * @method \Aws\Result managePropertygraphStatistics(array $args = [])
 * @method \GuzzleHttp\Promise\Promise managePropertygraphStatisticsAsync(array $args = [])
 * @method \Aws\Result manageSparqlStatistics(array $args = [])
 * @method \GuzzleHttp\Promise\Promise manageSparqlStatisticsAsync(array $args = [])
 * @method \Aws\Result startLoaderJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startLoaderJobAsync(array $args = [])
 * @method \Aws\Result startMLDataProcessingJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startMLDataProcessingJobAsync(array $args = [])
 * @method \Aws\Result startMLModelTrainingJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startMLModelTrainingJobAsync(array $args = [])
 * @method \Aws\Result startMLModelTransformJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startMLModelTransformJobAsync(array $args = [])
 */
class NeptunedataClient extends AwsClient {}
