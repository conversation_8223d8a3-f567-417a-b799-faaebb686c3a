<?php
namespace Aws\PartnerCentralSelling;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Partner Central Selling API** service.
 * @method \Aws\Result acceptEngagementInvitation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise acceptEngagementInvitationAsync(array $args = [])
 * @method \Aws\Result assignOpportunity(array $args = [])
 * @method \GuzzleHttp\Promise\Promise assignOpportunityAsync(array $args = [])
 * @method \Aws\Result associateOpportunity(array $args = [])
 * @method \GuzzleHttp\Promise\Promise associateOpportunityAsync(array $args = [])
 * @method \Aws\Result createEngagement(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createEngagementAsync(array $args = [])
 * @method \Aws\Result createEngagementInvitation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createEngagementInvitationAsync(array $args = [])
 * @method \Aws\Result createOpportunity(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createOpportunityAsync(array $args = [])
 * @method \Aws\Result createResourceSnapshot(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createResourceSnapshotAsync(array $args = [])
 * @method \Aws\Result createResourceSnapshotJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createResourceSnapshotJobAsync(array $args = [])
 * @method \Aws\Result deleteResourceSnapshotJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteResourceSnapshotJobAsync(array $args = [])
 * @method \Aws\Result disassociateOpportunity(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disassociateOpportunityAsync(array $args = [])
 * @method \Aws\Result getAwsOpportunitySummary(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getAwsOpportunitySummaryAsync(array $args = [])
 * @method \Aws\Result getEngagement(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getEngagementAsync(array $args = [])
 * @method \Aws\Result getEngagementInvitation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getEngagementInvitationAsync(array $args = [])
 * @method \Aws\Result getOpportunity(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getOpportunityAsync(array $args = [])
 * @method \Aws\Result getResourceSnapshot(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getResourceSnapshotAsync(array $args = [])
 * @method \Aws\Result getResourceSnapshotJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getResourceSnapshotJobAsync(array $args = [])
 * @method \Aws\Result getSellingSystemSettings(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getSellingSystemSettingsAsync(array $args = [])
 * @method \Aws\Result listEngagementByAcceptingInvitationTasks(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listEngagementByAcceptingInvitationTasksAsync(array $args = [])
 * @method \Aws\Result listEngagementFromOpportunityTasks(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listEngagementFromOpportunityTasksAsync(array $args = [])
 * @method \Aws\Result listEngagementInvitations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listEngagementInvitationsAsync(array $args = [])
 * @method \Aws\Result listEngagementMembers(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listEngagementMembersAsync(array $args = [])
 * @method \Aws\Result listEngagementResourceAssociations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listEngagementResourceAssociationsAsync(array $args = [])
 * @method \Aws\Result listEngagements(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listEngagementsAsync(array $args = [])
 * @method \Aws\Result listOpportunities(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listOpportunitiesAsync(array $args = [])
 * @method \Aws\Result listResourceSnapshotJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listResourceSnapshotJobsAsync(array $args = [])
 * @method \Aws\Result listResourceSnapshots(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listResourceSnapshotsAsync(array $args = [])
 * @method \Aws\Result listSolutions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listSolutionsAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result putSellingSystemSettings(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putSellingSystemSettingsAsync(array $args = [])
 * @method \Aws\Result rejectEngagementInvitation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise rejectEngagementInvitationAsync(array $args = [])
 * @method \Aws\Result startEngagementByAcceptingInvitationTask(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startEngagementByAcceptingInvitationTaskAsync(array $args = [])
 * @method \Aws\Result startEngagementFromOpportunityTask(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startEngagementFromOpportunityTaskAsync(array $args = [])
 * @method \Aws\Result startResourceSnapshotJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startResourceSnapshotJobAsync(array $args = [])
 * @method \Aws\Result stopResourceSnapshotJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise stopResourceSnapshotJobAsync(array $args = [])
 * @method \Aws\Result submitOpportunity(array $args = [])
 * @method \GuzzleHttp\Promise\Promise submitOpportunityAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateOpportunity(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateOpportunityAsync(array $args = [])
 */
class PartnerCentralSellingClient extends AwsClient {}
