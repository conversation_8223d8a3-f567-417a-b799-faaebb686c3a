<?php
namespace Aws\S3Tables;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Amazon S3 Tables** service.
 * @method \Aws\Result createNamespace(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createNamespaceAsync(array $args = [])
 * @method \Aws\Result createTable(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createTableAsync(array $args = [])
 * @method \Aws\Result createTableBucket(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createTableBucketAsync(array $args = [])
 * @method \Aws\Result deleteNamespace(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteNamespaceAsync(array $args = [])
 * @method \Aws\Result deleteTable(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteTableAsync(array $args = [])
 * @method \Aws\Result deleteTableBucket(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteTableBucketAsync(array $args = [])
 * @method \Aws\Result deleteTableBucketPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteTableBucketPolicyAsync(array $args = [])
 * @method \Aws\Result deleteTablePolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteTablePolicyAsync(array $args = [])
 * @method \Aws\Result getNamespace(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getNamespaceAsync(array $args = [])
 * @method \Aws\Result getTable(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getTableAsync(array $args = [])
 * @method \Aws\Result getTableBucket(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getTableBucketAsync(array $args = [])
 * @method \Aws\Result getTableBucketMaintenanceConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getTableBucketMaintenanceConfigurationAsync(array $args = [])
 * @method \Aws\Result getTableBucketPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getTableBucketPolicyAsync(array $args = [])
 * @method \Aws\Result getTableMaintenanceConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getTableMaintenanceConfigurationAsync(array $args = [])
 * @method \Aws\Result getTableMaintenanceJobStatus(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getTableMaintenanceJobStatusAsync(array $args = [])
 * @method \Aws\Result getTableMetadataLocation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getTableMetadataLocationAsync(array $args = [])
 * @method \Aws\Result getTablePolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getTablePolicyAsync(array $args = [])
 * @method \Aws\Result listNamespaces(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listNamespacesAsync(array $args = [])
 * @method \Aws\Result listTableBuckets(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTableBucketsAsync(array $args = [])
 * @method \Aws\Result listTables(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTablesAsync(array $args = [])
 * @method \Aws\Result putTableBucketMaintenanceConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putTableBucketMaintenanceConfigurationAsync(array $args = [])
 * @method \Aws\Result putTableBucketPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putTableBucketPolicyAsync(array $args = [])
 * @method \Aws\Result putTableMaintenanceConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putTableMaintenanceConfigurationAsync(array $args = [])
 * @method \Aws\Result putTablePolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putTablePolicyAsync(array $args = [])
 * @method \Aws\Result renameTable(array $args = [])
 * @method \GuzzleHttp\Promise\Promise renameTableAsync(array $args = [])
 * @method \Aws\Result updateTableMetadataLocation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateTableMetadataLocationAsync(array $args = [])
 */
class S3TablesClient extends AwsClient {}
