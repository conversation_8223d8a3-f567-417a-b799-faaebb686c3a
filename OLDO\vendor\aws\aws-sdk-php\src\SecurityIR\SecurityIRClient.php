<?php
namespace Aws\SecurityIR;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Security Incident Response** service.
 * @method \Aws\Result batchGetMemberAccountDetails(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchGetMemberAccountDetailsAsync(array $args = [])
 * @method \Aws\Result cancelMembership(array $args = [])
 * @method \GuzzleHttp\Promise\Promise cancelMembershipAsync(array $args = [])
 * @method \Aws\Result closeCase(array $args = [])
 * @method \GuzzleHttp\Promise\Promise closeCaseAsync(array $args = [])
 * @method \Aws\Result createCase(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createCaseAsync(array $args = [])
 * @method \Aws\Result createCaseComment(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createCaseCommentAsync(array $args = [])
 * @method \Aws\Result createMembership(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createMembershipAsync(array $args = [])
 * @method \Aws\Result getCase(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getCaseAsync(array $args = [])
 * @method \Aws\Result getCaseAttachmentDownloadUrl(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getCaseAttachmentDownloadUrlAsync(array $args = [])
 * @method \Aws\Result getCaseAttachmentUploadUrl(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getCaseAttachmentUploadUrlAsync(array $args = [])
 * @method \Aws\Result getMembership(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getMembershipAsync(array $args = [])
 * @method \Aws\Result listCaseEdits(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listCaseEditsAsync(array $args = [])
 * @method \Aws\Result listCases(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listCasesAsync(array $args = [])
 * @method \Aws\Result listComments(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listCommentsAsync(array $args = [])
 * @method \Aws\Result listMemberships(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listMembershipsAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateCase(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateCaseAsync(array $args = [])
 * @method \Aws\Result updateCaseComment(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateCaseCommentAsync(array $args = [])
 * @method \Aws\Result updateCaseStatus(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateCaseStatusAsync(array $args = [])
 * @method \Aws\Result updateMembership(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateMembershipAsync(array $args = [])
 * @method \Aws\Result updateResolverType(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateResolverTypeAsync(array $args = [])
 */
class SecurityIRClient extends AwsClient {}
