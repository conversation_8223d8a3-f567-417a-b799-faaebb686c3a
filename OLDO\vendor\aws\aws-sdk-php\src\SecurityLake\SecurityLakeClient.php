<?php
namespace Aws\SecurityLake;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Amazon Security Lake** service.
 * @method \Aws\Result createAwsLogSource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createAwsLogSourceAsync(array $args = [])
 * @method \Aws\Result createCustomLogSource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createCustomLogSourceAsync(array $args = [])
 * @method \Aws\Result createDataLake(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createDataLakeAsync(array $args = [])
 * @method \Aws\Result createDataLakeExceptionSubscription(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createDataLakeExceptionSubscriptionAsync(array $args = [])
 * @method \Aws\Result createDataLakeOrganizationConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createDataLakeOrganizationConfigurationAsync(array $args = [])
 * @method \Aws\Result createSubscriber(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createSubscriberAsync(array $args = [])
 * @method \Aws\Result createSubscriberNotification(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createSubscriberNotificationAsync(array $args = [])
 * @method \Aws\Result deleteAwsLogSource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteAwsLogSourceAsync(array $args = [])
 * @method \Aws\Result deleteCustomLogSource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteCustomLogSourceAsync(array $args = [])
 * @method \Aws\Result deleteDataLake(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteDataLakeAsync(array $args = [])
 * @method \Aws\Result deleteDataLakeExceptionSubscription(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteDataLakeExceptionSubscriptionAsync(array $args = [])
 * @method \Aws\Result deleteDataLakeOrganizationConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteDataLakeOrganizationConfigurationAsync(array $args = [])
 * @method \Aws\Result deleteSubscriber(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteSubscriberAsync(array $args = [])
 * @method \Aws\Result deleteSubscriberNotification(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteSubscriberNotificationAsync(array $args = [])
 * @method \Aws\Result deregisterDataLakeDelegatedAdministrator(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deregisterDataLakeDelegatedAdministratorAsync(array $args = [])
 * @method \Aws\Result getDataLakeExceptionSubscription(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getDataLakeExceptionSubscriptionAsync(array $args = [])
 * @method \Aws\Result getDataLakeOrganizationConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getDataLakeOrganizationConfigurationAsync(array $args = [])
 * @method \Aws\Result getDataLakeSources(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getDataLakeSourcesAsync(array $args = [])
 * @method \Aws\Result getSubscriber(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getSubscriberAsync(array $args = [])
 * @method \Aws\Result listDataLakeExceptions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDataLakeExceptionsAsync(array $args = [])
 * @method \Aws\Result listDataLakes(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDataLakesAsync(array $args = [])
 * @method \Aws\Result listLogSources(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listLogSourcesAsync(array $args = [])
 * @method \Aws\Result listSubscribers(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listSubscribersAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result registerDataLakeDelegatedAdministrator(array $args = [])
 * @method \GuzzleHttp\Promise\Promise registerDataLakeDelegatedAdministratorAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateDataLake(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateDataLakeAsync(array $args = [])
 * @method \Aws\Result updateDataLakeExceptionSubscription(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateDataLakeExceptionSubscriptionAsync(array $args = [])
 * @method \Aws\Result updateSubscriber(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateSubscriberAsync(array $args = [])
 * @method \Aws\Result updateSubscriberNotification(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateSubscriberNotificationAsync(array $args = [])
 */
class SecurityLakeClient extends AwsClient {}
