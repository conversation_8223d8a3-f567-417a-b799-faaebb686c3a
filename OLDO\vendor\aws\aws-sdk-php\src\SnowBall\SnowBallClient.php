<?php
namespace Aws\SnowBall;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Amazon Import/Export Snowball** service.
 * @method \Aws\Result cancelCluster(array $args = [])
 * @method \GuzzleHttp\Promise\Promise cancelClusterAsync(array $args = [])
 * @method \Aws\Result cancelJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise cancelJobAsync(array $args = [])
 * @method \Aws\Result createAddress(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createAddressAsync(array $args = [])
 * @method \Aws\Result createCluster(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createClusterAsync(array $args = [])
 * @method \Aws\Result createJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createJobAsync(array $args = [])
 * @method \Aws\Result createLongTermPricing(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createLongTermPricingAsync(array $args = [])
 * @method \Aws\Result createReturnShippingLabel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createReturnShippingLabelAsync(array $args = [])
 * @method \Aws\Result describeAddress(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeAddressAsync(array $args = [])
 * @method \Aws\Result describeAddresses(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeAddressesAsync(array $args = [])
 * @method \Aws\Result describeCluster(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeClusterAsync(array $args = [])
 * @method \Aws\Result describeJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeJobAsync(array $args = [])
 * @method \Aws\Result describeReturnShippingLabel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeReturnShippingLabelAsync(array $args = [])
 * @method \Aws\Result getJobManifest(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getJobManifestAsync(array $args = [])
 * @method \Aws\Result getJobUnlockCode(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getJobUnlockCodeAsync(array $args = [])
 * @method \Aws\Result getSnowballUsage(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getSnowballUsageAsync(array $args = [])
 * @method \Aws\Result getSoftwareUpdates(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getSoftwareUpdatesAsync(array $args = [])
 * @method \Aws\Result listClusterJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listClusterJobsAsync(array $args = [])
 * @method \Aws\Result listClusters(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listClustersAsync(array $args = [])
 * @method \Aws\Result listCompatibleImages(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listCompatibleImagesAsync(array $args = [])
 * @method \Aws\Result listJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listJobsAsync(array $args = [])
 * @method \Aws\Result listLongTermPricing(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listLongTermPricingAsync(array $args = [])
 * @method \Aws\Result listPickupLocations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listPickupLocationsAsync(array $args = [])
 * @method \Aws\Result listServiceVersions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listServiceVersionsAsync(array $args = [])
 * @method \Aws\Result updateCluster(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateClusterAsync(array $args = [])
 * @method \Aws\Result updateJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateJobAsync(array $args = [])
 * @method \Aws\Result updateJobShipmentState(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateJobShipmentStateAsync(array $args = [])
 * @method \Aws\Result updateLongTermPricing(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateLongTermPricingAsync(array $args = [])
 */
class SnowBallClient extends AwsClient {}
