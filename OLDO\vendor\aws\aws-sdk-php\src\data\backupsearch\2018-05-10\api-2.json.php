<?php
// This file was auto-generated from sdk-root/src/data/backupsearch/2018-05-10/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-05-10', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'backup-search', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'AWS Backup Search', 'serviceId' => 'BackupSearch', 'signatureVersion' => 'v4', 'signingName' => 'backup-search', 'uid' => 'backupsearch-2018-05-10', ], 'operations' => [ 'GetSearchJob' => [ 'name' => 'GetSearchJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/search-jobs/{SearchJobIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSearchJobInput', ], 'output' => [ 'shape' => 'GetSearchJobOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetSearchResultExportJob' => [ 'name' => 'GetSearchResultExportJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/export-search-jobs/{ExportJobIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSearchResultExportJobInput', ], 'output' => [ 'shape' => 'GetSearchResultExportJobOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListSearchJobBackups' => [ 'name' => 'ListSearchJobBackups', 'http' => [ 'method' => 'GET', 'requestUri' => '/search-jobs/{SearchJobIdentifier}/backups', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSearchJobBackupsInput', ], 'output' => [ 'shape' => 'ListSearchJobBackupsOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListSearchJobResults' => [ 'name' => 'ListSearchJobResults', 'http' => [ 'method' => 'GET', 'requestUri' => '/search-jobs/{SearchJobIdentifier}/search-results', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSearchJobResultsInput', ], 'output' => [ 'shape' => 'ListSearchJobResultsOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListSearchJobs' => [ 'name' => 'ListSearchJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/search-jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSearchJobsInput', ], 'output' => [ 'shape' => 'ListSearchJobsOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListSearchResultExportJobs' => [ 'name' => 'ListSearchResultExportJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/export-search-jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSearchResultExportJobsInput', ], 'output' => [ 'shape' => 'ListSearchResultExportJobsOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartSearchJob' => [ 'name' => 'StartSearchJob', 'http' => [ 'method' => 'PUT', 'requestUri' => '/search-jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartSearchJobInput', ], 'output' => [ 'shape' => 'StartSearchJobOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'StartSearchResultExportJob' => [ 'name' => 'StartSearchResultExportJob', 'http' => [ 'method' => 'PUT', 'requestUri' => '/export-search-jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartSearchResultExportJobInput', ], 'output' => [ 'shape' => 'StartSearchResultExportJobOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'StopSearchJob' => [ 'name' => 'StopSearchJob', 'http' => [ 'method' => 'PUT', 'requestUri' => '/search-jobs/{SearchJobIdentifier}/actions/cancel', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopSearchJobInput', ], 'output' => [ 'shape' => 'StopSearchJobOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'BackupCreationTimeFilter' => [ 'type' => 'structure', 'members' => [ 'CreatedAfter' => [ 'shape' => 'Timestamp', ], 'CreatedBefore' => [ 'shape' => 'Timestamp', ], ], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CurrentSearchProgress' => [ 'type' => 'structure', 'members' => [ 'RecoveryPointsScannedCount' => [ 'shape' => 'Integer', ], 'ItemsScannedCount' => [ 'shape' => 'Long', ], 'ItemsMatchedCount' => [ 'shape' => 'Long', ], ], ], 'EBSItemFilter' => [ 'type' => 'structure', 'members' => [ 'FilePaths' => [ 'shape' => 'StringConditionList', ], 'Sizes' => [ 'shape' => 'LongConditionList', ], 'CreationTimes' => [ 'shape' => 'TimeConditionList', ], 'LastModificationTimes' => [ 'shape' => 'TimeConditionList', ], ], ], 'EBSItemFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'EBSItemFilter', ], 'max' => 10, 'min' => 0, ], 'EBSResultItem' => [ 'type' => 'structure', 'members' => [ 'BackupResourceArn' => [ 'shape' => 'String', ], 'SourceResourceArn' => [ 'shape' => 'String', ], 'BackupVaultName' => [ 'shape' => 'String', ], 'FileSystemIdentifier' => [ 'shape' => 'String', ], 'FilePath' => [ 'shape' => 'FilePath', ], 'FileSize' => [ 'shape' => 'Long', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'EncryptionKeyArn' => [ 'type' => 'string', ], 'ExportJobArn' => [ 'type' => 'string', ], 'ExportJobStatus' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'FAILED', 'COMPLETED', ], ], 'ExportJobSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExportJobSummary', ], ], 'ExportJobSummary' => [ 'type' => 'structure', 'required' => [ 'ExportJobIdentifier', ], 'members' => [ 'ExportJobIdentifier' => [ 'shape' => 'GenericId', ], 'ExportJobArn' => [ 'shape' => 'ExportJobArn', ], 'Status' => [ 'shape' => 'ExportJobStatus', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'CompletionTime' => [ 'shape' => 'Timestamp', ], 'StatusMessage' => [ 'shape' => 'String', ], 'SearchJobArn' => [ 'shape' => 'SearchJobArn', ], ], ], 'ExportSpecification' => [ 'type' => 'structure', 'members' => [ 's3ExportSpecification' => [ 'shape' => 'S3ExportSpecification', ], ], 'union' => true, ], 'FilePath' => [ 'type' => 'string', 'sensitive' => true, ], 'GenericId' => [ 'type' => 'string', ], 'GetSearchJobInput' => [ 'type' => 'structure', 'required' => [ 'SearchJobIdentifier', ], 'members' => [ 'SearchJobIdentifier' => [ 'shape' => 'GenericId', 'location' => 'uri', 'locationName' => 'SearchJobIdentifier', ], ], ], 'GetSearchJobOutput' => [ 'type' => 'structure', 'required' => [ 'Status', 'SearchScope', 'ItemFilters', 'CreationTime', 'SearchJobIdentifier', 'SearchJobArn', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'SearchScopeSummary' => [ 'shape' => 'SearchScopeSummary', ], 'CurrentSearchProgress' => [ 'shape' => 'CurrentSearchProgress', ], 'StatusMessage' => [ 'shape' => 'String', ], 'EncryptionKeyArn' => [ 'shape' => 'EncryptionKeyArn', ], 'CompletionTime' => [ 'shape' => 'Timestamp', ], 'Status' => [ 'shape' => 'SearchJobState', ], 'SearchScope' => [ 'shape' => 'SearchScope', ], 'ItemFilters' => [ 'shape' => 'ItemFilters', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'SearchJobIdentifier' => [ 'shape' => 'GenericId', ], 'SearchJobArn' => [ 'shape' => 'SearchJobArn', ], ], ], 'GetSearchResultExportJobInput' => [ 'type' => 'structure', 'required' => [ 'ExportJobIdentifier', ], 'members' => [ 'ExportJobIdentifier' => [ 'shape' => 'GenericId', 'location' => 'uri', 'locationName' => 'ExportJobIdentifier', ], ], ], 'GetSearchResultExportJobOutput' => [ 'type' => 'structure', 'required' => [ 'ExportJobIdentifier', ], 'members' => [ 'ExportJobIdentifier' => [ 'shape' => 'GenericId', ], 'ExportJobArn' => [ 'shape' => 'ExportJobArn', ], 'Status' => [ 'shape' => 'ExportJobStatus', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'CompletionTime' => [ 'shape' => 'Timestamp', ], 'StatusMessage' => [ 'shape' => 'String', ], 'ExportSpecification' => [ 'shape' => 'ExportSpecification', ], 'SearchJobArn' => [ 'shape' => 'SearchJobArn', ], ], ], 'IamRoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:(?:aws|aws-cn|aws-us-gov):iam::[a-z0-9-]+:role/(.+)', ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'ItemFilters' => [ 'type' => 'structure', 'members' => [ 'S3ItemFilters' => [ 'shape' => 'S3ItemFilters', ], 'EBSItemFilters' => [ 'shape' => 'EBSItemFilters', ], ], ], 'ListSearchJobBackupsInput' => [ 'type' => 'structure', 'required' => [ 'SearchJobIdentifier', ], 'members' => [ 'SearchJobIdentifier' => [ 'shape' => 'GenericId', 'location' => 'uri', 'locationName' => 'SearchJobIdentifier', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'ListSearchJobBackupsInputMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListSearchJobBackupsInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'ListSearchJobBackupsOutput' => [ 'type' => 'structure', 'required' => [ 'Results', ], 'members' => [ 'Results' => [ 'shape' => 'SearchJobBackupsResults', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListSearchJobResultsInput' => [ 'type' => 'structure', 'required' => [ 'SearchJobIdentifier', ], 'members' => [ 'SearchJobIdentifier' => [ 'shape' => 'GenericId', 'location' => 'uri', 'locationName' => 'SearchJobIdentifier', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'ListSearchJobResultsInputMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListSearchJobResultsInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'ListSearchJobResultsOutput' => [ 'type' => 'structure', 'required' => [ 'Results', ], 'members' => [ 'Results' => [ 'shape' => 'Results', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListSearchJobsInput' => [ 'type' => 'structure', 'members' => [ 'ByStatus' => [ 'shape' => 'SearchJobState', 'location' => 'querystring', 'locationName' => 'Status', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'ListSearchJobsInputMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListSearchJobsInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'ListSearchJobsOutput' => [ 'type' => 'structure', 'required' => [ 'SearchJobs', ], 'members' => [ 'SearchJobs' => [ 'shape' => 'SearchJobs', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListSearchResultExportJobsInput' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'ExportJobStatus', 'location' => 'querystring', 'locationName' => 'Status', ], 'SearchJobIdentifier' => [ 'shape' => 'GenericId', 'location' => 'querystring', 'locationName' => 'SearchJobIdentifier', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'ListSearchResultExportJobsInputMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListSearchResultExportJobsInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'ListSearchResultExportJobsOutput' => [ 'type' => 'structure', 'required' => [ 'ExportJobs', ], 'members' => [ 'ExportJobs' => [ 'shape' => 'ExportJobSummaries', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'Long' => [ 'type' => 'long', 'box' => true, ], 'LongCondition' => [ 'type' => 'structure', 'required' => [ 'Value', ], 'members' => [ 'Value' => [ 'shape' => 'Long', ], 'Operator' => [ 'shape' => 'LongConditionOperator', ], ], ], 'LongConditionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LongCondition', ], 'max' => 10, 'min' => 1, ], 'LongConditionOperator' => [ 'type' => 'string', 'enum' => [ 'EQUALS_TO', 'NOT_EQUALS_TO', 'LESS_THAN_EQUAL_TO', 'GREATER_THAN_EQUAL_TO', ], ], 'ObjectKey' => [ 'type' => 'string', 'sensitive' => true, ], 'RecoveryPoint' => [ 'type' => 'string', ], 'RecoveryPointArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecoveryPoint', ], 'max' => 50, 'min' => 0, ], 'ResourceArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 50, 'min' => 0, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'S3', 'EBS', ], ], 'ResourceTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceType', ], 'max' => 1, 'min' => 1, ], 'ResultItem' => [ 'type' => 'structure', 'members' => [ 'S3ResultItem' => [ 'shape' => 'S3ResultItem', ], 'EBSResultItem' => [ 'shape' => 'EBSResultItem', ], ], 'union' => true, ], 'Results' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResultItem', ], ], 'S3ExportSpecification' => [ 'type' => 'structure', 'required' => [ 'DestinationBucket', ], 'members' => [ 'DestinationBucket' => [ 'shape' => 'String', ], 'DestinationPrefix' => [ 'shape' => 'String', ], ], ], 'S3ItemFilter' => [ 'type' => 'structure', 'members' => [ 'ObjectKeys' => [ 'shape' => 'StringConditionList', ], 'Sizes' => [ 'shape' => 'LongConditionList', ], 'CreationTimes' => [ 'shape' => 'TimeConditionList', ], 'VersionIds' => [ 'shape' => 'StringConditionList', ], 'ETags' => [ 'shape' => 'StringConditionList', ], ], ], 'S3ItemFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'S3ItemFilter', ], 'max' => 10, 'min' => 0, ], 'S3ResultItem' => [ 'type' => 'structure', 'members' => [ 'BackupResourceArn' => [ 'shape' => 'String', ], 'SourceResourceArn' => [ 'shape' => 'String', ], 'BackupVaultName' => [ 'shape' => 'String', ], 'ObjectKey' => [ 'shape' => 'ObjectKey', ], 'ObjectSize' => [ 'shape' => 'Long', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'ETag' => [ 'shape' => 'String', ], 'VersionId' => [ 'shape' => 'String', ], ], ], 'SearchJobArn' => [ 'type' => 'string', ], 'SearchJobBackupsResult' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'SearchJobState', ], 'StatusMessage' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'BackupResourceArn' => [ 'shape' => 'String', ], 'SourceResourceArn' => [ 'shape' => 'String', ], 'IndexCreationTime' => [ 'shape' => 'Timestamp', ], 'BackupCreationTime' => [ 'shape' => 'Timestamp', ], ], ], 'SearchJobBackupsResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchJobBackupsResult', ], ], 'SearchJobState' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'COMPLETED', 'STOPPING', 'STOPPED', 'FAILED', ], ], 'SearchJobSummary' => [ 'type' => 'structure', 'members' => [ 'SearchJobIdentifier' => [ 'shape' => 'GenericId', ], 'SearchJobArn' => [ 'shape' => 'SearchJobArn', ], 'Name' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'SearchJobState', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'CompletionTime' => [ 'shape' => 'Timestamp', ], 'SearchScopeSummary' => [ 'shape' => 'SearchScopeSummary', ], 'StatusMessage' => [ 'shape' => 'String', ], ], ], 'SearchJobs' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchJobSummary', ], ], 'SearchScope' => [ 'type' => 'structure', 'required' => [ 'BackupResourceTypes', ], 'members' => [ 'BackupResourceTypes' => [ 'shape' => 'ResourceTypeList', ], 'BackupResourceCreationTime' => [ 'shape' => 'BackupCreationTimeFilter', ], 'SourceResourceArns' => [ 'shape' => 'ResourceArnList', ], 'BackupResourceArns' => [ 'shape' => 'RecoveryPointArnList', ], 'BackupResourceTags' => [ 'shape' => 'TagMap', ], ], ], 'SearchScopeSummary' => [ 'type' => 'structure', 'members' => [ 'TotalRecoveryPointsToScanCount' => [ 'shape' => 'Integer', ], 'TotalItemsToScanCount' => [ 'shape' => 'Long', ], ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', 'serviceCode', 'quotaCode', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], 'serviceCode' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'StartSearchJobInput' => [ 'type' => 'structure', 'required' => [ 'SearchScope', ], 'members' => [ 'Tags' => [ 'shape' => 'TagMap', ], 'Name' => [ 'shape' => 'StartSearchJobInputNameString', ], 'EncryptionKeyArn' => [ 'shape' => 'EncryptionKeyArn', ], 'ClientToken' => [ 'shape' => 'String', ], 'SearchScope' => [ 'shape' => 'SearchScope', ], 'ItemFilters' => [ 'shape' => 'ItemFilters', ], ], ], 'StartSearchJobInputNameString' => [ 'type' => 'string', 'max' => 500, 'min' => 0, ], 'StartSearchJobOutput' => [ 'type' => 'structure', 'members' => [ 'SearchJobArn' => [ 'shape' => 'SearchJobArn', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'SearchJobIdentifier' => [ 'shape' => 'GenericId', ], ], ], 'StartSearchResultExportJobInput' => [ 'type' => 'structure', 'required' => [ 'SearchJobIdentifier', 'ExportSpecification', ], 'members' => [ 'SearchJobIdentifier' => [ 'shape' => 'GenericId', ], 'ExportSpecification' => [ 'shape' => 'ExportSpecification', ], 'ClientToken' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagMap', ], 'RoleArn' => [ 'shape' => 'IamRoleArn', ], ], ], 'StartSearchResultExportJobOutput' => [ 'type' => 'structure', 'required' => [ 'ExportJobIdentifier', ], 'members' => [ 'ExportJobArn' => [ 'shape' => 'ExportJobArn', ], 'ExportJobIdentifier' => [ 'shape' => 'GenericId', ], ], ], 'StopSearchJobInput' => [ 'type' => 'structure', 'required' => [ 'SearchJobIdentifier', ], 'members' => [ 'SearchJobIdentifier' => [ 'shape' => 'GenericId', 'location' => 'uri', 'locationName' => 'SearchJobIdentifier', ], ], ], 'StopSearchJobOutput' => [ 'type' => 'structure', 'members' => [], ], 'String' => [ 'type' => 'string', ], 'StringCondition' => [ 'type' => 'structure', 'required' => [ 'Value', ], 'members' => [ 'Value' => [ 'shape' => 'String', ], 'Operator' => [ 'shape' => 'StringConditionOperator', ], ], ], 'StringConditionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StringCondition', ], 'max' => 10, 'min' => 1, ], 'StringConditionOperator' => [ 'type' => 'string', 'enum' => [ 'EQUALS_TO', 'NOT_EQUALS_TO', 'CONTAINS', 'DOES_NOT_CONTAIN', 'BEGINS_WITH', 'ENDS_WITH', 'DOES_NOT_BEGIN_WITH', 'DOES_NOT_END_WITH', ], ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'serviceCode' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'TimeCondition' => [ 'type' => 'structure', 'required' => [ 'Value', ], 'members' => [ 'Value' => [ 'shape' => 'Timestamp', ], 'Operator' => [ 'shape' => 'TimeConditionOperator', ], ], ], 'TimeConditionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TimeCondition', ], 'max' => 10, 'min' => 1, ], 'TimeConditionOperator' => [ 'type' => 'string', 'enum' => [ 'EQUALS_TO', 'NOT_EQUALS_TO', 'LESS_THAN_EQUAL_TO', 'GREATER_THAN_EQUAL_TO', ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeys', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], ],];
