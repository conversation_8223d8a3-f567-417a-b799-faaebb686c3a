<?php
// This file was auto-generated from sdk-root/src/data/timestream-query/2018-11-01/paginators-1.json
return [ 'pagination' => [ 'ListScheduledQueries' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'ScheduledQueries', ], 'ListTagsForResource' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'Tags', ], 'Query' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxRows', 'non_aggregate_keys' => [ 'ColumnInfo', 'QueryId', 'QueryStatus', 'QueryInsightsResponse', ], 'output_token' => 'NextToken', 'result_key' => 'Rows', ], ],];
