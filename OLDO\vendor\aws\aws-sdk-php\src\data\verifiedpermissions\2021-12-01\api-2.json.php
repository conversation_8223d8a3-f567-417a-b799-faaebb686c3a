<?php
// This file was auto-generated from sdk-root/src/data/verifiedpermissions/2021-12-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2021-12-01', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'verifiedpermissions', 'jsonVersion' => '1.0', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceFullName' => 'Amazon Verified Permissions', 'serviceId' => 'VerifiedPermissions', 'signatureVersion' => 'v4', 'signingName' => 'verifiedpermissions', 'targetPrefix' => 'VerifiedPermissions', 'uid' => 'verifiedpermissions-2021-12-01', ], 'operations' => [ 'BatchGetPolicy' => [ 'name' => 'BatchGetPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetPolicyInput', ], 'output' => [ 'shape' => 'BatchGetPolicyOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'BatchIsAuthorized' => [ 'name' => 'BatchIsAuthorized', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchIsAuthorizedInput', ], 'output' => [ 'shape' => 'BatchIsAuthorizedOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'BatchIsAuthorizedWithToken' => [ 'name' => 'BatchIsAuthorizedWithToken', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchIsAuthorizedWithTokenInput', ], 'output' => [ 'shape' => 'BatchIsAuthorizedWithTokenOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateIdentitySource' => [ 'name' => 'CreateIdentitySource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateIdentitySourceInput', ], 'output' => [ 'shape' => 'CreateIdentitySourceOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreatePolicy' => [ 'name' => 'CreatePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreatePolicyInput', ], 'output' => [ 'shape' => 'CreatePolicyOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreatePolicyStore' => [ 'name' => 'CreatePolicyStore', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreatePolicyStoreInput', ], 'output' => [ 'shape' => 'CreatePolicyStoreOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreatePolicyTemplate' => [ 'name' => 'CreatePolicyTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreatePolicyTemplateInput', ], 'output' => [ 'shape' => 'CreatePolicyTemplateOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteIdentitySource' => [ 'name' => 'DeleteIdentitySource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteIdentitySourceInput', ], 'output' => [ 'shape' => 'DeleteIdentitySourceOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeletePolicy' => [ 'name' => 'DeletePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeletePolicyInput', ], 'output' => [ 'shape' => 'DeletePolicyOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeletePolicyStore' => [ 'name' => 'DeletePolicyStore', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeletePolicyStoreInput', ], 'output' => [ 'shape' => 'DeletePolicyStoreOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeletePolicyTemplate' => [ 'name' => 'DeletePolicyTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeletePolicyTemplateInput', ], 'output' => [ 'shape' => 'DeletePolicyTemplateOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'GetIdentitySource' => [ 'name' => 'GetIdentitySource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetIdentitySourceInput', ], 'output' => [ 'shape' => 'GetIdentitySourceOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetPolicy' => [ 'name' => 'GetPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPolicyInput', ], 'output' => [ 'shape' => 'GetPolicyOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetPolicyStore' => [ 'name' => 'GetPolicyStore', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPolicyStoreInput', ], 'output' => [ 'shape' => 'GetPolicyStoreOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetPolicyTemplate' => [ 'name' => 'GetPolicyTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPolicyTemplateInput', ], 'output' => [ 'shape' => 'GetPolicyTemplateOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetSchema' => [ 'name' => 'GetSchema', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSchemaInput', ], 'output' => [ 'shape' => 'GetSchemaOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'IsAuthorized' => [ 'name' => 'IsAuthorized', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'IsAuthorizedInput', ], 'output' => [ 'shape' => 'IsAuthorizedOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'IsAuthorizedWithToken' => [ 'name' => 'IsAuthorizedWithToken', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'IsAuthorizedWithTokenInput', ], 'output' => [ 'shape' => 'IsAuthorizedWithTokenOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListIdentitySources' => [ 'name' => 'ListIdentitySources', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListIdentitySourcesInput', ], 'output' => [ 'shape' => 'ListIdentitySourcesOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListPolicies' => [ 'name' => 'ListPolicies', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListPoliciesInput', ], 'output' => [ 'shape' => 'ListPoliciesOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListPolicyStores' => [ 'name' => 'ListPolicyStores', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListPolicyStoresInput', ], 'output' => [ 'shape' => 'ListPolicyStoresOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListPolicyTemplates' => [ 'name' => 'ListPolicyTemplates', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListPolicyTemplatesInput', ], 'output' => [ 'shape' => 'ListPolicyTemplatesOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'PutSchema' => [ 'name' => 'PutSchema', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutSchemaInput', ], 'output' => [ 'shape' => 'PutSchemaOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UpdateIdentitySource' => [ 'name' => 'UpdateIdentitySource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateIdentitySourceInput', ], 'output' => [ 'shape' => 'UpdateIdentitySourceOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UpdatePolicy' => [ 'name' => 'UpdatePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdatePolicyInput', ], 'output' => [ 'shape' => 'UpdatePolicyOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UpdatePolicyStore' => [ 'name' => 'UpdatePolicyStore', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdatePolicyStoreInput', ], 'output' => [ 'shape' => 'UpdatePolicyStoreOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UpdatePolicyTemplate' => [ 'name' => 'UpdatePolicyTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdatePolicyTemplateInput', ], 'output' => [ 'shape' => 'UpdatePolicyTemplateOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ActionId' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '.*', 'sensitive' => true, ], 'ActionIdentifier' => [ 'type' => 'structure', 'required' => [ 'actionType', 'actionId', ], 'members' => [ 'actionType' => [ 'shape' => 'ActionType', ], 'actionId' => [ 'shape' => 'ActionId', ], ], ], 'ActionIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ActionIdentifier', ], ], 'ActionType' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => 'Action$|^.+::Action', 'sensitive' => true, ], 'AttributeValue' => [ 'type' => 'structure', 'members' => [ 'boolean' => [ 'shape' => 'BooleanAttribute', ], 'entityIdentifier' => [ 'shape' => 'EntityIdentifier', ], 'long' => [ 'shape' => 'LongAttribute', ], 'string' => [ 'shape' => 'StringAttribute', ], 'set' => [ 'shape' => 'SetAttribute', ], 'record' => [ 'shape' => 'RecordAttribute', ], 'ipaddr' => [ 'shape' => 'IpAddr', ], 'decimal' => [ 'shape' => 'Decimal', ], ], 'union' => true, ], 'Audience' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'Audiences' => [ 'type' => 'list', 'member' => [ 'shape' => 'Audience', ], 'max' => 255, 'min' => 1, ], 'BatchGetPolicyErrorCode' => [ 'type' => 'string', 'enum' => [ 'POLICY_STORE_NOT_FOUND', 'POLICY_NOT_FOUND', ], ], 'BatchGetPolicyErrorItem' => [ 'type' => 'structure', 'required' => [ 'code', 'policyStoreId', 'policyId', 'message', ], 'members' => [ 'code' => [ 'shape' => 'BatchGetPolicyErrorCode', ], 'policyStoreId' => [ 'shape' => 'String', ], 'policyId' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], ], 'BatchGetPolicyErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchGetPolicyErrorItem', ], ], 'BatchGetPolicyInput' => [ 'type' => 'structure', 'required' => [ 'requests', ], 'members' => [ 'requests' => [ 'shape' => 'BatchGetPolicyInputList', ], ], ], 'BatchGetPolicyInputItem' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', 'policyId', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'policyId' => [ 'shape' => 'PolicyId', ], ], ], 'BatchGetPolicyInputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchGetPolicyInputItem', ], 'max' => 100, 'min' => 1, ], 'BatchGetPolicyOutput' => [ 'type' => 'structure', 'required' => [ 'results', 'errors', ], 'members' => [ 'results' => [ 'shape' => 'BatchGetPolicyOutputList', ], 'errors' => [ 'shape' => 'BatchGetPolicyErrorList', ], ], ], 'BatchGetPolicyOutputItem' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', 'policyId', 'policyType', 'definition', 'createdDate', 'lastUpdatedDate', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'policyId' => [ 'shape' => 'PolicyId', ], 'policyType' => [ 'shape' => 'PolicyType', ], 'definition' => [ 'shape' => 'PolicyDefinitionDetail', ], 'createdDate' => [ 'shape' => 'TimestampFormat', ], 'lastUpdatedDate' => [ 'shape' => 'TimestampFormat', ], ], ], 'BatchGetPolicyOutputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchGetPolicyOutputItem', ], ], 'BatchIsAuthorizedInput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', 'requests', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'entities' => [ 'shape' => 'EntitiesDefinition', ], 'requests' => [ 'shape' => 'BatchIsAuthorizedInputList', ], ], ], 'BatchIsAuthorizedInputItem' => [ 'type' => 'structure', 'members' => [ 'principal' => [ 'shape' => 'EntityIdentifier', ], 'action' => [ 'shape' => 'ActionIdentifier', ], 'resource' => [ 'shape' => 'EntityIdentifier', ], 'context' => [ 'shape' => 'ContextDefinition', ], ], ], 'BatchIsAuthorizedInputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchIsAuthorizedInputItem', ], 'min' => 1, ], 'BatchIsAuthorizedOutput' => [ 'type' => 'structure', 'required' => [ 'results', ], 'members' => [ 'results' => [ 'shape' => 'BatchIsAuthorizedOutputList', ], ], ], 'BatchIsAuthorizedOutputItem' => [ 'type' => 'structure', 'required' => [ 'request', 'decision', 'determiningPolicies', 'errors', ], 'members' => [ 'request' => [ 'shape' => 'BatchIsAuthorizedInputItem', ], 'decision' => [ 'shape' => 'Decision', ], 'determiningPolicies' => [ 'shape' => 'DeterminingPolicyList', ], 'errors' => [ 'shape' => 'EvaluationErrorList', ], ], ], 'BatchIsAuthorizedOutputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchIsAuthorizedOutputItem', ], ], 'BatchIsAuthorizedWithTokenInput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', 'requests', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'identityToken' => [ 'shape' => 'Token', ], 'accessToken' => [ 'shape' => 'Token', ], 'entities' => [ 'shape' => 'EntitiesDefinition', ], 'requests' => [ 'shape' => 'BatchIsAuthorizedWithTokenInputList', ], ], ], 'BatchIsAuthorizedWithTokenInputItem' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'ActionIdentifier', ], 'resource' => [ 'shape' => 'EntityIdentifier', ], 'context' => [ 'shape' => 'ContextDefinition', ], ], ], 'BatchIsAuthorizedWithTokenInputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchIsAuthorizedWithTokenInputItem', ], 'min' => 1, ], 'BatchIsAuthorizedWithTokenOutput' => [ 'type' => 'structure', 'required' => [ 'results', ], 'members' => [ 'principal' => [ 'shape' => 'EntityIdentifier', ], 'results' => [ 'shape' => 'BatchIsAuthorizedWithTokenOutputList', ], ], ], 'BatchIsAuthorizedWithTokenOutputItem' => [ 'type' => 'structure', 'required' => [ 'request', 'decision', 'determiningPolicies', 'errors', ], 'members' => [ 'request' => [ 'shape' => 'BatchIsAuthorizedWithTokenInputItem', ], 'decision' => [ 'shape' => 'Decision', ], 'determiningPolicies' => [ 'shape' => 'DeterminingPolicyList', ], 'errors' => [ 'shape' => 'EvaluationErrorList', ], ], ], 'BatchIsAuthorizedWithTokenOutputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchIsAuthorizedWithTokenOutputItem', ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'BooleanAttribute' => [ 'type' => 'boolean', 'box' => true, 'sensitive' => true, ], 'CedarJson' => [ 'type' => 'string', 'sensitive' => true, ], 'Claim' => [ 'type' => 'string', 'min' => 1, 'sensitive' => true, ], 'ClientId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '.*', 'sensitive' => true, ], 'ClientIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'ClientId', ], 'max' => 1000, 'min' => 0, ], 'CognitoGroupConfiguration' => [ 'type' => 'structure', 'required' => [ 'groupEntityType', ], 'members' => [ 'groupEntityType' => [ 'shape' => 'GroupEntityType', ], ], ], 'CognitoGroupConfigurationDetail' => [ 'type' => 'structure', 'members' => [ 'groupEntityType' => [ 'shape' => 'GroupEntityType', ], ], ], 'CognitoGroupConfigurationItem' => [ 'type' => 'structure', 'members' => [ 'groupEntityType' => [ 'shape' => 'GroupEntityType', ], ], ], 'CognitoUserPoolConfiguration' => [ 'type' => 'structure', 'required' => [ 'userPoolArn', ], 'members' => [ 'userPoolArn' => [ 'shape' => 'UserPoolArn', ], 'clientIds' => [ 'shape' => 'ClientIds', ], 'groupConfiguration' => [ 'shape' => 'CognitoGroupConfiguration', ], ], ], 'CognitoUserPoolConfigurationDetail' => [ 'type' => 'structure', 'required' => [ 'userPoolArn', 'clientIds', 'issuer', ], 'members' => [ 'userPoolArn' => [ 'shape' => 'UserPoolArn', ], 'clientIds' => [ 'shape' => 'ClientIds', ], 'issuer' => [ 'shape' => 'Issuer', ], 'groupConfiguration' => [ 'shape' => 'CognitoGroupConfigurationDetail', ], ], ], 'CognitoUserPoolConfigurationItem' => [ 'type' => 'structure', 'required' => [ 'userPoolArn', 'clientIds', 'issuer', ], 'members' => [ 'userPoolArn' => [ 'shape' => 'UserPoolArn', ], 'clientIds' => [ 'shape' => 'ClientIds', ], 'issuer' => [ 'shape' => 'Issuer', ], 'groupConfiguration' => [ 'shape' => 'CognitoGroupConfigurationItem', ], ], ], 'Configuration' => [ 'type' => 'structure', 'members' => [ 'cognitoUserPoolConfiguration' => [ 'shape' => 'CognitoUserPoolConfiguration', ], 'openIdConnectConfiguration' => [ 'shape' => 'OpenIdConnectConfiguration', ], ], 'union' => true, ], 'ConfigurationDetail' => [ 'type' => 'structure', 'members' => [ 'cognitoUserPoolConfiguration' => [ 'shape' => 'CognitoUserPoolConfigurationDetail', ], 'openIdConnectConfiguration' => [ 'shape' => 'OpenIdConnectConfigurationDetail', ], ], 'union' => true, ], 'ConfigurationItem' => [ 'type' => 'structure', 'members' => [ 'cognitoUserPoolConfiguration' => [ 'shape' => 'CognitoUserPoolConfigurationItem', ], 'openIdConnectConfiguration' => [ 'shape' => 'OpenIdConnectConfigurationItem', ], ], 'union' => true, ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', 'resources', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resources' => [ 'shape' => 'ResourceConflictList', ], ], 'exception' => true, ], 'ContextDefinition' => [ 'type' => 'structure', 'members' => [ 'contextMap' => [ 'shape' => 'ContextMap', ], 'cedarJson' => [ 'shape' => 'CedarJson', ], ], 'union' => true, ], 'ContextMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'AttributeValue', ], 'sensitive' => true, ], 'CreateIdentitySourceInput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', 'configuration', ], 'members' => [ 'clientToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'configuration' => [ 'shape' => 'Configuration', ], 'principalEntityType' => [ 'shape' => 'PrincipalEntityType', ], ], ], 'CreateIdentitySourceOutput' => [ 'type' => 'structure', 'required' => [ 'createdDate', 'identitySourceId', 'lastUpdatedDate', 'policyStoreId', ], 'members' => [ 'createdDate' => [ 'shape' => 'TimestampFormat', ], 'identitySourceId' => [ 'shape' => 'IdentitySourceId', ], 'lastUpdatedDate' => [ 'shape' => 'TimestampFormat', ], 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], ], ], 'CreatePolicyInput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', 'definition', ], 'members' => [ 'clientToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'definition' => [ 'shape' => 'PolicyDefinition', ], ], ], 'CreatePolicyOutput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', 'policyId', 'policyType', 'createdDate', 'lastUpdatedDate', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'policyId' => [ 'shape' => 'PolicyId', ], 'policyType' => [ 'shape' => 'PolicyType', ], 'principal' => [ 'shape' => 'EntityIdentifier', ], 'resource' => [ 'shape' => 'EntityIdentifier', ], 'actions' => [ 'shape' => 'ActionIdentifierList', ], 'createdDate' => [ 'shape' => 'TimestampFormat', ], 'lastUpdatedDate' => [ 'shape' => 'TimestampFormat', ], 'effect' => [ 'shape' => 'PolicyEffect', ], ], ], 'CreatePolicyStoreInput' => [ 'type' => 'structure', 'required' => [ 'validationSettings', ], 'members' => [ 'clientToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], 'validationSettings' => [ 'shape' => 'ValidationSettings', ], 'description' => [ 'shape' => 'PolicyStoreDescription', ], ], ], 'CreatePolicyStoreOutput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', 'arn', 'createdDate', 'lastUpdatedDate', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'arn' => [ 'shape' => 'ResourceArn', ], 'createdDate' => [ 'shape' => 'TimestampFormat', ], 'lastUpdatedDate' => [ 'shape' => 'TimestampFormat', ], ], ], 'CreatePolicyTemplateInput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', 'statement', ], 'members' => [ 'clientToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'description' => [ 'shape' => 'PolicyTemplateDescription', ], 'statement' => [ 'shape' => 'PolicyStatement', ], ], ], 'CreatePolicyTemplateOutput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', 'policyTemplateId', 'createdDate', 'lastUpdatedDate', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'policyTemplateId' => [ 'shape' => 'PolicyTemplateId', ], 'createdDate' => [ 'shape' => 'TimestampFormat', ], 'lastUpdatedDate' => [ 'shape' => 'TimestampFormat', ], ], ], 'Decimal' => [ 'type' => 'string', 'max' => 23, 'min' => 3, 'pattern' => '-?\\d{1,15}\\.\\d{1,4}', 'sensitive' => true, ], 'Decision' => [ 'type' => 'string', 'enum' => [ 'ALLOW', 'DENY', ], ], 'DeleteIdentitySourceInput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', 'identitySourceId', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'identitySourceId' => [ 'shape' => 'IdentitySourceId', ], ], ], 'DeleteIdentitySourceOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeletePolicyInput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', 'policyId', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'policyId' => [ 'shape' => 'PolicyId', ], ], ], 'DeletePolicyOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeletePolicyStoreInput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], ], ], 'DeletePolicyStoreOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeletePolicyTemplateInput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', 'policyTemplateId', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'policyTemplateId' => [ 'shape' => 'PolicyTemplateId', ], ], ], 'DeletePolicyTemplateOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeterminingPolicyItem' => [ 'type' => 'structure', 'required' => [ 'policyId', ], 'members' => [ 'policyId' => [ 'shape' => 'PolicyId', ], ], ], 'DeterminingPolicyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeterminingPolicyItem', ], ], 'DiscoveryUrl' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => 'https://.*', ], 'EntitiesDefinition' => [ 'type' => 'structure', 'members' => [ 'entityList' => [ 'shape' => 'EntityList', ], 'cedarJson' => [ 'shape' => 'CedarJson', ], ], 'union' => true, ], 'EntityAttributes' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'AttributeValue', ], ], 'EntityId' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '.*', 'sensitive' => true, ], 'EntityIdPrefix' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'sensitive' => true, ], 'EntityIdentifier' => [ 'type' => 'structure', 'required' => [ 'entityType', 'entityId', ], 'members' => [ 'entityType' => [ 'shape' => 'EntityType', ], 'entityId' => [ 'shape' => 'EntityId', ], ], ], 'EntityItem' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'identifier' => [ 'shape' => 'EntityIdentifier', ], 'attributes' => [ 'shape' => 'EntityAttributes', ], 'parents' => [ 'shape' => 'ParentList', ], ], ], 'EntityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EntityItem', ], ], 'EntityReference' => [ 'type' => 'structure', 'members' => [ 'unspecified' => [ 'shape' => 'Boolean', ], 'identifier' => [ 'shape' => 'EntityIdentifier', ], ], 'union' => true, ], 'EntityType' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '.*', 'sensitive' => true, ], 'EvaluationErrorItem' => [ 'type' => 'structure', 'required' => [ 'errorDescription', ], 'members' => [ 'errorDescription' => [ 'shape' => 'String', ], ], 'sensitive' => true, ], 'EvaluationErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EvaluationErrorItem', ], ], 'GetIdentitySourceInput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', 'identitySourceId', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'identitySourceId' => [ 'shape' => 'IdentitySourceId', ], ], ], 'GetIdentitySourceOutput' => [ 'type' => 'structure', 'required' => [ 'createdDate', 'identitySourceId', 'lastUpdatedDate', 'policyStoreId', 'principalEntityType', ], 'members' => [ 'createdDate' => [ 'shape' => 'TimestampFormat', ], 'details' => [ 'shape' => 'IdentitySourceDetails', 'deprecated' => true, 'deprecatedMessage' => 'This attribute has been replaced by configuration.cognitoUserPoolConfiguration', ], 'identitySourceId' => [ 'shape' => 'IdentitySourceId', ], 'lastUpdatedDate' => [ 'shape' => 'TimestampFormat', ], 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'principalEntityType' => [ 'shape' => 'PrincipalEntityType', ], 'configuration' => [ 'shape' => 'ConfigurationDetail', ], ], ], 'GetPolicyInput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', 'policyId', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'policyId' => [ 'shape' => 'PolicyId', ], ], ], 'GetPolicyOutput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', 'policyId', 'policyType', 'definition', 'createdDate', 'lastUpdatedDate', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'policyId' => [ 'shape' => 'PolicyId', ], 'policyType' => [ 'shape' => 'PolicyType', ], 'principal' => [ 'shape' => 'EntityIdentifier', ], 'resource' => [ 'shape' => 'EntityIdentifier', ], 'actions' => [ 'shape' => 'ActionIdentifierList', ], 'definition' => [ 'shape' => 'PolicyDefinitionDetail', ], 'createdDate' => [ 'shape' => 'TimestampFormat', ], 'lastUpdatedDate' => [ 'shape' => 'TimestampFormat', ], 'effect' => [ 'shape' => 'PolicyEffect', ], ], ], 'GetPolicyStoreInput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], ], ], 'GetPolicyStoreOutput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', 'arn', 'validationSettings', 'createdDate', 'lastUpdatedDate', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'arn' => [ 'shape' => 'ResourceArn', ], 'validationSettings' => [ 'shape' => 'ValidationSettings', ], 'createdDate' => [ 'shape' => 'TimestampFormat', ], 'lastUpdatedDate' => [ 'shape' => 'TimestampFormat', ], 'description' => [ 'shape' => 'PolicyStoreDescription', ], ], ], 'GetPolicyTemplateInput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', 'policyTemplateId', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'policyTemplateId' => [ 'shape' => 'PolicyTemplateId', ], ], ], 'GetPolicyTemplateOutput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', 'policyTemplateId', 'statement', 'createdDate', 'lastUpdatedDate', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'policyTemplateId' => [ 'shape' => 'PolicyTemplateId', ], 'description' => [ 'shape' => 'PolicyTemplateDescription', ], 'statement' => [ 'shape' => 'PolicyStatement', ], 'createdDate' => [ 'shape' => 'TimestampFormat', ], 'lastUpdatedDate' => [ 'shape' => 'TimestampFormat', ], ], ], 'GetSchemaInput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], ], ], 'GetSchemaOutput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', 'schema', 'createdDate', 'lastUpdatedDate', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'schema' => [ 'shape' => 'SchemaJson', ], 'createdDate' => [ 'shape' => 'TimestampFormat', ], 'lastUpdatedDate' => [ 'shape' => 'TimestampFormat', ], 'namespaces' => [ 'shape' => 'NamespaceList', ], ], ], 'GroupEntityType' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '([_a-zA-Z][_a-zA-Z0-9]*::)*[_a-zA-Z][_a-zA-Z0-9]*', 'sensitive' => true, ], 'IdempotencyToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9-]*', ], 'IdentitySourceDetails' => [ 'type' => 'structure', 'members' => [ 'clientIds' => [ 'shape' => 'ClientIds', 'deprecated' => true, 'deprecatedMessage' => 'This attribute has been replaced by configuration.cognitoUserPoolConfiguration.clientIds', ], 'userPoolArn' => [ 'shape' => 'UserPoolArn', 'deprecated' => true, 'deprecatedMessage' => 'This attribute has been replaced by configuration.cognitoUserPoolConfiguration.userPoolArn', ], 'discoveryUrl' => [ 'shape' => 'DiscoveryUrl', 'deprecated' => true, 'deprecatedMessage' => 'This attribute has been replaced by configuration.cognitoUserPoolConfiguration.issuer', ], 'openIdIssuer' => [ 'shape' => 'OpenIdIssuer', 'deprecated' => true, 'deprecatedMessage' => 'This attribute has been replaced by configuration', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This shape has been replaced by ConfigurationDetail', ], 'IdentitySourceFilter' => [ 'type' => 'structure', 'members' => [ 'principalEntityType' => [ 'shape' => 'PrincipalEntityType', ], ], ], 'IdentitySourceFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdentitySourceFilter', ], 'max' => 10, 'min' => 0, ], 'IdentitySourceId' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '[a-zA-Z0-9-]*', ], 'IdentitySourceItem' => [ 'type' => 'structure', 'required' => [ 'createdDate', 'identitySourceId', 'lastUpdatedDate', 'policyStoreId', 'principalEntityType', ], 'members' => [ 'createdDate' => [ 'shape' => 'TimestampFormat', ], 'details' => [ 'shape' => 'IdentitySourceItemDetails', 'deprecated' => true, 'deprecatedMessage' => 'This attribute has been replaced by configuration.cognitoUserPoolConfiguration', ], 'identitySourceId' => [ 'shape' => 'IdentitySourceId', ], 'lastUpdatedDate' => [ 'shape' => 'TimestampFormat', ], 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'principalEntityType' => [ 'shape' => 'PrincipalEntityType', ], 'configuration' => [ 'shape' => 'ConfigurationItem', ], ], ], 'IdentitySourceItemDetails' => [ 'type' => 'structure', 'members' => [ 'clientIds' => [ 'shape' => 'ClientIds', 'deprecated' => true, 'deprecatedMessage' => 'This attribute has been replaced by configuration.cognitoUserPoolConfiguration.clientIds', ], 'userPoolArn' => [ 'shape' => 'UserPoolArn', 'deprecated' => true, 'deprecatedMessage' => 'This attribute has been replaced by configuration.cognitoUserPoolConfiguration.userPoolArn', ], 'discoveryUrl' => [ 'shape' => 'DiscoveryUrl', 'deprecated' => true, 'deprecatedMessage' => 'This attribute has been replaced by configuration.cognitoUserPoolConfiguration.issuer', ], 'openIdIssuer' => [ 'shape' => 'OpenIdIssuer', 'deprecated' => true, 'deprecatedMessage' => 'This attribute has been replaced by configuration', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This shape has been replaced by ConfigurationItem', ], 'IdentitySources' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdentitySourceItem', ], ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'IpAddr' => [ 'type' => 'string', 'max' => 44, 'min' => 1, 'pattern' => '[0-9a-fA-F\\.:\\/]*', 'sensitive' => true, ], 'IsAuthorizedInput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'principal' => [ 'shape' => 'EntityIdentifier', ], 'action' => [ 'shape' => 'ActionIdentifier', ], 'resource' => [ 'shape' => 'EntityIdentifier', ], 'context' => [ 'shape' => 'ContextDefinition', ], 'entities' => [ 'shape' => 'EntitiesDefinition', ], ], ], 'IsAuthorizedOutput' => [ 'type' => 'structure', 'required' => [ 'decision', 'determiningPolicies', 'errors', ], 'members' => [ 'decision' => [ 'shape' => 'Decision', ], 'determiningPolicies' => [ 'shape' => 'DeterminingPolicyList', ], 'errors' => [ 'shape' => 'EvaluationErrorList', ], ], ], 'IsAuthorizedWithTokenInput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'identityToken' => [ 'shape' => 'Token', ], 'accessToken' => [ 'shape' => 'Token', ], 'action' => [ 'shape' => 'ActionIdentifier', ], 'resource' => [ 'shape' => 'EntityIdentifier', ], 'context' => [ 'shape' => 'ContextDefinition', ], 'entities' => [ 'shape' => 'EntitiesDefinition', ], ], ], 'IsAuthorizedWithTokenOutput' => [ 'type' => 'structure', 'required' => [ 'decision', 'determiningPolicies', 'errors', ], 'members' => [ 'decision' => [ 'shape' => 'Decision', ], 'determiningPolicies' => [ 'shape' => 'DeterminingPolicyList', ], 'errors' => [ 'shape' => 'EvaluationErrorList', ], 'principal' => [ 'shape' => 'EntityIdentifier', ], ], ], 'Issuer' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => 'https://.*', ], 'ListIdentitySourcesInput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'ListIdentitySourcesMaxResults', ], 'filters' => [ 'shape' => 'IdentitySourceFilters', ], ], ], 'ListIdentitySourcesMaxResults' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'ListIdentitySourcesOutput' => [ 'type' => 'structure', 'required' => [ 'identitySources', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'identitySources' => [ 'shape' => 'IdentitySources', ], ], ], 'ListPoliciesInput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'filter' => [ 'shape' => 'PolicyFilter', ], ], ], 'ListPoliciesOutput' => [ 'type' => 'structure', 'required' => [ 'policies', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'policies' => [ 'shape' => 'PolicyList', ], ], ], 'ListPolicyStoresInput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListPolicyStoresOutput' => [ 'type' => 'structure', 'required' => [ 'policyStores', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'policyStores' => [ 'shape' => 'PolicyStoreList', ], ], ], 'ListPolicyTemplatesInput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListPolicyTemplatesOutput' => [ 'type' => 'structure', 'required' => [ 'policyTemplates', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'policyTemplates' => [ 'shape' => 'PolicyTemplatesList', ], ], ], 'LongAttribute' => [ 'type' => 'long', 'box' => true, 'sensitive' => true, ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'Namespace' => [ 'type' => 'string', 'max' => 100, 'min' => 0, 'pattern' => '.*', 'sensitive' => true, ], 'NamespaceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Namespace', ], ], 'NextToken' => [ 'type' => 'string', 'max' => 8000, 'min' => 1, 'pattern' => '[A-Za-z0-9-_=+/\\.]*', ], 'OpenIdConnectAccessTokenConfiguration' => [ 'type' => 'structure', 'members' => [ 'principalIdClaim' => [ 'shape' => 'Claim', ], 'audiences' => [ 'shape' => 'Audiences', ], ], ], 'OpenIdConnectAccessTokenConfigurationDetail' => [ 'type' => 'structure', 'members' => [ 'principalIdClaim' => [ 'shape' => 'Claim', ], 'audiences' => [ 'shape' => 'Audiences', ], ], ], 'OpenIdConnectAccessTokenConfigurationItem' => [ 'type' => 'structure', 'members' => [ 'principalIdClaim' => [ 'shape' => 'Claim', ], 'audiences' => [ 'shape' => 'Audiences', ], ], ], 'OpenIdConnectConfiguration' => [ 'type' => 'structure', 'required' => [ 'issuer', 'tokenSelection', ], 'members' => [ 'issuer' => [ 'shape' => 'Issuer', ], 'entityIdPrefix' => [ 'shape' => 'EntityIdPrefix', ], 'groupConfiguration' => [ 'shape' => 'OpenIdConnectGroupConfiguration', ], 'tokenSelection' => [ 'shape' => 'OpenIdConnectTokenSelection', ], ], ], 'OpenIdConnectConfigurationDetail' => [ 'type' => 'structure', 'required' => [ 'issuer', 'tokenSelection', ], 'members' => [ 'issuer' => [ 'shape' => 'Issuer', ], 'entityIdPrefix' => [ 'shape' => 'EntityIdPrefix', ], 'groupConfiguration' => [ 'shape' => 'OpenIdConnectGroupConfigurationDetail', ], 'tokenSelection' => [ 'shape' => 'OpenIdConnectTokenSelectionDetail', ], ], ], 'OpenIdConnectConfigurationItem' => [ 'type' => 'structure', 'required' => [ 'issuer', 'tokenSelection', ], 'members' => [ 'issuer' => [ 'shape' => 'Issuer', ], 'entityIdPrefix' => [ 'shape' => 'EntityIdPrefix', ], 'groupConfiguration' => [ 'shape' => 'OpenIdConnectGroupConfigurationItem', ], 'tokenSelection' => [ 'shape' => 'OpenIdConnectTokenSelectionItem', ], ], ], 'OpenIdConnectGroupConfiguration' => [ 'type' => 'structure', 'required' => [ 'groupClaim', 'groupEntityType', ], 'members' => [ 'groupClaim' => [ 'shape' => 'Claim', ], 'groupEntityType' => [ 'shape' => 'GroupEntityType', ], ], ], 'OpenIdConnectGroupConfigurationDetail' => [ 'type' => 'structure', 'required' => [ 'groupClaim', 'groupEntityType', ], 'members' => [ 'groupClaim' => [ 'shape' => 'Claim', ], 'groupEntityType' => [ 'shape' => 'GroupEntityType', ], ], ], 'OpenIdConnectGroupConfigurationItem' => [ 'type' => 'structure', 'required' => [ 'groupClaim', 'groupEntityType', ], 'members' => [ 'groupClaim' => [ 'shape' => 'Claim', ], 'groupEntityType' => [ 'shape' => 'GroupEntityType', ], ], ], 'OpenIdConnectIdentityTokenConfiguration' => [ 'type' => 'structure', 'members' => [ 'principalIdClaim' => [ 'shape' => 'Claim', ], 'clientIds' => [ 'shape' => 'ClientIds', ], ], ], 'OpenIdConnectIdentityTokenConfigurationDetail' => [ 'type' => 'structure', 'members' => [ 'principalIdClaim' => [ 'shape' => 'Claim', ], 'clientIds' => [ 'shape' => 'ClientIds', ], ], ], 'OpenIdConnectIdentityTokenConfigurationItem' => [ 'type' => 'structure', 'members' => [ 'principalIdClaim' => [ 'shape' => 'Claim', ], 'clientIds' => [ 'shape' => 'ClientIds', ], ], ], 'OpenIdConnectTokenSelection' => [ 'type' => 'structure', 'members' => [ 'accessTokenOnly' => [ 'shape' => 'OpenIdConnectAccessTokenConfiguration', ], 'identityTokenOnly' => [ 'shape' => 'OpenIdConnectIdentityTokenConfiguration', ], ], 'union' => true, ], 'OpenIdConnectTokenSelectionDetail' => [ 'type' => 'structure', 'members' => [ 'accessTokenOnly' => [ 'shape' => 'OpenIdConnectAccessTokenConfigurationDetail', ], 'identityTokenOnly' => [ 'shape' => 'OpenIdConnectIdentityTokenConfigurationDetail', ], ], 'union' => true, ], 'OpenIdConnectTokenSelectionItem' => [ 'type' => 'structure', 'members' => [ 'accessTokenOnly' => [ 'shape' => 'OpenIdConnectAccessTokenConfigurationItem', ], 'identityTokenOnly' => [ 'shape' => 'OpenIdConnectIdentityTokenConfigurationItem', ], ], 'union' => true, ], 'OpenIdIssuer' => [ 'type' => 'string', 'enum' => [ 'COGNITO', ], ], 'ParentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EntityIdentifier', ], ], 'PolicyDefinition' => [ 'type' => 'structure', 'members' => [ 'static' => [ 'shape' => 'StaticPolicyDefinition', ], 'templateLinked' => [ 'shape' => 'TemplateLinkedPolicyDefinition', ], ], 'union' => true, ], 'PolicyDefinitionDetail' => [ 'type' => 'structure', 'members' => [ 'static' => [ 'shape' => 'StaticPolicyDefinitionDetail', ], 'templateLinked' => [ 'shape' => 'TemplateLinkedPolicyDefinitionDetail', ], ], 'union' => true, ], 'PolicyDefinitionItem' => [ 'type' => 'structure', 'members' => [ 'static' => [ 'shape' => 'StaticPolicyDefinitionItem', ], 'templateLinked' => [ 'shape' => 'TemplateLinkedPolicyDefinitionItem', ], ], 'union' => true, ], 'PolicyEffect' => [ 'type' => 'string', 'enum' => [ 'Permit', 'Forbid', ], ], 'PolicyFilter' => [ 'type' => 'structure', 'members' => [ 'principal' => [ 'shape' => 'EntityReference', ], 'resource' => [ 'shape' => 'EntityReference', ], 'policyType' => [ 'shape' => 'PolicyType', ], 'policyTemplateId' => [ 'shape' => 'PolicyTemplateId', ], ], ], 'PolicyId' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '[a-zA-Z0-9-]*', ], 'PolicyItem' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', 'policyId', 'policyType', 'definition', 'createdDate', 'lastUpdatedDate', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'policyId' => [ 'shape' => 'PolicyId', ], 'policyType' => [ 'shape' => 'PolicyType', ], 'principal' => [ 'shape' => 'EntityIdentifier', ], 'resource' => [ 'shape' => 'EntityIdentifier', ], 'actions' => [ 'shape' => 'ActionIdentifierList', ], 'definition' => [ 'shape' => 'PolicyDefinitionItem', ], 'createdDate' => [ 'shape' => 'TimestampFormat', ], 'lastUpdatedDate' => [ 'shape' => 'TimestampFormat', ], 'effect' => [ 'shape' => 'PolicyEffect', ], ], ], 'PolicyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PolicyItem', ], ], 'PolicyStatement' => [ 'type' => 'string', 'max' => 10000, 'min' => 1, 'sensitive' => true, ], 'PolicyStoreDescription' => [ 'type' => 'string', 'max' => 150, 'min' => 0, 'sensitive' => true, ], 'PolicyStoreId' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '[a-zA-Z0-9-]*', ], 'PolicyStoreItem' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', 'arn', 'createdDate', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'arn' => [ 'shape' => 'ResourceArn', ], 'createdDate' => [ 'shape' => 'TimestampFormat', ], 'lastUpdatedDate' => [ 'shape' => 'TimestampFormat', ], 'description' => [ 'shape' => 'PolicyStoreDescription', ], ], ], 'PolicyStoreList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PolicyStoreItem', ], ], 'PolicyTemplateDescription' => [ 'type' => 'string', 'max' => 150, 'min' => 0, 'sensitive' => true, ], 'PolicyTemplateId' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '[a-zA-Z0-9-]*', ], 'PolicyTemplateItem' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', 'policyTemplateId', 'createdDate', 'lastUpdatedDate', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'policyTemplateId' => [ 'shape' => 'PolicyTemplateId', ], 'description' => [ 'shape' => 'PolicyTemplateDescription', ], 'createdDate' => [ 'shape' => 'TimestampFormat', ], 'lastUpdatedDate' => [ 'shape' => 'TimestampFormat', ], ], ], 'PolicyTemplatesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PolicyTemplateItem', ], ], 'PolicyType' => [ 'type' => 'string', 'enum' => [ 'STATIC', 'TEMPLATE_LINKED', ], ], 'PrincipalEntityType' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '.*', 'sensitive' => true, ], 'PutSchemaInput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', 'definition', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'definition' => [ 'shape' => 'SchemaDefinition', ], ], ], 'PutSchemaOutput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', 'namespaces', 'createdDate', 'lastUpdatedDate', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'namespaces' => [ 'shape' => 'NamespaceList', ], 'createdDate' => [ 'shape' => 'TimestampFormat', ], 'lastUpdatedDate' => [ 'shape' => 'TimestampFormat', ], ], ], 'RecordAttribute' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'AttributeValue', ], ], 'ResourceArn' => [ 'type' => 'string', 'max' => 2500, 'min' => 1, 'pattern' => 'arn:[^:]*:[^:]*:[^:]*:[^:]*:.*', ], 'ResourceConflict' => [ 'type' => 'structure', 'required' => [ 'resourceId', 'resourceType', ], 'members' => [ 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'ResourceType', ], ], ], 'ResourceConflictList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceConflict', ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'ResourceType', ], ], 'exception' => true, ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'IDENTITY_SOURCE', 'POLICY_STORE', 'POLICY', 'POLICY_TEMPLATE', 'SCHEMA', ], ], 'SchemaDefinition' => [ 'type' => 'structure', 'members' => [ 'cedarJson' => [ 'shape' => 'SchemaJson', ], ], 'union' => true, ], 'SchemaJson' => [ 'type' => 'string', 'min' => 1, 'sensitive' => true, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'serviceCode' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'SetAttribute' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttributeValue', ], ], 'StaticPolicyDefinition' => [ 'type' => 'structure', 'required' => [ 'statement', ], 'members' => [ 'description' => [ 'shape' => 'StaticPolicyDescription', ], 'statement' => [ 'shape' => 'PolicyStatement', ], ], ], 'StaticPolicyDefinitionDetail' => [ 'type' => 'structure', 'required' => [ 'statement', ], 'members' => [ 'description' => [ 'shape' => 'StaticPolicyDescription', ], 'statement' => [ 'shape' => 'PolicyStatement', ], ], ], 'StaticPolicyDefinitionItem' => [ 'type' => 'structure', 'members' => [ 'description' => [ 'shape' => 'StaticPolicyDescription', ], ], ], 'StaticPolicyDescription' => [ 'type' => 'string', 'max' => 150, 'min' => 0, 'sensitive' => true, ], 'String' => [ 'type' => 'string', ], 'StringAttribute' => [ 'type' => 'string', 'sensitive' => true, ], 'TemplateLinkedPolicyDefinition' => [ 'type' => 'structure', 'required' => [ 'policyTemplateId', ], 'members' => [ 'policyTemplateId' => [ 'shape' => 'PolicyTemplateId', ], 'principal' => [ 'shape' => 'EntityIdentifier', ], 'resource' => [ 'shape' => 'EntityIdentifier', ], ], ], 'TemplateLinkedPolicyDefinitionDetail' => [ 'type' => 'structure', 'required' => [ 'policyTemplateId', ], 'members' => [ 'policyTemplateId' => [ 'shape' => 'PolicyTemplateId', ], 'principal' => [ 'shape' => 'EntityIdentifier', ], 'resource' => [ 'shape' => 'EntityIdentifier', ], ], ], 'TemplateLinkedPolicyDefinitionItem' => [ 'type' => 'structure', 'required' => [ 'policyTemplateId', ], 'members' => [ 'policyTemplateId' => [ 'shape' => 'PolicyTemplateId', ], 'principal' => [ 'shape' => 'EntityIdentifier', ], 'resource' => [ 'shape' => 'EntityIdentifier', ], ], ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'serviceCode' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'TimestampFormat' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'Token' => [ 'type' => 'string', 'max' => 131072, 'min' => 1, 'pattern' => '[A-Za-z0-9-_=]+.[A-Za-z0-9-_=]+.[A-Za-z0-9-_=]+', 'sensitive' => true, ], 'UpdateCognitoGroupConfiguration' => [ 'type' => 'structure', 'required' => [ 'groupEntityType', ], 'members' => [ 'groupEntityType' => [ 'shape' => 'GroupEntityType', ], ], ], 'UpdateCognitoUserPoolConfiguration' => [ 'type' => 'structure', 'required' => [ 'userPoolArn', ], 'members' => [ 'userPoolArn' => [ 'shape' => 'UserPoolArn', ], 'clientIds' => [ 'shape' => 'ClientIds', ], 'groupConfiguration' => [ 'shape' => 'UpdateCognitoGroupConfiguration', ], ], ], 'UpdateConfiguration' => [ 'type' => 'structure', 'members' => [ 'cognitoUserPoolConfiguration' => [ 'shape' => 'UpdateCognitoUserPoolConfiguration', ], 'openIdConnectConfiguration' => [ 'shape' => 'UpdateOpenIdConnectConfiguration', ], ], 'union' => true, ], 'UpdateIdentitySourceInput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', 'identitySourceId', 'updateConfiguration', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'identitySourceId' => [ 'shape' => 'IdentitySourceId', ], 'updateConfiguration' => [ 'shape' => 'UpdateConfiguration', ], 'principalEntityType' => [ 'shape' => 'PrincipalEntityType', ], ], ], 'UpdateIdentitySourceOutput' => [ 'type' => 'structure', 'required' => [ 'createdDate', 'identitySourceId', 'lastUpdatedDate', 'policyStoreId', ], 'members' => [ 'createdDate' => [ 'shape' => 'TimestampFormat', ], 'identitySourceId' => [ 'shape' => 'IdentitySourceId', ], 'lastUpdatedDate' => [ 'shape' => 'TimestampFormat', ], 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], ], ], 'UpdateOpenIdConnectAccessTokenConfiguration' => [ 'type' => 'structure', 'members' => [ 'principalIdClaim' => [ 'shape' => 'Claim', ], 'audiences' => [ 'shape' => 'Audiences', ], ], ], 'UpdateOpenIdConnectConfiguration' => [ 'type' => 'structure', 'required' => [ 'issuer', 'tokenSelection', ], 'members' => [ 'issuer' => [ 'shape' => 'Issuer', ], 'entityIdPrefix' => [ 'shape' => 'EntityIdPrefix', ], 'groupConfiguration' => [ 'shape' => 'UpdateOpenIdConnectGroupConfiguration', ], 'tokenSelection' => [ 'shape' => 'UpdateOpenIdConnectTokenSelection', ], ], ], 'UpdateOpenIdConnectGroupConfiguration' => [ 'type' => 'structure', 'required' => [ 'groupClaim', 'groupEntityType', ], 'members' => [ 'groupClaim' => [ 'shape' => 'Claim', ], 'groupEntityType' => [ 'shape' => 'GroupEntityType', ], ], ], 'UpdateOpenIdConnectIdentityTokenConfiguration' => [ 'type' => 'structure', 'members' => [ 'principalIdClaim' => [ 'shape' => 'Claim', ], 'clientIds' => [ 'shape' => 'ClientIds', ], ], ], 'UpdateOpenIdConnectTokenSelection' => [ 'type' => 'structure', 'members' => [ 'accessTokenOnly' => [ 'shape' => 'UpdateOpenIdConnectAccessTokenConfiguration', ], 'identityTokenOnly' => [ 'shape' => 'UpdateOpenIdConnectIdentityTokenConfiguration', ], ], 'union' => true, ], 'UpdatePolicyDefinition' => [ 'type' => 'structure', 'members' => [ 'static' => [ 'shape' => 'UpdateStaticPolicyDefinition', ], ], 'union' => true, ], 'UpdatePolicyInput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', 'policyId', 'definition', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'policyId' => [ 'shape' => 'PolicyId', ], 'definition' => [ 'shape' => 'UpdatePolicyDefinition', ], ], ], 'UpdatePolicyOutput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', 'policyId', 'policyType', 'createdDate', 'lastUpdatedDate', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'policyId' => [ 'shape' => 'PolicyId', ], 'policyType' => [ 'shape' => 'PolicyType', ], 'principal' => [ 'shape' => 'EntityIdentifier', ], 'resource' => [ 'shape' => 'EntityIdentifier', ], 'actions' => [ 'shape' => 'ActionIdentifierList', ], 'createdDate' => [ 'shape' => 'TimestampFormat', ], 'lastUpdatedDate' => [ 'shape' => 'TimestampFormat', ], 'effect' => [ 'shape' => 'PolicyEffect', ], ], ], 'UpdatePolicyStoreInput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', 'validationSettings', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'validationSettings' => [ 'shape' => 'ValidationSettings', ], 'description' => [ 'shape' => 'PolicyStoreDescription', ], ], ], 'UpdatePolicyStoreOutput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', 'arn', 'createdDate', 'lastUpdatedDate', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'arn' => [ 'shape' => 'ResourceArn', ], 'createdDate' => [ 'shape' => 'TimestampFormat', ], 'lastUpdatedDate' => [ 'shape' => 'TimestampFormat', ], ], ], 'UpdatePolicyTemplateInput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', 'policyTemplateId', 'statement', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'policyTemplateId' => [ 'shape' => 'PolicyTemplateId', ], 'description' => [ 'shape' => 'PolicyTemplateDescription', ], 'statement' => [ 'shape' => 'PolicyStatement', ], ], ], 'UpdatePolicyTemplateOutput' => [ 'type' => 'structure', 'required' => [ 'policyStoreId', 'policyTemplateId', 'createdDate', 'lastUpdatedDate', ], 'members' => [ 'policyStoreId' => [ 'shape' => 'PolicyStoreId', ], 'policyTemplateId' => [ 'shape' => 'PolicyTemplateId', ], 'createdDate' => [ 'shape' => 'TimestampFormat', ], 'lastUpdatedDate' => [ 'shape' => 'TimestampFormat', ], ], ], 'UpdateStaticPolicyDefinition' => [ 'type' => 'structure', 'required' => [ 'statement', ], 'members' => [ 'description' => [ 'shape' => 'StaticPolicyDescription', ], 'statement' => [ 'shape' => 'PolicyStatement', ], ], ], 'UserPoolArn' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => 'arn:[a-zA-Z0-9-]+:cognito-idp:(([a-zA-Z0-9-]+:\\d{12}:userpool/[\\w-]+_[0-9a-zA-Z]+))', ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'path', 'message', ], 'members' => [ 'path' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationMode' => [ 'type' => 'string', 'enum' => [ 'OFF', 'STRICT', ], ], 'ValidationSettings' => [ 'type' => 'structure', 'required' => [ 'mode', ], 'members' => [ 'mode' => [ 'shape' => 'ValidationMode', ], ], ], ],];
