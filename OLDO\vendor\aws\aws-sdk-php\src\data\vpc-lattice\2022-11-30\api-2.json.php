<?php
// This file was auto-generated from sdk-root/src/data/vpc-lattice/2022-11-30/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2022-11-30', 'endpointPrefix' => 'vpc-lattice', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'Amazon VPC Lattice', 'serviceId' => 'VPC Lattice', 'signatureVersion' => 'v4', 'signingName' => 'vpc-lattice', 'uid' => 'vpc-lattice-2022-11-30', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'BatchUpdateRule' => [ 'name' => 'BatchUpdateRule', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/services/{serviceIdentifier}/listeners/{listenerIdentifier}/rules', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchUpdateRuleRequest', ], 'output' => [ 'shape' => 'BatchUpdateRuleResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateAccessLogSubscription' => [ 'name' => 'CreateAccessLogSubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/accesslogsubscriptions', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateAccessLogSubscriptionRequest', ], 'output' => [ 'shape' => 'CreateAccessLogSubscriptionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateListener' => [ 'name' => 'CreateListener', 'http' => [ 'method' => 'POST', 'requestUri' => '/services/{serviceIdentifier}/listeners', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateListenerRequest', ], 'output' => [ 'shape' => 'CreateListenerResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateResourceConfiguration' => [ 'name' => 'CreateResourceConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/resourceconfigurations', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateResourceConfigurationRequest', ], 'output' => [ 'shape' => 'CreateResourceConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateResourceGateway' => [ 'name' => 'CreateResourceGateway', 'http' => [ 'method' => 'POST', 'requestUri' => '/resourcegateways', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateResourceGatewayRequest', ], 'output' => [ 'shape' => 'CreateResourceGatewayResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateRule' => [ 'name' => 'CreateRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/services/{serviceIdentifier}/listeners/{listenerIdentifier}/rules', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateRuleRequest', ], 'output' => [ 'shape' => 'CreateRuleResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateService' => [ 'name' => 'CreateService', 'http' => [ 'method' => 'POST', 'requestUri' => '/services', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateServiceRequest', ], 'output' => [ 'shape' => 'CreateServiceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateServiceNetwork' => [ 'name' => 'CreateServiceNetwork', 'http' => [ 'method' => 'POST', 'requestUri' => '/servicenetworks', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateServiceNetworkRequest', ], 'output' => [ 'shape' => 'CreateServiceNetworkResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateServiceNetworkResourceAssociation' => [ 'name' => 'CreateServiceNetworkResourceAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/servicenetworkresourceassociations', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateServiceNetworkResourceAssociationRequest', ], 'output' => [ 'shape' => 'CreateServiceNetworkResourceAssociationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateServiceNetworkServiceAssociation' => [ 'name' => 'CreateServiceNetworkServiceAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/servicenetworkserviceassociations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateServiceNetworkServiceAssociationRequest', ], 'output' => [ 'shape' => 'CreateServiceNetworkServiceAssociationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateServiceNetworkVpcAssociation' => [ 'name' => 'CreateServiceNetworkVpcAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/servicenetworkvpcassociations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateServiceNetworkVpcAssociationRequest', ], 'output' => [ 'shape' => 'CreateServiceNetworkVpcAssociationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateTargetGroup' => [ 'name' => 'CreateTargetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/targetgroups', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateTargetGroupRequest', ], 'output' => [ 'shape' => 'CreateTargetGroupResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteAccessLogSubscription' => [ 'name' => 'DeleteAccessLogSubscription', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/accesslogsubscriptions/{accessLogSubscriptionIdentifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteAccessLogSubscriptionRequest', ], 'output' => [ 'shape' => 'DeleteAccessLogSubscriptionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteAuthPolicy' => [ 'name' => 'DeleteAuthPolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/authpolicy/{resourceIdentifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteAuthPolicyRequest', ], 'output' => [ 'shape' => 'DeleteAuthPolicyResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteListener' => [ 'name' => 'DeleteListener', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/services/{serviceIdentifier}/listeners/{listenerIdentifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteListenerRequest', ], 'output' => [ 'shape' => 'DeleteListenerResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteResourceConfiguration' => [ 'name' => 'DeleteResourceConfiguration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/resourceconfigurations/{resourceConfigurationIdentifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteResourceConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteResourceConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteResourceEndpointAssociation' => [ 'name' => 'DeleteResourceEndpointAssociation', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/resourceendpointassociations/{resourceEndpointAssociationIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteResourceEndpointAssociationRequest', ], 'output' => [ 'shape' => 'DeleteResourceEndpointAssociationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteResourceGateway' => [ 'name' => 'DeleteResourceGateway', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/resourcegateways/{resourceGatewayIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteResourceGatewayRequest', ], 'output' => [ 'shape' => 'DeleteResourceGatewayResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteResourcePolicy' => [ 'name' => 'DeleteResourcePolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/resourcepolicy/{resourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteResourcePolicyRequest', ], 'output' => [ 'shape' => 'DeleteResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteRule' => [ 'name' => 'DeleteRule', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/services/{serviceIdentifier}/listeners/{listenerIdentifier}/rules/{ruleIdentifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteRuleRequest', ], 'output' => [ 'shape' => 'DeleteRuleResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteService' => [ 'name' => 'DeleteService', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/services/{serviceIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteServiceRequest', ], 'output' => [ 'shape' => 'DeleteServiceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteServiceNetwork' => [ 'name' => 'DeleteServiceNetwork', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/servicenetworks/{serviceNetworkIdentifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteServiceNetworkRequest', ], 'output' => [ 'shape' => 'DeleteServiceNetworkResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteServiceNetworkResourceAssociation' => [ 'name' => 'DeleteServiceNetworkResourceAssociation', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/servicenetworkresourceassociations/{serviceNetworkResourceAssociationIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteServiceNetworkResourceAssociationRequest', ], 'output' => [ 'shape' => 'DeleteServiceNetworkResourceAssociationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteServiceNetworkServiceAssociation' => [ 'name' => 'DeleteServiceNetworkServiceAssociation', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/servicenetworkserviceassociations/{serviceNetworkServiceAssociationIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteServiceNetworkServiceAssociationRequest', ], 'output' => [ 'shape' => 'DeleteServiceNetworkServiceAssociationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteServiceNetworkVpcAssociation' => [ 'name' => 'DeleteServiceNetworkVpcAssociation', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/servicenetworkvpcassociations/{serviceNetworkVpcAssociationIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteServiceNetworkVpcAssociationRequest', ], 'output' => [ 'shape' => 'DeleteServiceNetworkVpcAssociationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteTargetGroup' => [ 'name' => 'DeleteTargetGroup', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/targetgroups/{targetGroupIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteTargetGroupRequest', ], 'output' => [ 'shape' => 'DeleteTargetGroupResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeregisterTargets' => [ 'name' => 'DeregisterTargets', 'http' => [ 'method' => 'POST', 'requestUri' => '/targetgroups/{targetGroupIdentifier}/deregistertargets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeregisterTargetsRequest', ], 'output' => [ 'shape' => 'DeregisterTargetsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'GetAccessLogSubscription' => [ 'name' => 'GetAccessLogSubscription', 'http' => [ 'method' => 'GET', 'requestUri' => '/accesslogsubscriptions/{accessLogSubscriptionIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAccessLogSubscriptionRequest', ], 'output' => [ 'shape' => 'GetAccessLogSubscriptionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetAuthPolicy' => [ 'name' => 'GetAuthPolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/authpolicy/{resourceIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAuthPolicyRequest', ], 'output' => [ 'shape' => 'GetAuthPolicyResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetListener' => [ 'name' => 'GetListener', 'http' => [ 'method' => 'GET', 'requestUri' => '/services/{serviceIdentifier}/listeners/{listenerIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetListenerRequest', ], 'output' => [ 'shape' => 'GetListenerResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetResourceConfiguration' => [ 'name' => 'GetResourceConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/resourceconfigurations/{resourceConfigurationIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetResourceConfigurationRequest', ], 'output' => [ 'shape' => 'GetResourceConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetResourceGateway' => [ 'name' => 'GetResourceGateway', 'http' => [ 'method' => 'GET', 'requestUri' => '/resourcegateways/{resourceGatewayIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetResourceGatewayRequest', ], 'output' => [ 'shape' => 'GetResourceGatewayResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetResourcePolicy' => [ 'name' => 'GetResourcePolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/resourcepolicy/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetResourcePolicyRequest', ], 'output' => [ 'shape' => 'GetResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetRule' => [ 'name' => 'GetRule', 'http' => [ 'method' => 'GET', 'requestUri' => '/services/{serviceIdentifier}/listeners/{listenerIdentifier}/rules/{ruleIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetRuleRequest', ], 'output' => [ 'shape' => 'GetRuleResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetService' => [ 'name' => 'GetService', 'http' => [ 'method' => 'GET', 'requestUri' => '/services/{serviceIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetServiceRequest', ], 'output' => [ 'shape' => 'GetServiceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetServiceNetwork' => [ 'name' => 'GetServiceNetwork', 'http' => [ 'method' => 'GET', 'requestUri' => '/servicenetworks/{serviceNetworkIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetServiceNetworkRequest', ], 'output' => [ 'shape' => 'GetServiceNetworkResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetServiceNetworkResourceAssociation' => [ 'name' => 'GetServiceNetworkResourceAssociation', 'http' => [ 'method' => 'GET', 'requestUri' => '/servicenetworkresourceassociations/{serviceNetworkResourceAssociationIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetServiceNetworkResourceAssociationRequest', ], 'output' => [ 'shape' => 'GetServiceNetworkResourceAssociationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetServiceNetworkServiceAssociation' => [ 'name' => 'GetServiceNetworkServiceAssociation', 'http' => [ 'method' => 'GET', 'requestUri' => '/servicenetworkserviceassociations/{serviceNetworkServiceAssociationIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetServiceNetworkServiceAssociationRequest', ], 'output' => [ 'shape' => 'GetServiceNetworkServiceAssociationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetServiceNetworkVpcAssociation' => [ 'name' => 'GetServiceNetworkVpcAssociation', 'http' => [ 'method' => 'GET', 'requestUri' => '/servicenetworkvpcassociations/{serviceNetworkVpcAssociationIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetServiceNetworkVpcAssociationRequest', ], 'output' => [ 'shape' => 'GetServiceNetworkVpcAssociationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetTargetGroup' => [ 'name' => 'GetTargetGroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/targetgroups/{targetGroupIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTargetGroupRequest', ], 'output' => [ 'shape' => 'GetTargetGroupResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListAccessLogSubscriptions' => [ 'name' => 'ListAccessLogSubscriptions', 'http' => [ 'method' => 'GET', 'requestUri' => '/accesslogsubscriptions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAccessLogSubscriptionsRequest', ], 'output' => [ 'shape' => 'ListAccessLogSubscriptionsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListListeners' => [ 'name' => 'ListListeners', 'http' => [ 'method' => 'GET', 'requestUri' => '/services/{serviceIdentifier}/listeners', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListListenersRequest', ], 'output' => [ 'shape' => 'ListListenersResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListResourceConfigurations' => [ 'name' => 'ListResourceConfigurations', 'http' => [ 'method' => 'GET', 'requestUri' => '/resourceconfigurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListResourceConfigurationsRequest', ], 'output' => [ 'shape' => 'ListResourceConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListResourceEndpointAssociations' => [ 'name' => 'ListResourceEndpointAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/resourceendpointassociations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListResourceEndpointAssociationsRequest', ], 'output' => [ 'shape' => 'ListResourceEndpointAssociationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListResourceGateways' => [ 'name' => 'ListResourceGateways', 'http' => [ 'method' => 'GET', 'requestUri' => '/resourcegateways', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListResourceGatewaysRequest', ], 'output' => [ 'shape' => 'ListResourceGatewaysResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListRules' => [ 'name' => 'ListRules', 'http' => [ 'method' => 'GET', 'requestUri' => '/services/{serviceIdentifier}/listeners/{listenerIdentifier}/rules', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListRulesRequest', ], 'output' => [ 'shape' => 'ListRulesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListServiceNetworkResourceAssociations' => [ 'name' => 'ListServiceNetworkResourceAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/servicenetworkresourceassociations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListServiceNetworkResourceAssociationsRequest', ], 'output' => [ 'shape' => 'ListServiceNetworkResourceAssociationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListServiceNetworkServiceAssociations' => [ 'name' => 'ListServiceNetworkServiceAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/servicenetworkserviceassociations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListServiceNetworkServiceAssociationsRequest', ], 'output' => [ 'shape' => 'ListServiceNetworkServiceAssociationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListServiceNetworkVpcAssociations' => [ 'name' => 'ListServiceNetworkVpcAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/servicenetworkvpcassociations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListServiceNetworkVpcAssociationsRequest', ], 'output' => [ 'shape' => 'ListServiceNetworkVpcAssociationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListServiceNetworkVpcEndpointAssociations' => [ 'name' => 'ListServiceNetworkVpcEndpointAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/servicenetworkvpcendpointassociations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListServiceNetworkVpcEndpointAssociationsRequest', ], 'output' => [ 'shape' => 'ListServiceNetworkVpcEndpointAssociationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListServiceNetworks' => [ 'name' => 'ListServiceNetworks', 'http' => [ 'method' => 'GET', 'requestUri' => '/servicenetworks', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListServiceNetworksRequest', ], 'output' => [ 'shape' => 'ListServiceNetworksResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListServices' => [ 'name' => 'ListServices', 'http' => [ 'method' => 'GET', 'requestUri' => '/services', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListServicesRequest', ], 'output' => [ 'shape' => 'ListServicesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTargetGroups' => [ 'name' => 'ListTargetGroups', 'http' => [ 'method' => 'GET', 'requestUri' => '/targetgroups', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTargetGroupsRequest', ], 'output' => [ 'shape' => 'ListTargetGroupsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTargets' => [ 'name' => 'ListTargets', 'http' => [ 'method' => 'POST', 'requestUri' => '/targetgroups/{targetGroupIdentifier}/listtargets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTargetsRequest', ], 'output' => [ 'shape' => 'ListTargetsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'PutAuthPolicy' => [ 'name' => 'PutAuthPolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/authpolicy/{resourceIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutAuthPolicyRequest', ], 'output' => [ 'shape' => 'PutAuthPolicyResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'PutResourcePolicy' => [ 'name' => 'PutResourcePolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/resourcepolicy/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutResourcePolicyRequest', ], 'output' => [ 'shape' => 'PutResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'RegisterTargets' => [ 'name' => 'RegisterTargets', 'http' => [ 'method' => 'POST', 'requestUri' => '/targetgroups/{targetGroupIdentifier}/registertargets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RegisterTargetsRequest', ], 'output' => [ 'shape' => 'RegisterTargetsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UpdateAccessLogSubscription' => [ 'name' => 'UpdateAccessLogSubscription', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/accesslogsubscriptions/{accessLogSubscriptionIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAccessLogSubscriptionRequest', ], 'output' => [ 'shape' => 'UpdateAccessLogSubscriptionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UpdateListener' => [ 'name' => 'UpdateListener', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/services/{serviceIdentifier}/listeners/{listenerIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateListenerRequest', ], 'output' => [ 'shape' => 'UpdateListenerResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UpdateResourceConfiguration' => [ 'name' => 'UpdateResourceConfiguration', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/resourceconfigurations/{resourceConfigurationIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateResourceConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateResourceConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateResourceGateway' => [ 'name' => 'UpdateResourceGateway', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/resourcegateways/{resourceGatewayIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateResourceGatewayRequest', ], 'output' => [ 'shape' => 'UpdateResourceGatewayResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateRule' => [ 'name' => 'UpdateRule', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/services/{serviceIdentifier}/listeners/{listenerIdentifier}/rules/{ruleIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateRuleRequest', ], 'output' => [ 'shape' => 'UpdateRuleResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UpdateService' => [ 'name' => 'UpdateService', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/services/{serviceIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateServiceRequest', ], 'output' => [ 'shape' => 'UpdateServiceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateServiceNetwork' => [ 'name' => 'UpdateServiceNetwork', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/servicenetworks/{serviceNetworkIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateServiceNetworkRequest', ], 'output' => [ 'shape' => 'UpdateServiceNetworkResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UpdateServiceNetworkVpcAssociation' => [ 'name' => 'UpdateServiceNetworkVpcAssociation', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/servicenetworkvpcassociations/{serviceNetworkVpcAssociationIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateServiceNetworkVpcAssociationRequest', ], 'output' => [ 'shape' => 'UpdateServiceNetworkVpcAssociationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UpdateTargetGroup' => [ 'name' => 'UpdateTargetGroup', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/targetgroups/{targetGroupIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateTargetGroupRequest', ], 'output' => [ 'shape' => 'UpdateTargetGroupResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AccessLogDestinationArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn(:[a-z0-9]+([.-][a-z0-9]+)*){2}(:([a-z0-9]+([.-][a-z0-9]+)*)?){2}:([^/].*)?$', ], 'AccessLogSubscriptionArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:[a-z0-9\\-]+:vpc-lattice:[a-zA-Z0-9\\-]+:\\d{12}:accesslogsubscription/als-[0-9a-z]{17}$', ], 'AccessLogSubscriptionId' => [ 'type' => 'string', 'max' => 21, 'min' => 21, 'pattern' => '^als-[0-9a-z]{17}$', ], 'AccessLogSubscriptionIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 17, 'pattern' => '^((als-[0-9a-z]{17})|(arn:[a-z0-9\\-]+:vpc-lattice:[a-zA-Z0-9\\-]+:\\d{12}:accesslogsubscription/als-[0-9a-z]{17}))$', ], 'AccessLogSubscriptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccessLogSubscriptionSummary', ], ], 'AccessLogSubscriptionSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'destinationArn', 'id', 'lastUpdatedAt', 'resourceArn', 'resourceId', ], 'members' => [ 'arn' => [ 'shape' => 'AccessLogSubscriptionArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'destinationArn' => [ 'shape' => 'AccessLogDestinationArn', ], 'id' => [ 'shape' => 'AccessLogSubscriptionId', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'resourceArn' => [ 'shape' => 'ResourceArn', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'serviceNetworkLogType' => [ 'shape' => 'ServiceNetworkLogType', ], ], ], 'AccountId' => [ 'type' => 'string', 'max' => 12, 'min' => 1, 'pattern' => '^[0-9]{12}$', ], 'Arn' => [ 'type' => 'string', 'max' => 1224, 'min' => 0, 'pattern' => '^arn:[a-z0-9][-.a-z0-9]{0,62}:vpc-lattice:([a-z0-9][-.a-z0-9]{0,62})?:\\d{12}?:[^/].{0,1023}$', ], 'ArnResource' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'WildcardArn', ], ], ], 'AuthPolicyState' => [ 'type' => 'string', 'enum' => [ 'Active', 'Inactive', ], ], 'AuthPolicyString' => [ 'type' => 'string', 'max' => 10000, 'min' => 0, ], 'AuthType' => [ 'type' => 'string', 'enum' => [ 'NONE', 'AWS_IAM', ], ], 'BatchUpdateRuleRequest' => [ 'type' => 'structure', 'required' => [ 'listenerIdentifier', 'rules', 'serviceIdentifier', ], 'members' => [ 'listenerIdentifier' => [ 'shape' => 'ListenerIdentifier', 'location' => 'uri', 'locationName' => 'listenerIdentifier', ], 'rules' => [ 'shape' => 'RuleUpdateList', ], 'serviceIdentifier' => [ 'shape' => 'ServiceIdentifier', 'location' => 'uri', 'locationName' => 'serviceIdentifier', ], ], ], 'BatchUpdateRuleResponse' => [ 'type' => 'structure', 'members' => [ 'successful' => [ 'shape' => 'RuleUpdateSuccessList', ], 'unsuccessful' => [ 'shape' => 'RuleUpdateFailureList', ], ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'CertificateArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^(arn(:[a-z0-9]+([.-][a-z0-9]+)*){2}(:([a-z0-9]+([.-][a-z0-9]+)*)?){2}:certificate/[0-9a-z-]+)?$', ], 'ClientToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[!-~]+', ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateAccessLogSubscriptionRequest' => [ 'type' => 'structure', 'required' => [ 'destinationArn', 'resourceIdentifier', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'destinationArn' => [ 'shape' => 'AccessLogDestinationArn', ], 'resourceIdentifier' => [ 'shape' => 'ResourceIdentifier', ], 'serviceNetworkLogType' => [ 'shape' => 'ServiceNetworkLogType', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateAccessLogSubscriptionResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'destinationArn', 'id', 'resourceArn', 'resourceId', ], 'members' => [ 'arn' => [ 'shape' => 'AccessLogSubscriptionArn', ], 'destinationArn' => [ 'shape' => 'AccessLogDestinationArn', ], 'id' => [ 'shape' => 'AccessLogSubscriptionId', ], 'resourceArn' => [ 'shape' => 'ResourceArn', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'serviceNetworkLogType' => [ 'shape' => 'ServiceNetworkLogType', ], ], ], 'CreateListenerRequest' => [ 'type' => 'structure', 'required' => [ 'defaultAction', 'name', 'protocol', 'serviceIdentifier', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'defaultAction' => [ 'shape' => 'RuleAction', ], 'name' => [ 'shape' => 'ListenerName', ], 'port' => [ 'shape' => 'Port', ], 'protocol' => [ 'shape' => 'ListenerProtocol', ], 'serviceIdentifier' => [ 'shape' => 'ServiceIdentifier', 'location' => 'uri', 'locationName' => 'serviceIdentifier', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateListenerResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ListenerArn', ], 'defaultAction' => [ 'shape' => 'RuleAction', ], 'id' => [ 'shape' => 'ListenerId', ], 'name' => [ 'shape' => 'ListenerName', ], 'port' => [ 'shape' => 'Port', ], 'protocol' => [ 'shape' => 'ListenerProtocol', ], 'serviceArn' => [ 'shape' => 'ServiceArn', ], 'serviceId' => [ 'shape' => 'ServiceId', ], ], ], 'CreateResourceConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'type', ], 'members' => [ 'allowAssociationToShareableServiceNetwork' => [ 'shape' => 'Boolean', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'name' => [ 'shape' => 'ResourceConfigurationName', ], 'portRanges' => [ 'shape' => 'PortRangeList', ], 'protocol' => [ 'shape' => 'ProtocolType', ], 'resourceConfigurationDefinition' => [ 'shape' => 'ResourceConfigurationDefinition', ], 'resourceConfigurationGroupIdentifier' => [ 'shape' => 'ResourceConfigurationIdentifier', ], 'resourceGatewayIdentifier' => [ 'shape' => 'ResourceGatewayIdentifier', ], 'tags' => [ 'shape' => 'TagMap', ], 'type' => [ 'shape' => 'ResourceConfigurationType', ], ], ], 'CreateResourceConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'allowAssociationToShareableServiceNetwork' => [ 'shape' => 'Boolean', ], 'arn' => [ 'shape' => 'ResourceConfigurationArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'failureReason' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'ResourceConfigurationId', ], 'name' => [ 'shape' => 'ResourceConfigurationName', ], 'portRanges' => [ 'shape' => 'PortRangeList', ], 'protocol' => [ 'shape' => 'ProtocolType', ], 'resourceConfigurationDefinition' => [ 'shape' => 'ResourceConfigurationDefinition', ], 'resourceConfigurationGroupId' => [ 'shape' => 'ResourceConfigurationId', ], 'resourceGatewayId' => [ 'shape' => 'ResourceGatewayId', ], 'status' => [ 'shape' => 'ResourceConfigurationStatus', ], 'type' => [ 'shape' => 'ResourceConfigurationType', ], ], ], 'CreateResourceGatewayRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'subnetIds', 'vpcIdentifier', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'ipAddressType' => [ 'shape' => 'ResourceGatewayIpAddressType', ], 'name' => [ 'shape' => 'ResourceGatewayName', ], 'securityGroupIds' => [ 'shape' => 'CreateResourceGatewayRequestSecurityGroupIdsList', ], 'subnetIds' => [ 'shape' => 'SubnetList', ], 'tags' => [ 'shape' => 'TagMap', ], 'vpcIdentifier' => [ 'shape' => 'VpcId', ], ], ], 'CreateResourceGatewayRequestSecurityGroupIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], 'max' => 5, 'min' => 0, ], 'CreateResourceGatewayResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ResourceGatewayArn', ], 'id' => [ 'shape' => 'ResourceGatewayId', ], 'ipAddressType' => [ 'shape' => 'ResourceGatewayIpAddressType', ], 'name' => [ 'shape' => 'ResourceGatewayName', ], 'securityGroupIds' => [ 'shape' => 'SecurityGroupList', ], 'status' => [ 'shape' => 'ResourceGatewayStatus', ], 'subnetIds' => [ 'shape' => 'SubnetList', ], 'vpcIdentifier' => [ 'shape' => 'VpcId', ], ], ], 'CreateRuleRequest' => [ 'type' => 'structure', 'required' => [ 'action', 'listenerIdentifier', 'match', 'name', 'priority', 'serviceIdentifier', ], 'members' => [ 'action' => [ 'shape' => 'RuleAction', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'listenerIdentifier' => [ 'shape' => 'ListenerIdentifier', 'location' => 'uri', 'locationName' => 'listenerIdentifier', ], 'match' => [ 'shape' => 'RuleMatch', ], 'name' => [ 'shape' => 'RuleName', ], 'priority' => [ 'shape' => 'RulePriority', ], 'serviceIdentifier' => [ 'shape' => 'ServiceIdentifier', 'location' => 'uri', 'locationName' => 'serviceIdentifier', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateRuleResponse' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'RuleAction', ], 'arn' => [ 'shape' => 'RuleArn', ], 'id' => [ 'shape' => 'RuleId', ], 'match' => [ 'shape' => 'RuleMatch', ], 'name' => [ 'shape' => 'RuleName', ], 'priority' => [ 'shape' => 'RulePriority', ], ], ], 'CreateServiceNetworkRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'authType' => [ 'shape' => 'AuthType', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'name' => [ 'shape' => 'ServiceNetworkName', ], 'sharingConfig' => [ 'shape' => 'SharingConfig', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateServiceNetworkResourceAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'resourceConfigurationIdentifier', 'serviceNetworkIdentifier', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'resourceConfigurationIdentifier' => [ 'shape' => 'ResourceConfigurationIdentifier', ], 'serviceNetworkIdentifier' => [ 'shape' => 'ServiceNetworkIdentifierWithoutRegex', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateServiceNetworkResourceAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ServiceNetworkResourceAssociationArn', ], 'createdBy' => [ 'shape' => 'AccountId', ], 'id' => [ 'shape' => 'ServiceNetworkResourceAssociationId', ], 'status' => [ 'shape' => 'ServiceNetworkResourceAssociationStatus', ], ], ], 'CreateServiceNetworkResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ServiceNetworkArn', ], 'authType' => [ 'shape' => 'AuthType', ], 'id' => [ 'shape' => 'ServiceNetworkId', ], 'name' => [ 'shape' => 'ServiceNetworkName', ], 'sharingConfig' => [ 'shape' => 'SharingConfig', ], ], ], 'CreateServiceNetworkServiceAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'serviceIdentifier', 'serviceNetworkIdentifier', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'serviceIdentifier' => [ 'shape' => 'ServiceIdentifier', ], 'serviceNetworkIdentifier' => [ 'shape' => 'ServiceNetworkIdentifier', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateServiceNetworkServiceAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ServiceNetworkServiceAssociationArn', ], 'createdBy' => [ 'shape' => 'AccountId', ], 'customDomainName' => [ 'shape' => 'ServiceCustomDomainName', ], 'dnsEntry' => [ 'shape' => 'DnsEntry', ], 'id' => [ 'shape' => 'ServiceNetworkServiceAssociationIdentifier', ], 'status' => [ 'shape' => 'ServiceNetworkServiceAssociationStatus', ], ], ], 'CreateServiceNetworkVpcAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'serviceNetworkIdentifier', 'vpcIdentifier', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'securityGroupIds' => [ 'shape' => 'CreateServiceNetworkVpcAssociationRequestSecurityGroupIdsList', ], 'serviceNetworkIdentifier' => [ 'shape' => 'ServiceNetworkIdentifier', ], 'tags' => [ 'shape' => 'TagMap', ], 'vpcIdentifier' => [ 'shape' => 'VpcId', ], ], ], 'CreateServiceNetworkVpcAssociationRequestSecurityGroupIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], 'max' => 5, 'min' => 0, ], 'CreateServiceNetworkVpcAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ServiceNetworkVpcAssociationArn', ], 'createdBy' => [ 'shape' => 'AccountId', ], 'id' => [ 'shape' => 'ServiceNetworkVpcAssociationId', ], 'securityGroupIds' => [ 'shape' => 'SecurityGroupList', ], 'status' => [ 'shape' => 'ServiceNetworkVpcAssociationStatus', ], ], ], 'CreateServiceRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'authType' => [ 'shape' => 'AuthType', ], 'certificateArn' => [ 'shape' => 'CertificateArn', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'customDomainName' => [ 'shape' => 'ServiceCustomDomainName', ], 'name' => [ 'shape' => 'ServiceName', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateServiceResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ServiceArn', ], 'authType' => [ 'shape' => 'AuthType', ], 'certificateArn' => [ 'shape' => 'CertificateArn', ], 'customDomainName' => [ 'shape' => 'ServiceCustomDomainName', ], 'dnsEntry' => [ 'shape' => 'DnsEntry', ], 'id' => [ 'shape' => 'ServiceId', ], 'name' => [ 'shape' => 'ServiceName', ], 'status' => [ 'shape' => 'ServiceStatus', ], ], ], 'CreateTargetGroupRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'type', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'config' => [ 'shape' => 'TargetGroupConfig', ], 'name' => [ 'shape' => 'TargetGroupName', ], 'tags' => [ 'shape' => 'TagMap', ], 'type' => [ 'shape' => 'TargetGroupType', ], ], ], 'CreateTargetGroupResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'TargetGroupArn', ], 'config' => [ 'shape' => 'TargetGroupConfig', ], 'id' => [ 'shape' => 'TargetGroupId', ], 'name' => [ 'shape' => 'TargetGroupName', ], 'status' => [ 'shape' => 'TargetGroupStatus', ], 'type' => [ 'shape' => 'TargetGroupType', ], ], ], 'DeleteAccessLogSubscriptionRequest' => [ 'type' => 'structure', 'required' => [ 'accessLogSubscriptionIdentifier', ], 'members' => [ 'accessLogSubscriptionIdentifier' => [ 'shape' => 'AccessLogSubscriptionIdentifier', 'location' => 'uri', 'locationName' => 'accessLogSubscriptionIdentifier', ], ], ], 'DeleteAccessLogSubscriptionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAuthPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'resourceIdentifier', ], 'members' => [ 'resourceIdentifier' => [ 'shape' => 'ResourceIdentifier', 'location' => 'uri', 'locationName' => 'resourceIdentifier', ], ], ], 'DeleteAuthPolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteListenerRequest' => [ 'type' => 'structure', 'required' => [ 'listenerIdentifier', 'serviceIdentifier', ], 'members' => [ 'listenerIdentifier' => [ 'shape' => 'ListenerIdentifier', 'location' => 'uri', 'locationName' => 'listenerIdentifier', ], 'serviceIdentifier' => [ 'shape' => 'ServiceIdentifier', 'location' => 'uri', 'locationName' => 'serviceIdentifier', ], ], ], 'DeleteListenerResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteResourceConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'resourceConfigurationIdentifier', ], 'members' => [ 'resourceConfigurationIdentifier' => [ 'shape' => 'ResourceConfigurationIdentifier', 'location' => 'uri', 'locationName' => 'resourceConfigurationIdentifier', ], ], ], 'DeleteResourceConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteResourceEndpointAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'resourceEndpointAssociationIdentifier', ], 'members' => [ 'resourceEndpointAssociationIdentifier' => [ 'shape' => 'ResourceEndpointAssociationIdentifier', 'location' => 'uri', 'locationName' => 'resourceEndpointAssociationIdentifier', ], ], ], 'DeleteResourceEndpointAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ResourceEndpointAssociationArn', ], 'id' => [ 'shape' => 'ResourceEndpointAssociationId', ], 'resourceConfigurationArn' => [ 'shape' => 'ResourceConfigurationArn', ], 'resourceConfigurationId' => [ 'shape' => 'ResourceConfigurationId', ], 'vpcEndpointId' => [ 'shape' => 'VpcEndpointId', ], ], ], 'DeleteResourceGatewayRequest' => [ 'type' => 'structure', 'required' => [ 'resourceGatewayIdentifier', ], 'members' => [ 'resourceGatewayIdentifier' => [ 'shape' => 'ResourceGatewayIdentifier', 'location' => 'uri', 'locationName' => 'resourceGatewayIdentifier', ], ], ], 'DeleteResourceGatewayResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ResourceGatewayArn', ], 'id' => [ 'shape' => 'ResourceGatewayId', ], 'name' => [ 'shape' => 'ResourceGatewayName', ], 'status' => [ 'shape' => 'ResourceGatewayStatus', ], ], ], 'DeleteResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'DeleteResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRuleRequest' => [ 'type' => 'structure', 'required' => [ 'listenerIdentifier', 'ruleIdentifier', 'serviceIdentifier', ], 'members' => [ 'listenerIdentifier' => [ 'shape' => 'ListenerIdentifier', 'location' => 'uri', 'locationName' => 'listenerIdentifier', ], 'ruleIdentifier' => [ 'shape' => 'RuleIdentifier', 'location' => 'uri', 'locationName' => 'ruleIdentifier', ], 'serviceIdentifier' => [ 'shape' => 'ServiceIdentifier', 'location' => 'uri', 'locationName' => 'serviceIdentifier', ], ], ], 'DeleteRuleResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteServiceNetworkRequest' => [ 'type' => 'structure', 'required' => [ 'serviceNetworkIdentifier', ], 'members' => [ 'serviceNetworkIdentifier' => [ 'shape' => 'ServiceNetworkIdentifier', 'location' => 'uri', 'locationName' => 'serviceNetworkIdentifier', ], ], ], 'DeleteServiceNetworkResourceAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'serviceNetworkResourceAssociationIdentifier', ], 'members' => [ 'serviceNetworkResourceAssociationIdentifier' => [ 'shape' => 'ServiceNetworkResourceAssociationIdentifier', 'location' => 'uri', 'locationName' => 'serviceNetworkResourceAssociationIdentifier', ], ], ], 'DeleteServiceNetworkResourceAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ServiceNetworkResourceAssociationArn', ], 'id' => [ 'shape' => 'ServiceNetworkResourceAssociationId', ], 'status' => [ 'shape' => 'ServiceNetworkResourceAssociationStatus', ], ], ], 'DeleteServiceNetworkResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteServiceNetworkServiceAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'serviceNetworkServiceAssociationIdentifier', ], 'members' => [ 'serviceNetworkServiceAssociationIdentifier' => [ 'shape' => 'ServiceNetworkServiceAssociationIdentifier', 'location' => 'uri', 'locationName' => 'serviceNetworkServiceAssociationIdentifier', ], ], ], 'DeleteServiceNetworkServiceAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ServiceNetworkServiceAssociationArn', ], 'id' => [ 'shape' => 'ServiceNetworkServiceAssociationIdentifier', ], 'status' => [ 'shape' => 'ServiceNetworkServiceAssociationStatus', ], ], ], 'DeleteServiceNetworkVpcAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'serviceNetworkVpcAssociationIdentifier', ], 'members' => [ 'serviceNetworkVpcAssociationIdentifier' => [ 'shape' => 'ServiceNetworkVpcAssociationIdentifier', 'location' => 'uri', 'locationName' => 'serviceNetworkVpcAssociationIdentifier', ], ], ], 'DeleteServiceNetworkVpcAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ServiceNetworkVpcAssociationArn', ], 'id' => [ 'shape' => 'ServiceNetworkVpcAssociationId', ], 'status' => [ 'shape' => 'ServiceNetworkVpcAssociationStatus', ], ], ], 'DeleteServiceRequest' => [ 'type' => 'structure', 'required' => [ 'serviceIdentifier', ], 'members' => [ 'serviceIdentifier' => [ 'shape' => 'ServiceIdentifier', 'location' => 'uri', 'locationName' => 'serviceIdentifier', ], ], ], 'DeleteServiceResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ServiceArn', ], 'id' => [ 'shape' => 'ServiceId', ], 'name' => [ 'shape' => 'ServiceName', ], 'status' => [ 'shape' => 'ServiceStatus', ], ], ], 'DeleteTargetGroupRequest' => [ 'type' => 'structure', 'required' => [ 'targetGroupIdentifier', ], 'members' => [ 'targetGroupIdentifier' => [ 'shape' => 'TargetGroupIdentifier', 'location' => 'uri', 'locationName' => 'targetGroupIdentifier', ], ], ], 'DeleteTargetGroupResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'TargetGroupArn', ], 'id' => [ 'shape' => 'TargetGroupId', ], 'status' => [ 'shape' => 'TargetGroupStatus', ], ], ], 'DeregisterTargetsRequest' => [ 'type' => 'structure', 'required' => [ 'targetGroupIdentifier', 'targets', ], 'members' => [ 'targetGroupIdentifier' => [ 'shape' => 'TargetGroupIdentifier', 'location' => 'uri', 'locationName' => 'targetGroupIdentifier', ], 'targets' => [ 'shape' => 'DeregisterTargetsRequestTargetsList', ], ], ], 'DeregisterTargetsRequestTargetsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Target', ], 'max' => 100, 'min' => 1, ], 'DeregisterTargetsResponse' => [ 'type' => 'structure', 'members' => [ 'successful' => [ 'shape' => 'TargetList', ], 'unsuccessful' => [ 'shape' => 'TargetFailureList', ], ], ], 'DnsEntry' => [ 'type' => 'structure', 'members' => [ 'domainName' => [ 'shape' => 'String', ], 'hostedZoneId' => [ 'shape' => 'String', ], ], ], 'DnsResource' => [ 'type' => 'structure', 'members' => [ 'domainName' => [ 'shape' => 'DomainName', ], 'ipAddressType' => [ 'shape' => 'ResourceConfigurationIpAddressType', ], ], ], 'DomainName' => [ 'type' => 'string', 'max' => 255, 'min' => 3, ], 'FailureCode' => [ 'type' => 'string', ], 'FailureMessage' => [ 'type' => 'string', ], 'FixedResponseAction' => [ 'type' => 'structure', 'required' => [ 'statusCode', ], 'members' => [ 'statusCode' => [ 'shape' => 'HttpStatusCode', ], ], ], 'ForwardAction' => [ 'type' => 'structure', 'required' => [ 'targetGroups', ], 'members' => [ 'targetGroups' => [ 'shape' => 'WeightedTargetGroupList', ], ], ], 'GetAccessLogSubscriptionRequest' => [ 'type' => 'structure', 'required' => [ 'accessLogSubscriptionIdentifier', ], 'members' => [ 'accessLogSubscriptionIdentifier' => [ 'shape' => 'AccessLogSubscriptionIdentifier', 'location' => 'uri', 'locationName' => 'accessLogSubscriptionIdentifier', ], ], ], 'GetAccessLogSubscriptionResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'destinationArn', 'id', 'lastUpdatedAt', 'resourceArn', 'resourceId', ], 'members' => [ 'arn' => [ 'shape' => 'AccessLogSubscriptionArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'destinationArn' => [ 'shape' => 'AccessLogDestinationArn', ], 'id' => [ 'shape' => 'AccessLogSubscriptionId', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'resourceArn' => [ 'shape' => 'ResourceArn', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'serviceNetworkLogType' => [ 'shape' => 'ServiceNetworkLogType', ], ], ], 'GetAuthPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'resourceIdentifier', ], 'members' => [ 'resourceIdentifier' => [ 'shape' => 'ResourceIdentifier', 'location' => 'uri', 'locationName' => 'resourceIdentifier', ], ], ], 'GetAuthPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'createdAt' => [ 'shape' => 'Timestamp', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'policy' => [ 'shape' => 'AuthPolicyString', ], 'state' => [ 'shape' => 'AuthPolicyState', ], ], ], 'GetListenerRequest' => [ 'type' => 'structure', 'required' => [ 'listenerIdentifier', 'serviceIdentifier', ], 'members' => [ 'listenerIdentifier' => [ 'shape' => 'ListenerIdentifier', 'location' => 'uri', 'locationName' => 'listenerIdentifier', ], 'serviceIdentifier' => [ 'shape' => 'ServiceIdentifier', 'location' => 'uri', 'locationName' => 'serviceIdentifier', ], ], ], 'GetListenerResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ListenerArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'defaultAction' => [ 'shape' => 'RuleAction', ], 'id' => [ 'shape' => 'ListenerId', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'name' => [ 'shape' => 'ListenerName', ], 'port' => [ 'shape' => 'Port', ], 'protocol' => [ 'shape' => 'ListenerProtocol', ], 'serviceArn' => [ 'shape' => 'ServiceArn', ], 'serviceId' => [ 'shape' => 'ServiceId', ], ], ], 'GetResourceConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'resourceConfigurationIdentifier', ], 'members' => [ 'resourceConfigurationIdentifier' => [ 'shape' => 'ResourceConfigurationIdentifier', 'location' => 'uri', 'locationName' => 'resourceConfigurationIdentifier', ], ], ], 'GetResourceConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'allowAssociationToShareableServiceNetwork' => [ 'shape' => 'Boolean', ], 'amazonManaged' => [ 'shape' => 'Boolean', ], 'arn' => [ 'shape' => 'ResourceConfigurationArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'customDomainName' => [ 'shape' => 'DomainName', ], 'failureReason' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'ResourceConfigurationId', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'name' => [ 'shape' => 'ResourceConfigurationName', ], 'portRanges' => [ 'shape' => 'PortRangeList', ], 'protocol' => [ 'shape' => 'ProtocolType', ], 'resourceConfigurationDefinition' => [ 'shape' => 'ResourceConfigurationDefinition', ], 'resourceConfigurationGroupId' => [ 'shape' => 'ResourceConfigurationId', ], 'resourceGatewayId' => [ 'shape' => 'ResourceGatewayId', ], 'status' => [ 'shape' => 'ResourceConfigurationStatus', ], 'type' => [ 'shape' => 'ResourceConfigurationType', ], ], ], 'GetResourceGatewayRequest' => [ 'type' => 'structure', 'required' => [ 'resourceGatewayIdentifier', ], 'members' => [ 'resourceGatewayIdentifier' => [ 'shape' => 'ResourceGatewayIdentifier', 'location' => 'uri', 'locationName' => 'resourceGatewayIdentifier', ], ], ], 'GetResourceGatewayResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ResourceGatewayArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'id' => [ 'shape' => 'ResourceGatewayId', ], 'ipAddressType' => [ 'shape' => 'ResourceGatewayIpAddressType', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'name' => [ 'shape' => 'ResourceGatewayName', ], 'securityGroupIds' => [ 'shape' => 'SecurityGroupList', ], 'status' => [ 'shape' => 'ResourceGatewayStatus', ], 'subnetIds' => [ 'shape' => 'SubnetList', ], 'vpcId' => [ 'shape' => 'VpcId', ], ], ], 'GetResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'GetResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'policy' => [ 'shape' => 'PolicyString', ], ], ], 'GetRuleRequest' => [ 'type' => 'structure', 'required' => [ 'listenerIdentifier', 'ruleIdentifier', 'serviceIdentifier', ], 'members' => [ 'listenerIdentifier' => [ 'shape' => 'ListenerIdentifier', 'location' => 'uri', 'locationName' => 'listenerIdentifier', ], 'ruleIdentifier' => [ 'shape' => 'RuleIdentifier', 'location' => 'uri', 'locationName' => 'ruleIdentifier', ], 'serviceIdentifier' => [ 'shape' => 'ServiceIdentifier', 'location' => 'uri', 'locationName' => 'serviceIdentifier', ], ], ], 'GetRuleResponse' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'RuleAction', ], 'arn' => [ 'shape' => 'RuleArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'id' => [ 'shape' => 'RuleId', ], 'isDefault' => [ 'shape' => 'Boolean', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'match' => [ 'shape' => 'RuleMatch', ], 'name' => [ 'shape' => 'RuleName', ], 'priority' => [ 'shape' => 'RulePriority', ], ], ], 'GetServiceNetworkRequest' => [ 'type' => 'structure', 'required' => [ 'serviceNetworkIdentifier', ], 'members' => [ 'serviceNetworkIdentifier' => [ 'shape' => 'ServiceNetworkIdentifier', 'location' => 'uri', 'locationName' => 'serviceNetworkIdentifier', ], ], ], 'GetServiceNetworkResourceAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'serviceNetworkResourceAssociationIdentifier', ], 'members' => [ 'serviceNetworkResourceAssociationIdentifier' => [ 'shape' => 'ServiceNetworkResourceAssociationIdentifier', 'location' => 'uri', 'locationName' => 'serviceNetworkResourceAssociationIdentifier', ], ], ], 'GetServiceNetworkResourceAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ServiceNetworkResourceAssociationArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'createdBy' => [ 'shape' => 'AccountId', ], 'dnsEntry' => [ 'shape' => 'DnsEntry', ], 'failureCode' => [ 'shape' => 'String', ], 'failureReason' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'ServiceNetworkResourceAssociationId', ], 'isManagedAssociation' => [ 'shape' => 'Boolean', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'privateDnsEntry' => [ 'shape' => 'DnsEntry', ], 'resourceConfigurationArn' => [ 'shape' => 'ResourceConfigurationArn', ], 'resourceConfigurationId' => [ 'shape' => 'ResourceConfigurationId', ], 'resourceConfigurationName' => [ 'shape' => 'ResourceConfigurationName', ], 'serviceNetworkArn' => [ 'shape' => 'ServiceNetworkIdentifierWithoutRegex', ], 'serviceNetworkId' => [ 'shape' => 'ServiceNetworkIdentifierWithoutRegex', ], 'serviceNetworkName' => [ 'shape' => 'ServiceNetworkNameWithoutRegex', ], 'status' => [ 'shape' => 'ServiceNetworkResourceAssociationStatus', ], ], ], 'GetServiceNetworkResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ServiceNetworkArn', ], 'authType' => [ 'shape' => 'AuthType', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'id' => [ 'shape' => 'ServiceNetworkId', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'name' => [ 'shape' => 'ServiceNetworkName', ], 'numberOfAssociatedServices' => [ 'shape' => 'Long', ], 'numberOfAssociatedVPCs' => [ 'shape' => 'Long', ], 'sharingConfig' => [ 'shape' => 'SharingConfig', ], ], ], 'GetServiceNetworkServiceAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'serviceNetworkServiceAssociationIdentifier', ], 'members' => [ 'serviceNetworkServiceAssociationIdentifier' => [ 'shape' => 'ServiceNetworkServiceAssociationIdentifier', 'location' => 'uri', 'locationName' => 'serviceNetworkServiceAssociationIdentifier', ], ], ], 'GetServiceNetworkServiceAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ServiceNetworkServiceAssociationArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'createdBy' => [ 'shape' => 'AccountId', ], 'customDomainName' => [ 'shape' => 'ServiceCustomDomainName', ], 'dnsEntry' => [ 'shape' => 'DnsEntry', ], 'failureCode' => [ 'shape' => 'String', ], 'failureMessage' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'ServiceNetworkServiceAssociationIdentifier', ], 'serviceArn' => [ 'shape' => 'ServiceArn', ], 'serviceId' => [ 'shape' => 'ServiceId', ], 'serviceName' => [ 'shape' => 'ServiceName', ], 'serviceNetworkArn' => [ 'shape' => 'ServiceNetworkArn', ], 'serviceNetworkId' => [ 'shape' => 'ServiceNetworkId', ], 'serviceNetworkName' => [ 'shape' => 'ServiceNetworkName', ], 'status' => [ 'shape' => 'ServiceNetworkServiceAssociationStatus', ], ], ], 'GetServiceNetworkVpcAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'serviceNetworkVpcAssociationIdentifier', ], 'members' => [ 'serviceNetworkVpcAssociationIdentifier' => [ 'shape' => 'ServiceNetworkVpcAssociationIdentifier', 'location' => 'uri', 'locationName' => 'serviceNetworkVpcAssociationIdentifier', ], ], ], 'GetServiceNetworkVpcAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ServiceNetworkVpcAssociationArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'createdBy' => [ 'shape' => 'AccountId', ], 'failureCode' => [ 'shape' => 'String', ], 'failureMessage' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'ServiceNetworkVpcAssociationId', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'securityGroupIds' => [ 'shape' => 'SecurityGroupList', ], 'serviceNetworkArn' => [ 'shape' => 'ServiceNetworkArn', ], 'serviceNetworkId' => [ 'shape' => 'ServiceNetworkId', ], 'serviceNetworkName' => [ 'shape' => 'ServiceNetworkName', ], 'status' => [ 'shape' => 'ServiceNetworkVpcAssociationStatus', ], 'vpcId' => [ 'shape' => 'VpcId', ], ], ], 'GetServiceRequest' => [ 'type' => 'structure', 'required' => [ 'serviceIdentifier', ], 'members' => [ 'serviceIdentifier' => [ 'shape' => 'ServiceIdentifier', 'location' => 'uri', 'locationName' => 'serviceIdentifier', ], ], ], 'GetServiceResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ServiceArn', ], 'authType' => [ 'shape' => 'AuthType', ], 'certificateArn' => [ 'shape' => 'CertificateArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'customDomainName' => [ 'shape' => 'ServiceCustomDomainName', ], 'dnsEntry' => [ 'shape' => 'DnsEntry', ], 'failureCode' => [ 'shape' => 'FailureCode', ], 'failureMessage' => [ 'shape' => 'FailureMessage', ], 'id' => [ 'shape' => 'ServiceId', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'name' => [ 'shape' => 'ServiceName', ], 'status' => [ 'shape' => 'ServiceStatus', ], ], ], 'GetTargetGroupRequest' => [ 'type' => 'structure', 'required' => [ 'targetGroupIdentifier', ], 'members' => [ 'targetGroupIdentifier' => [ 'shape' => 'TargetGroupIdentifier', 'location' => 'uri', 'locationName' => 'targetGroupIdentifier', ], ], ], 'GetTargetGroupResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'TargetGroupArn', ], 'config' => [ 'shape' => 'TargetGroupConfig', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'failureCode' => [ 'shape' => 'String', ], 'failureMessage' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'TargetGroupId', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'name' => [ 'shape' => 'TargetGroupName', ], 'serviceArns' => [ 'shape' => 'ServiceArnList', ], 'status' => [ 'shape' => 'TargetGroupStatus', ], 'type' => [ 'shape' => 'TargetGroupType', ], ], ], 'HeaderMatch' => [ 'type' => 'structure', 'required' => [ 'match', 'name', ], 'members' => [ 'caseSensitive' => [ 'shape' => 'Boolean', ], 'match' => [ 'shape' => 'HeaderMatchType', ], 'name' => [ 'shape' => 'HeaderMatchName', ], ], ], 'HeaderMatchContains' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'HeaderMatchExact' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'HeaderMatchList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HeaderMatch', ], 'max' => 5, 'min' => 1, ], 'HeaderMatchName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'HeaderMatchPrefix' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'HeaderMatchType' => [ 'type' => 'structure', 'members' => [ 'contains' => [ 'shape' => 'HeaderMatchContains', ], 'exact' => [ 'shape' => 'HeaderMatchExact', ], 'prefix' => [ 'shape' => 'HeaderMatchPrefix', ], ], 'union' => true, ], 'HealthCheckConfig' => [ 'type' => 'structure', 'members' => [ 'enabled' => [ 'shape' => 'Boolean', ], 'healthCheckIntervalSeconds' => [ 'shape' => 'HealthCheckIntervalSeconds', ], 'healthCheckTimeoutSeconds' => [ 'shape' => 'HealthCheckTimeoutSeconds', ], 'healthyThresholdCount' => [ 'shape' => 'HealthyThresholdCount', ], 'matcher' => [ 'shape' => 'Matcher', ], 'path' => [ 'shape' => 'HealthCheckPath', ], 'port' => [ 'shape' => 'HealthCheckPort', ], 'protocol' => [ 'shape' => 'TargetGroupProtocol', ], 'protocolVersion' => [ 'shape' => 'HealthCheckProtocolVersion', ], 'unhealthyThresholdCount' => [ 'shape' => 'UnhealthyThresholdCount', ], ], ], 'HealthCheckIntervalSeconds' => [ 'type' => 'integer', 'box' => true, 'max' => 300, 'min' => 0, ], 'HealthCheckPath' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '(^/[a-zA-Z0-9@:%_+.~#?&/=-]*$|(^$))', ], 'HealthCheckPort' => [ 'type' => 'integer', 'box' => true, 'max' => 65535, 'min' => 0, ], 'HealthCheckProtocolVersion' => [ 'type' => 'string', 'enum' => [ 'HTTP1', 'HTTP2', ], ], 'HealthCheckTimeoutSeconds' => [ 'type' => 'integer', 'box' => true, 'max' => 120, 'min' => 0, ], 'HealthyThresholdCount' => [ 'type' => 'integer', 'box' => true, 'max' => 10, 'min' => 0, ], 'HttpCodeMatcher' => [ 'type' => 'string', 'max' => 2000, 'min' => 0, 'pattern' => '(^[0-9-,]+$|(^$))', ], 'HttpMatch' => [ 'type' => 'structure', 'members' => [ 'headerMatches' => [ 'shape' => 'HeaderMatchList', ], 'method' => [ 'shape' => 'HttpMethod', ], 'pathMatch' => [ 'shape' => 'PathMatch', ], ], ], 'HttpMethod' => [ 'type' => 'string', 'max' => 16, 'min' => 0, ], 'HttpStatusCode' => [ 'type' => 'integer', 'box' => true, 'max' => 599, 'min' => 100, ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'IpAddress' => [ 'type' => 'string', 'max' => 39, 'min' => 4, ], 'IpAddressType' => [ 'type' => 'string', 'enum' => [ 'IPV4', 'IPV6', ], ], 'IpResource' => [ 'type' => 'structure', 'members' => [ 'ipAddress' => [ 'shape' => 'IpAddress', ], ], ], 'LambdaEventStructureVersion' => [ 'type' => 'string', 'enum' => [ 'V1', 'V2', ], ], 'ListAccessLogSubscriptionsRequest' => [ 'type' => 'structure', 'required' => [ 'resourceIdentifier', ], 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'resourceIdentifier' => [ 'shape' => 'ResourceIdentifier', 'location' => 'querystring', 'locationName' => 'resourceIdentifier', ], ], ], 'ListAccessLogSubscriptionsResponse' => [ 'type' => 'structure', 'required' => [ 'items', ], 'members' => [ 'items' => [ 'shape' => 'AccessLogSubscriptionList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListListenersRequest' => [ 'type' => 'structure', 'required' => [ 'serviceIdentifier', ], 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'serviceIdentifier' => [ 'shape' => 'ServiceIdentifier', 'location' => 'uri', 'locationName' => 'serviceIdentifier', ], ], ], 'ListListenersResponse' => [ 'type' => 'structure', 'required' => [ 'items', ], 'members' => [ 'items' => [ 'shape' => 'ListenerSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListResourceConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'resourceConfigurationGroupIdentifier' => [ 'shape' => 'ResourceConfigurationIdentifier', 'location' => 'querystring', 'locationName' => 'resourceConfigurationGroupIdentifier', ], 'resourceGatewayIdentifier' => [ 'shape' => 'ResourceGatewayIdentifier', 'location' => 'querystring', 'locationName' => 'resourceGatewayIdentifier', ], ], ], 'ListResourceConfigurationsResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'ResourceConfigurationSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListResourceEndpointAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'resourceConfigurationIdentifier', ], 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'resourceConfigurationIdentifier' => [ 'shape' => 'ResourceConfigurationIdentifier', 'location' => 'querystring', 'locationName' => 'resourceConfigurationIdentifier', ], 'resourceEndpointAssociationIdentifier' => [ 'shape' => 'ResourceEndpointAssociationIdentifier', 'location' => 'querystring', 'locationName' => 'resourceEndpointAssociationIdentifier', ], 'vpcEndpointId' => [ 'shape' => 'VpcEndpointId', 'location' => 'querystring', 'locationName' => 'vpcEndpointId', ], 'vpcEndpointOwner' => [ 'shape' => 'VpcEndpointOwner', 'location' => 'querystring', 'locationName' => 'vpcEndpointOwner', ], ], ], 'ListResourceEndpointAssociationsResponse' => [ 'type' => 'structure', 'required' => [ 'items', ], 'members' => [ 'items' => [ 'shape' => 'ResourceEndpointAssociationList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListResourceGatewaysRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListResourceGatewaysResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'ResourceGatewayList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListRulesRequest' => [ 'type' => 'structure', 'required' => [ 'listenerIdentifier', 'serviceIdentifier', ], 'members' => [ 'listenerIdentifier' => [ 'shape' => 'ListenerIdentifier', 'location' => 'uri', 'locationName' => 'listenerIdentifier', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'serviceIdentifier' => [ 'shape' => 'ServiceIdentifier', 'location' => 'uri', 'locationName' => 'serviceIdentifier', ], ], ], 'ListRulesResponse' => [ 'type' => 'structure', 'required' => [ 'items', ], 'members' => [ 'items' => [ 'shape' => 'RuleSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListServiceNetworkResourceAssociationsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'resourceConfigurationIdentifier' => [ 'shape' => 'ResourceConfigurationIdentifier', 'location' => 'querystring', 'locationName' => 'resourceConfigurationIdentifier', ], 'serviceNetworkIdentifier' => [ 'shape' => 'ServiceNetworkIdentifier', 'location' => 'querystring', 'locationName' => 'serviceNetworkIdentifier', ], ], ], 'ListServiceNetworkResourceAssociationsResponse' => [ 'type' => 'structure', 'required' => [ 'items', ], 'members' => [ 'items' => [ 'shape' => 'ServiceNetworkResourceAssociationList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListServiceNetworkServiceAssociationsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'serviceIdentifier' => [ 'shape' => 'ServiceIdentifier', 'location' => 'querystring', 'locationName' => 'serviceIdentifier', ], 'serviceNetworkIdentifier' => [ 'shape' => 'ServiceNetworkIdentifier', 'location' => 'querystring', 'locationName' => 'serviceNetworkIdentifier', ], ], ], 'ListServiceNetworkServiceAssociationsResponse' => [ 'type' => 'structure', 'required' => [ 'items', ], 'members' => [ 'items' => [ 'shape' => 'ServiceNetworkServiceAssociationList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListServiceNetworkVpcAssociationsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'serviceNetworkIdentifier' => [ 'shape' => 'ServiceNetworkIdentifier', 'location' => 'querystring', 'locationName' => 'serviceNetworkIdentifier', ], 'vpcIdentifier' => [ 'shape' => 'VpcId', 'location' => 'querystring', 'locationName' => 'vpcIdentifier', ], ], ], 'ListServiceNetworkVpcAssociationsResponse' => [ 'type' => 'structure', 'required' => [ 'items', ], 'members' => [ 'items' => [ 'shape' => 'ServiceNetworkVpcAssociationList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListServiceNetworkVpcEndpointAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'serviceNetworkIdentifier', ], 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'serviceNetworkIdentifier' => [ 'shape' => 'ServiceNetworkIdentifier', 'location' => 'querystring', 'locationName' => 'serviceNetworkIdentifier', ], ], ], 'ListServiceNetworkVpcEndpointAssociationsResponse' => [ 'type' => 'structure', 'required' => [ 'items', ], 'members' => [ 'items' => [ 'shape' => 'ServiceNetworkVpcEndpointAssociationList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListServiceNetworksRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListServiceNetworksResponse' => [ 'type' => 'structure', 'required' => [ 'items', ], 'members' => [ 'items' => [ 'shape' => 'ServiceNetworkList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListServicesRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListServicesResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'ServiceList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ListTargetGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'targetGroupType' => [ 'shape' => 'TargetGroupType', 'location' => 'querystring', 'locationName' => 'targetGroupType', ], 'vpcIdentifier' => [ 'shape' => 'VpcId', 'location' => 'querystring', 'locationName' => 'vpcIdentifier', ], ], ], 'ListTargetGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'TargetGroupList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTargetsRequest' => [ 'type' => 'structure', 'required' => [ 'targetGroupIdentifier', ], 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'targetGroupIdentifier' => [ 'shape' => 'TargetGroupIdentifier', 'location' => 'uri', 'locationName' => 'targetGroupIdentifier', ], 'targets' => [ 'shape' => 'ListTargetsRequestTargetsList', ], ], ], 'ListTargetsRequestTargetsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Target', ], 'max' => 20, 'min' => 0, ], 'ListTargetsResponse' => [ 'type' => 'structure', 'required' => [ 'items', ], 'members' => [ 'items' => [ 'shape' => 'TargetSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListenerArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:[a-z0-9\\-]+:vpc-lattice:[a-zA-Z0-9\\-]+:\\d{12}:service/svc-[0-9a-z]{17}/listener/listener-[0-9a-z]{17}$', ], 'ListenerId' => [ 'type' => 'string', 'max' => 26, 'min' => 26, 'pattern' => '^listener-[0-9a-z]{17}$', ], 'ListenerIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^((listener-[0-9a-z]{17})|(^arn:[a-z0-9\\-]+:vpc-lattice:[a-zA-Z0-9\\-]+:\\d{12}:service/svc-[0-9a-z]{17}/listener/listener-[0-9a-z]{17}$))$', ], 'ListenerName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^(?!listener-)(?![-])(?!.*[-]$)(?!.*[-]{2})[a-z0-9-]+$', ], 'ListenerProtocol' => [ 'type' => 'string', 'enum' => [ 'HTTP', 'HTTPS', 'TLS_PASSTHROUGH', ], ], 'ListenerSummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ListenerArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'id' => [ 'shape' => 'ListenerId', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'name' => [ 'shape' => 'ListenerName', ], 'port' => [ 'shape' => 'Port', ], 'protocol' => [ 'shape' => 'ListenerProtocol', ], ], ], 'ListenerSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListenerSummary', ], ], 'Long' => [ 'type' => 'long', 'box' => true, ], 'Matcher' => [ 'type' => 'structure', 'members' => [ 'httpCode' => [ 'shape' => 'HttpCodeMatcher', ], ], 'union' => true, ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'NextToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'PathMatch' => [ 'type' => 'structure', 'required' => [ 'match', ], 'members' => [ 'caseSensitive' => [ 'shape' => 'Boolean', ], 'match' => [ 'shape' => 'PathMatchType', ], ], ], 'PathMatchExact' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^/[a-zA-Z0-9@:%_+.~#?&/=-]*$', ], 'PathMatchPrefix' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^/[a-zA-Z0-9@:%_+.~#?&/=-]*$', ], 'PathMatchType' => [ 'type' => 'structure', 'members' => [ 'exact' => [ 'shape' => 'PathMatchExact', ], 'prefix' => [ 'shape' => 'PathMatchPrefix', ], ], 'union' => true, ], 'PolicyString' => [ 'type' => 'string', 'max' => 10000, 'min' => 1, 'pattern' => '^.*\\S.*$', ], 'Port' => [ 'type' => 'integer', 'box' => true, 'max' => 65535, 'min' => 1, ], 'PortRange' => [ 'type' => 'string', 'max' => 11, 'min' => 1, 'pattern' => '^((\\d{1,5}\\-\\d{1,5})|(\\d+))$', ], 'PortRangeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PortRange', ], ], 'ProtocolType' => [ 'type' => 'string', 'enum' => [ 'TCP', ], ], 'PutAuthPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'policy', 'resourceIdentifier', ], 'members' => [ 'policy' => [ 'shape' => 'AuthPolicyString', ], 'resourceIdentifier' => [ 'shape' => 'ResourceIdentifier', 'location' => 'uri', 'locationName' => 'resourceIdentifier', ], ], ], 'PutAuthPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'policy' => [ 'shape' => 'AuthPolicyString', ], 'state' => [ 'shape' => 'AuthPolicyState', ], ], ], 'PutResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'policy', 'resourceArn', ], 'members' => [ 'policy' => [ 'shape' => 'PolicyString', ], 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'PutResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'RegisterTargetsRequest' => [ 'type' => 'structure', 'required' => [ 'targetGroupIdentifier', 'targets', ], 'members' => [ 'targetGroupIdentifier' => [ 'shape' => 'TargetGroupIdentifier', 'location' => 'uri', 'locationName' => 'targetGroupIdentifier', ], 'targets' => [ 'shape' => 'RegisterTargetsRequestTargetsList', ], ], ], 'RegisterTargetsRequestTargetsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Target', ], 'max' => 100, 'min' => 1, ], 'RegisterTargetsResponse' => [ 'type' => 'structure', 'members' => [ 'successful' => [ 'shape' => 'TargetList', ], 'unsuccessful' => [ 'shape' => 'TargetFailureList', ], ], ], 'ResourceArn' => [ 'type' => 'string', 'max' => 200, 'min' => 20, 'pattern' => '^arn(:[a-z0-9]+([.-][a-z0-9]+)*){2}(:([a-z0-9]+([.-][a-z0-9]+)*)?){2}:((servicenetwork/sn)|(service/svc)|(resourceconfiguration/rcfg))-[0-9a-z]{17}$', ], 'ResourceConfigurationArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:[a-z0-9f\\-]+:vpc-lattice:[a-zA-Z0-9\\-]+:\\d{12}:resourceconfiguration/rcfg-[0-9a-z]{17}$', ], 'ResourceConfigurationDefinition' => [ 'type' => 'structure', 'members' => [ 'arnResource' => [ 'shape' => 'ArnResource', ], 'dnsResource' => [ 'shape' => 'DnsResource', ], 'ipResource' => [ 'shape' => 'IpResource', ], ], 'union' => true, ], 'ResourceConfigurationId' => [ 'type' => 'string', 'max' => 22, 'min' => 22, 'pattern' => '^rcfg-[0-9a-z]{17}$', ], 'ResourceConfigurationIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^((rcfg-[0-9a-z]{17})|(arn:[a-z0-9\\-]+:vpc-lattice:[a-zA-Z0-9\\-]+:\\d{12}:resourceconfiguration/rcfg-[0-9a-z]{17}))$', ], 'ResourceConfigurationIpAddressType' => [ 'type' => 'string', 'enum' => [ 'IPV4', 'IPV6', 'DUALSTACK', ], ], 'ResourceConfigurationName' => [ 'type' => 'string', 'max' => 40, 'min' => 3, 'pattern' => '^(?!rcfg-)(?![-])(?!.*[-]$)(?!.*[-]{2})[a-z0-9-]+$', ], 'ResourceConfigurationStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'CREATE_IN_PROGRESS', 'UPDATE_IN_PROGRESS', 'DELETE_IN_PROGRESS', 'CREATE_FAILED', 'UPDATE_FAILED', 'DELETE_FAILED', ], ], 'ResourceConfigurationSummary' => [ 'type' => 'structure', 'members' => [ 'amazonManaged' => [ 'shape' => 'Boolean', ], 'arn' => [ 'shape' => 'ResourceConfigurationArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'id' => [ 'shape' => 'ResourceConfigurationId', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'name' => [ 'shape' => 'ResourceConfigurationName', ], 'resourceConfigurationGroupId' => [ 'shape' => 'ResourceConfigurationId', ], 'resourceGatewayId' => [ 'shape' => 'ResourceGatewayId', ], 'status' => [ 'shape' => 'ResourceConfigurationStatus', ], 'type' => [ 'shape' => 'ResourceConfigurationType', ], ], ], 'ResourceConfigurationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceConfigurationSummary', ], ], 'ResourceConfigurationType' => [ 'type' => 'string', 'enum' => [ 'GROUP', 'CHILD', 'SINGLE', 'ARN', ], ], 'ResourceEndpointAssociationArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 21, 'pattern' => '^arn:[a-z0-9\\-]+:vpc-lattice:[a-zA-Z0-9\\-]+:\\d{12}:resourceendpointassociation/rea-[0-9a-f]{17}$', ], 'ResourceEndpointAssociationId' => [ 'type' => 'string', 'max' => 21, 'min' => 21, 'pattern' => '^rea-[0-9a-f]{17}$', ], 'ResourceEndpointAssociationIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 21, 'pattern' => '^((rea-[0-9a-f]{17})|(arn:[a-z0-9\\-]+:vpc-lattice:[a-zA-Z0-9\\-]+:\\d{12}:resourceendpointassociation/rea-[0-9a-f]{17}))$', ], 'ResourceEndpointAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceEndpointAssociationSummary', ], ], 'ResourceEndpointAssociationSummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ResourceEndpointAssociationArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'createdBy' => [ 'shape' => 'AccountId', ], 'id' => [ 'shape' => 'ResourceEndpointAssociationId', ], 'resourceConfigurationArn' => [ 'shape' => 'ResourceConfigurationArn', ], 'resourceConfigurationId' => [ 'shape' => 'ResourceConfigurationId', ], 'resourceConfigurationName' => [ 'shape' => 'ResourceConfigurationName', ], 'vpcEndpointId' => [ 'shape' => 'VpcEndpointId', ], 'vpcEndpointOwner' => [ 'shape' => 'VpcEndpointOwner', ], ], ], 'ResourceGatewayArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:[a-z0-9\\-]+:vpc-lattice:[a-zA-Z0-9\\-]+:\\d{12}:resourcegateway/rgw-[0-9a-z]{17}$', ], 'ResourceGatewayId' => [ 'type' => 'string', 'max' => 21, 'min' => 21, 'pattern' => '^rgw-[0-9a-z]{17}$', ], 'ResourceGatewayIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 17, 'pattern' => '^((rgw-[0-9a-z]{17})|(arn:[a-z0-9\\-]+:vpc-lattice:[a-zA-Z0-9\\-]+:\\d{12}:resourcegateway/rgw-[0-9a-z]{17}))$', ], 'ResourceGatewayIpAddressType' => [ 'type' => 'string', 'enum' => [ 'IPV4', 'IPV6', 'DUALSTACK', ], ], 'ResourceGatewayList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceGatewaySummary', ], ], 'ResourceGatewayName' => [ 'type' => 'string', 'max' => 40, 'min' => 3, 'pattern' => '^(?!rgw-)(?![-])(?!.*[-]$)(?!.*[-]{2})[a-z0-9-]+$', ], 'ResourceGatewayStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'CREATE_IN_PROGRESS', 'UPDATE_IN_PROGRESS', 'DELETE_IN_PROGRESS', 'CREATE_FAILED', 'UPDATE_FAILED', 'DELETE_FAILED', ], ], 'ResourceGatewaySummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ResourceGatewayArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'id' => [ 'shape' => 'ResourceGatewayId', ], 'ipAddressType' => [ 'shape' => 'ResourceGatewayIpAddressType', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'name' => [ 'shape' => 'ResourceGatewayName', ], 'securityGroupIds' => [ 'shape' => 'SecurityGroupList', ], 'status' => [ 'shape' => 'ResourceGatewayStatus', ], 'subnetIds' => [ 'shape' => 'SubnetList', ], 'vpcIdentifier' => [ 'shape' => 'VpcId', ], ], ], 'ResourceId' => [ 'type' => 'string', 'max' => 50, 'min' => 20, 'pattern' => '^((sn)|(svc))-[0-9a-z]{17}$', ], 'ResourceIdentifier' => [ 'type' => 'string', 'max' => 200, 'min' => 17, 'pattern' => '^((((sn)|(svc)|(rcfg))-[0-9a-z]{17})|(arn(:[a-z0-9]+([.-][a-z0-9]+)*){2}(:([a-z0-9]+([.-][a-z0-9]+)*)?){2}:((servicenetwork/sn)|(resourceconfiguration/rcfg)|(service/svc))-[0-9a-z]{17}))$', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'RuleAction' => [ 'type' => 'structure', 'members' => [ 'fixedResponse' => [ 'shape' => 'FixedResponseAction', ], 'forward' => [ 'shape' => 'ForwardAction', ], ], 'union' => true, ], 'RuleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:[a-z0-9\\-]+:vpc-lattice:[a-zA-Z0-9\\-]+:\\d{12}:service/svc-[0-9a-z]{17}/listener/listener-[0-9a-z]{17}/rule/rule-[0-9a-z]{17}$', ], 'RuleId' => [ 'type' => 'string', 'max' => 22, 'min' => 5, 'pattern' => '^rule-[0-9a-z]{17}$', ], 'RuleIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^((rule-[0-9a-z]{17})|(^arn:[a-z0-9\\-]+:vpc-lattice:[a-zA-Z0-9\\-]+:\\d{12}:service/svc-[0-9a-z]{17}/listener/listener-[0-9a-z]{17}/rule/rule-[0-9a-z]{17}$))$', ], 'RuleMatch' => [ 'type' => 'structure', 'members' => [ 'httpMatch' => [ 'shape' => 'HttpMatch', ], ], 'union' => true, ], 'RuleName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^(?!rule-)(?![-])(?!.*[-]$)(?!.*[-]{2})[a-z0-9-]+$', ], 'RulePriority' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'RuleSummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'RuleArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'id' => [ 'shape' => 'RuleId', ], 'isDefault' => [ 'shape' => 'Boolean', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'name' => [ 'shape' => 'RuleName', ], 'priority' => [ 'shape' => 'RulePriority', ], ], ], 'RuleSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleSummary', ], ], 'RuleUpdate' => [ 'type' => 'structure', 'required' => [ 'ruleIdentifier', ], 'members' => [ 'action' => [ 'shape' => 'RuleAction', ], 'match' => [ 'shape' => 'RuleMatch', ], 'priority' => [ 'shape' => 'RulePriority', ], 'ruleIdentifier' => [ 'shape' => 'RuleIdentifier', ], ], ], 'RuleUpdateFailure' => [ 'type' => 'structure', 'members' => [ 'failureCode' => [ 'shape' => 'FailureCode', ], 'failureMessage' => [ 'shape' => 'FailureMessage', ], 'ruleIdentifier' => [ 'shape' => 'RuleIdentifier', ], ], ], 'RuleUpdateFailureList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleUpdateFailure', ], ], 'RuleUpdateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleUpdate', ], 'max' => 5, 'min' => 1, ], 'RuleUpdateSuccess' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'RuleAction', ], 'arn' => [ 'shape' => 'RuleArn', ], 'id' => [ 'shape' => 'RuleId', ], 'isDefault' => [ 'shape' => 'Boolean', ], 'match' => [ 'shape' => 'RuleMatch', ], 'name' => [ 'shape' => 'RuleName', ], 'priority' => [ 'shape' => 'RulePriority', ], ], ], 'RuleUpdateSuccessList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleUpdateSuccess', ], ], 'SecurityGroupId' => [ 'type' => 'string', 'max' => 200, 'min' => 5, 'pattern' => '^sg-(([0-9a-z]{8})|([0-9a-z]{17}))$', ], 'SecurityGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], ], 'ServiceArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:[a-z0-9\\-]+:vpc-lattice:[a-zA-Z0-9\\-]+:\\d{12}:service/svc-[0-9a-z]{17}$', ], 'ServiceArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServiceArn', ], ], 'ServiceCustomDomainName' => [ 'type' => 'string', 'max' => 255, 'min' => 3, ], 'ServiceId' => [ 'type' => 'string', 'max' => 21, 'min' => 21, 'pattern' => '^svc-[0-9a-z]{17}$', ], 'ServiceIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 17, 'pattern' => '^((svc-[0-9a-z]{17})|(arn:[a-z0-9\\-]+:vpc-lattice:[a-zA-Z0-9\\-]+:\\d{12}:service/svc-[0-9a-z]{17}))$', ], 'ServiceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServiceSummary', ], ], 'ServiceName' => [ 'type' => 'string', 'max' => 40, 'min' => 3, 'pattern' => '^(?!svc-)(?![-])(?!.*[-]$)(?!.*[-]{2})[a-z0-9-]+$', ], 'ServiceNetworkArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 32, 'pattern' => '^arn:[a-z0-9\\-]+:vpc-lattice:[a-zA-Z0-9\\-]+:\\d{12}:servicenetwork/sn-[0-9a-z]{17}$', ], 'ServiceNetworkArnWithoutRegex' => [ 'type' => 'string', 'max' => 2048, 'min' => 10, ], 'ServiceNetworkEndpointAssociation' => [ 'type' => 'structure', 'members' => [ 'createdAt' => [ 'shape' => 'Timestamp', ], 'id' => [ 'shape' => 'String', ], 'serviceNetworkArn' => [ 'shape' => 'ServiceNetworkArn', ], 'state' => [ 'shape' => 'String', ], 'vpcEndpointId' => [ 'shape' => 'String', ], 'vpcEndpointOwnerId' => [ 'shape' => 'String', ], 'vpcId' => [ 'shape' => 'String', ], ], ], 'ServiceNetworkId' => [ 'type' => 'string', 'max' => 20, 'min' => 20, 'pattern' => '^sn-[0-9a-z]{17}$', ], 'ServiceNetworkIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 3, 'pattern' => '^((sn-[0-9a-z]{17})|(arn:[a-z0-9\\-]+:vpc-lattice:[a-zA-Z0-9\\-]+:\\d{12}:servicenetwork/sn-[0-9a-z]{17}))$', ], 'ServiceNetworkIdentifierWithoutRegex' => [ 'type' => 'string', 'max' => 2048, 'min' => 3, ], 'ServiceNetworkList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServiceNetworkSummary', ], ], 'ServiceNetworkLogType' => [ 'type' => 'string', 'enum' => [ 'SERVICE', 'RESOURCE', ], ], 'ServiceNetworkName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^(?![-])(?!.*[-]$)(?!.*[-]{2})[a-z0-9-]+$', ], 'ServiceNetworkNameWithoutRegex' => [ 'type' => 'string', 'max' => 100, 'min' => 3, ], 'ServiceNetworkResourceAssociationArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 22, 'pattern' => '^arn:[a-z0-9\\-]+:vpc-lattice:[a-zA-Z0-9\\-]+:\\d{12}:servicenetworkresourceassociation/snra-[0-9a-f]{17}$', ], 'ServiceNetworkResourceAssociationId' => [ 'type' => 'string', 'max' => 22, 'min' => 22, 'pattern' => '^snra-[0-9a-f]{17}$', ], 'ServiceNetworkResourceAssociationIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 22, 'pattern' => '^((snra-[0-9a-z]{17})|(arn:[a-z0-9\\-]+:vpc-lattice:[a-zA-Z0-9\\-]+:\\d{12}:servicenetworkresourceassociation/snra-[0-9a-f]{17}))$', ], 'ServiceNetworkResourceAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServiceNetworkResourceAssociationSummary', ], ], 'ServiceNetworkResourceAssociationStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_IN_PROGRESS', 'ACTIVE', 'PARTIAL', 'DELETE_IN_PROGRESS', 'CREATE_FAILED', 'DELETE_FAILED', ], ], 'ServiceNetworkResourceAssociationSummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ServiceNetworkResourceAssociationArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'createdBy' => [ 'shape' => 'AccountId', ], 'dnsEntry' => [ 'shape' => 'DnsEntry', ], 'failureCode' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'ServiceNetworkResourceAssociationId', ], 'isManagedAssociation' => [ 'shape' => 'Boolean', ], 'privateDnsEntry' => [ 'shape' => 'DnsEntry', ], 'resourceConfigurationArn' => [ 'shape' => 'ResourceConfigurationArn', ], 'resourceConfigurationId' => [ 'shape' => 'ResourceConfigurationId', ], 'resourceConfigurationName' => [ 'shape' => 'ResourceConfigurationName', ], 'serviceNetworkArn' => [ 'shape' => 'ServiceNetworkArnWithoutRegex', ], 'serviceNetworkId' => [ 'shape' => 'ServiceNetworkIdentifierWithoutRegex', ], 'serviceNetworkName' => [ 'shape' => 'ServiceNetworkNameWithoutRegex', ], 'status' => [ 'shape' => 'ServiceNetworkResourceAssociationStatus', ], ], ], 'ServiceNetworkServiceAssociationArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:[a-z0-9\\-]+:vpc-lattice:[a-zA-Z0-9\\-]+:\\d{12}:servicenetworkserviceassociation/snsa-[0-9a-z]{17}$', ], 'ServiceNetworkServiceAssociationIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 17, 'pattern' => '^((snsa-[0-9a-z]{17})|(arn:[a-z0-9\\-]+:vpc-lattice:[a-zA-Z0-9\\-]+:\\d{12}:servicenetworkserviceassociation/snsa-[0-9a-z]{17}))$', ], 'ServiceNetworkServiceAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServiceNetworkServiceAssociationSummary', ], ], 'ServiceNetworkServiceAssociationStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_IN_PROGRESS', 'ACTIVE', 'DELETE_IN_PROGRESS', 'CREATE_FAILED', 'DELETE_FAILED', ], ], 'ServiceNetworkServiceAssociationSummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ServiceNetworkServiceAssociationArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'createdBy' => [ 'shape' => 'AccountId', ], 'customDomainName' => [ 'shape' => 'ServiceCustomDomainName', ], 'dnsEntry' => [ 'shape' => 'DnsEntry', ], 'id' => [ 'shape' => 'ServiceNetworkServiceAssociationIdentifier', ], 'serviceArn' => [ 'shape' => 'ServiceArn', ], 'serviceId' => [ 'shape' => 'ServiceId', ], 'serviceName' => [ 'shape' => 'ServiceName', ], 'serviceNetworkArn' => [ 'shape' => 'ServiceNetworkArn', ], 'serviceNetworkId' => [ 'shape' => 'ServiceNetworkId', ], 'serviceNetworkName' => [ 'shape' => 'ServiceNetworkName', ], 'status' => [ 'shape' => 'ServiceNetworkServiceAssociationStatus', ], ], ], 'ServiceNetworkSummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ServiceNetworkArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'id' => [ 'shape' => 'ServiceNetworkId', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'name' => [ 'shape' => 'ServiceNetworkName', ], 'numberOfAssociatedResourceConfigurations' => [ 'shape' => 'Long', ], 'numberOfAssociatedServices' => [ 'shape' => 'Long', ], 'numberOfAssociatedVPCs' => [ 'shape' => 'Long', ], ], ], 'ServiceNetworkVpcAssociationArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:[a-z0-9\\-]+:vpc-lattice:[a-zA-Z0-9\\-]+:\\d{12}:servicenetworkvpcassociation/snva-[0-9a-z]{17}$', ], 'ServiceNetworkVpcAssociationId' => [ 'type' => 'string', 'max' => 22, 'min' => 22, 'pattern' => '^snva-[0-9a-z]{17}$', ], 'ServiceNetworkVpcAssociationIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 17, 'pattern' => '^((snva-[0-9a-z]{17})|(arn:[a-z0-9\\-]+:vpc-lattice:[a-zA-Z0-9\\-]+:\\d{12}:servicenetworkvpcassociation/snva-[0-9a-z]{17}))$', ], 'ServiceNetworkVpcAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServiceNetworkVpcAssociationSummary', ], ], 'ServiceNetworkVpcAssociationStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_IN_PROGRESS', 'ACTIVE', 'UPDATE_IN_PROGRESS', 'DELETE_IN_PROGRESS', 'CREATE_FAILED', 'DELETE_FAILED', 'UPDATE_FAILED', ], ], 'ServiceNetworkVpcAssociationSummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ServiceNetworkVpcAssociationArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'createdBy' => [ 'shape' => 'AccountId', ], 'id' => [ 'shape' => 'ServiceNetworkVpcAssociationId', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'serviceNetworkArn' => [ 'shape' => 'ServiceNetworkArn', ], 'serviceNetworkId' => [ 'shape' => 'ServiceNetworkId', ], 'serviceNetworkName' => [ 'shape' => 'ServiceNetworkName', ], 'status' => [ 'shape' => 'ServiceNetworkVpcAssociationStatus', ], 'vpcId' => [ 'shape' => 'VpcId', ], ], ], 'ServiceNetworkVpcEndpointAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServiceNetworkEndpointAssociation', ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', 'quotaCode', 'resourceType', 'serviceCode', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], 'serviceCode' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'ServiceStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'CREATE_IN_PROGRESS', 'DELETE_IN_PROGRESS', 'CREATE_FAILED', 'DELETE_FAILED', ], ], 'ServiceSummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ServiceArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'customDomainName' => [ 'shape' => 'ServiceCustomDomainName', ], 'dnsEntry' => [ 'shape' => 'DnsEntry', ], 'id' => [ 'shape' => 'ServiceId', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'name' => [ 'shape' => 'ServiceName', ], 'status' => [ 'shape' => 'ServiceStatus', ], ], ], 'SharingConfig' => [ 'type' => 'structure', 'members' => [ 'enabled' => [ 'shape' => 'Boolean', ], ], ], 'String' => [ 'type' => 'string', ], 'SubnetId' => [ 'type' => 'string', 'max' => 200, 'min' => 5, ], 'SubnetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetId', ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'Target' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'TargetIdString', ], 'port' => [ 'shape' => 'Port', ], ], ], 'TargetFailure' => [ 'type' => 'structure', 'members' => [ 'failureCode' => [ 'shape' => 'String', ], 'failureMessage' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'String', ], 'port' => [ 'shape' => 'Port', ], ], ], 'TargetFailureList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetFailure', ], ], 'TargetGroupArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:[a-z0-9\\-]+:vpc-lattice:[a-zA-Z0-9\\-]+:\\d{12}:targetgroup/tg-[0-9a-z]{17}$', ], 'TargetGroupConfig' => [ 'type' => 'structure', 'members' => [ 'healthCheck' => [ 'shape' => 'HealthCheckConfig', ], 'ipAddressType' => [ 'shape' => 'IpAddressType', ], 'lambdaEventStructureVersion' => [ 'shape' => 'LambdaEventStructureVersion', ], 'port' => [ 'shape' => 'Port', ], 'protocol' => [ 'shape' => 'TargetGroupProtocol', ], 'protocolVersion' => [ 'shape' => 'TargetGroupProtocolVersion', ], 'vpcIdentifier' => [ 'shape' => 'VpcId', ], ], ], 'TargetGroupId' => [ 'type' => 'string', 'max' => 20, 'min' => 20, 'pattern' => '^tg-[0-9a-z]{17}$', ], 'TargetGroupIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 17, 'pattern' => '^((tg-[0-9a-z]{17})|(arn:[a-z0-9\\-]+:vpc-lattice:[a-zA-Z0-9\\-]+:\\d{12}:targetgroup/tg-[0-9a-z]{17}))$', ], 'TargetGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetGroupSummary', ], ], 'TargetGroupName' => [ 'type' => 'string', 'max' => 128, 'min' => 3, 'pattern' => '^(?!tg-)(?![-])(?!.*[-]$)(?!.*[-]{2})[a-z0-9-]+$', ], 'TargetGroupProtocol' => [ 'type' => 'string', 'enum' => [ 'HTTP', 'HTTPS', 'TCP', ], ], 'TargetGroupProtocolVersion' => [ 'type' => 'string', 'enum' => [ 'HTTP1', 'HTTP2', 'GRPC', ], ], 'TargetGroupStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_IN_PROGRESS', 'ACTIVE', 'DELETE_IN_PROGRESS', 'CREATE_FAILED', 'DELETE_FAILED', ], ], 'TargetGroupSummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'TargetGroupArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'id' => [ 'shape' => 'TargetGroupId', ], 'ipAddressType' => [ 'shape' => 'IpAddressType', ], 'lambdaEventStructureVersion' => [ 'shape' => 'LambdaEventStructureVersion', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'name' => [ 'shape' => 'TargetGroupName', ], 'port' => [ 'shape' => 'Port', ], 'protocol' => [ 'shape' => 'TargetGroupProtocol', ], 'serviceArns' => [ 'shape' => 'ServiceArnList', ], 'status' => [ 'shape' => 'TargetGroupStatus', ], 'type' => [ 'shape' => 'TargetGroupType', ], 'vpcIdentifier' => [ 'shape' => 'VpcId', ], ], ], 'TargetGroupType' => [ 'type' => 'string', 'enum' => [ 'IP', 'LAMBDA', 'INSTANCE', 'ALB', ], ], 'TargetGroupWeight' => [ 'type' => 'integer', 'box' => true, 'max' => 999, 'min' => 0, ], 'TargetIdString' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'TargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Target', ], ], 'TargetStatus' => [ 'type' => 'string', 'enum' => [ 'DRAINING', 'UNAVAILABLE', 'HEALTHY', 'UNHEALTHY', 'INITIAL', 'UNUSED', ], ], 'TargetSummary' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'String', ], 'port' => [ 'shape' => 'Port', ], 'reasonCode' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'TargetStatus', ], ], ], 'TargetSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetSummary', ], ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], 'serviceCode' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'Timestamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'UnhealthyThresholdCount' => [ 'type' => 'integer', 'box' => true, 'max' => 10, 'min' => 0, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeys', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAccessLogSubscriptionRequest' => [ 'type' => 'structure', 'required' => [ 'accessLogSubscriptionIdentifier', 'destinationArn', ], 'members' => [ 'accessLogSubscriptionIdentifier' => [ 'shape' => 'AccessLogSubscriptionIdentifier', 'location' => 'uri', 'locationName' => 'accessLogSubscriptionIdentifier', ], 'destinationArn' => [ 'shape' => 'AccessLogDestinationArn', ], ], ], 'UpdateAccessLogSubscriptionResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'destinationArn', 'id', 'resourceArn', 'resourceId', ], 'members' => [ 'arn' => [ 'shape' => 'AccessLogSubscriptionArn', ], 'destinationArn' => [ 'shape' => 'AccessLogDestinationArn', ], 'id' => [ 'shape' => 'AccessLogSubscriptionId', ], 'resourceArn' => [ 'shape' => 'ResourceArn', ], 'resourceId' => [ 'shape' => 'ResourceId', ], ], ], 'UpdateListenerRequest' => [ 'type' => 'structure', 'required' => [ 'defaultAction', 'listenerIdentifier', 'serviceIdentifier', ], 'members' => [ 'defaultAction' => [ 'shape' => 'RuleAction', ], 'listenerIdentifier' => [ 'shape' => 'ListenerIdentifier', 'location' => 'uri', 'locationName' => 'listenerIdentifier', ], 'serviceIdentifier' => [ 'shape' => 'ServiceIdentifier', 'location' => 'uri', 'locationName' => 'serviceIdentifier', ], ], ], 'UpdateListenerResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ListenerArn', ], 'defaultAction' => [ 'shape' => 'RuleAction', ], 'id' => [ 'shape' => 'ListenerId', ], 'name' => [ 'shape' => 'ListenerName', ], 'port' => [ 'shape' => 'Port', ], 'protocol' => [ 'shape' => 'ListenerProtocol', ], 'serviceArn' => [ 'shape' => 'ServiceArn', ], 'serviceId' => [ 'shape' => 'ServiceId', ], ], ], 'UpdateResourceConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'resourceConfigurationIdentifier', ], 'members' => [ 'allowAssociationToShareableServiceNetwork' => [ 'shape' => 'Boolean', ], 'portRanges' => [ 'shape' => 'PortRangeList', ], 'resourceConfigurationDefinition' => [ 'shape' => 'ResourceConfigurationDefinition', ], 'resourceConfigurationIdentifier' => [ 'shape' => 'ResourceConfigurationIdentifier', 'location' => 'uri', 'locationName' => 'resourceConfigurationIdentifier', ], ], ], 'UpdateResourceConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'allowAssociationToShareableServiceNetwork' => [ 'shape' => 'Boolean', ], 'arn' => [ 'shape' => 'ResourceConfigurationArn', ], 'id' => [ 'shape' => 'ResourceConfigurationId', ], 'name' => [ 'shape' => 'ResourceConfigurationName', ], 'portRanges' => [ 'shape' => 'PortRangeList', ], 'protocol' => [ 'shape' => 'ProtocolType', ], 'resourceConfigurationDefinition' => [ 'shape' => 'ResourceConfigurationDefinition', ], 'resourceConfigurationGroupId' => [ 'shape' => 'ResourceConfigurationId', ], 'resourceGatewayId' => [ 'shape' => 'ResourceGatewayId', ], 'status' => [ 'shape' => 'ResourceConfigurationStatus', ], 'type' => [ 'shape' => 'ResourceConfigurationType', ], ], ], 'UpdateResourceGatewayRequest' => [ 'type' => 'structure', 'required' => [ 'resourceGatewayIdentifier', ], 'members' => [ 'resourceGatewayIdentifier' => [ 'shape' => 'ResourceGatewayIdentifier', 'location' => 'uri', 'locationName' => 'resourceGatewayIdentifier', ], 'securityGroupIds' => [ 'shape' => 'UpdateResourceGatewayRequestSecurityGroupIdsList', ], ], ], 'UpdateResourceGatewayRequestSecurityGroupIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], 'max' => 5, 'min' => 0, ], 'UpdateResourceGatewayResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ResourceGatewayArn', ], 'id' => [ 'shape' => 'ResourceGatewayId', ], 'ipAddressType' => [ 'shape' => 'IpAddressType', ], 'name' => [ 'shape' => 'ResourceGatewayName', ], 'securityGroupIds' => [ 'shape' => 'SecurityGroupList', ], 'status' => [ 'shape' => 'ResourceGatewayStatus', ], 'subnetIds' => [ 'shape' => 'SubnetList', ], 'vpcId' => [ 'shape' => 'VpcId', ], ], ], 'UpdateRuleRequest' => [ 'type' => 'structure', 'required' => [ 'listenerIdentifier', 'ruleIdentifier', 'serviceIdentifier', ], 'members' => [ 'action' => [ 'shape' => 'RuleAction', ], 'listenerIdentifier' => [ 'shape' => 'ListenerIdentifier', 'location' => 'uri', 'locationName' => 'listenerIdentifier', ], 'match' => [ 'shape' => 'RuleMatch', ], 'priority' => [ 'shape' => 'RulePriority', ], 'ruleIdentifier' => [ 'shape' => 'RuleIdentifier', 'location' => 'uri', 'locationName' => 'ruleIdentifier', ], 'serviceIdentifier' => [ 'shape' => 'ServiceIdentifier', 'location' => 'uri', 'locationName' => 'serviceIdentifier', ], ], ], 'UpdateRuleResponse' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'RuleAction', ], 'arn' => [ 'shape' => 'RuleArn', ], 'id' => [ 'shape' => 'RuleId', ], 'isDefault' => [ 'shape' => 'Boolean', ], 'match' => [ 'shape' => 'RuleMatch', ], 'name' => [ 'shape' => 'RuleName', ], 'priority' => [ 'shape' => 'RulePriority', ], ], ], 'UpdateServiceNetworkRequest' => [ 'type' => 'structure', 'required' => [ 'authType', 'serviceNetworkIdentifier', ], 'members' => [ 'authType' => [ 'shape' => 'AuthType', ], 'serviceNetworkIdentifier' => [ 'shape' => 'ServiceNetworkIdentifier', 'location' => 'uri', 'locationName' => 'serviceNetworkIdentifier', ], ], ], 'UpdateServiceNetworkResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ServiceNetworkArn', ], 'authType' => [ 'shape' => 'AuthType', ], 'id' => [ 'shape' => 'ServiceNetworkId', ], 'name' => [ 'shape' => 'ServiceNetworkName', ], ], ], 'UpdateServiceNetworkVpcAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'securityGroupIds', 'serviceNetworkVpcAssociationIdentifier', ], 'members' => [ 'securityGroupIds' => [ 'shape' => 'UpdateServiceNetworkVpcAssociationRequestSecurityGroupIdsList', ], 'serviceNetworkVpcAssociationIdentifier' => [ 'shape' => 'ServiceNetworkVpcAssociationIdentifier', 'location' => 'uri', 'locationName' => 'serviceNetworkVpcAssociationIdentifier', ], ], ], 'UpdateServiceNetworkVpcAssociationRequestSecurityGroupIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], 'max' => 5, 'min' => 1, ], 'UpdateServiceNetworkVpcAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ServiceNetworkVpcAssociationArn', ], 'createdBy' => [ 'shape' => 'AccountId', ], 'id' => [ 'shape' => 'ServiceNetworkVpcAssociationId', ], 'securityGroupIds' => [ 'shape' => 'SecurityGroupList', ], 'status' => [ 'shape' => 'ServiceNetworkVpcAssociationStatus', ], ], ], 'UpdateServiceRequest' => [ 'type' => 'structure', 'required' => [ 'serviceIdentifier', ], 'members' => [ 'authType' => [ 'shape' => 'AuthType', ], 'certificateArn' => [ 'shape' => 'CertificateArn', ], 'serviceIdentifier' => [ 'shape' => 'ServiceIdentifier', 'location' => 'uri', 'locationName' => 'serviceIdentifier', ], ], ], 'UpdateServiceResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ServiceArn', ], 'authType' => [ 'shape' => 'AuthType', ], 'certificateArn' => [ 'shape' => 'CertificateArn', ], 'customDomainName' => [ 'shape' => 'ServiceCustomDomainName', ], 'id' => [ 'shape' => 'ServiceId', ], 'name' => [ 'shape' => 'ServiceName', ], ], ], 'UpdateTargetGroupRequest' => [ 'type' => 'structure', 'required' => [ 'healthCheck', 'targetGroupIdentifier', ], 'members' => [ 'healthCheck' => [ 'shape' => 'HealthCheckConfig', ], 'targetGroupIdentifier' => [ 'shape' => 'TargetGroupIdentifier', 'location' => 'uri', 'locationName' => 'targetGroupIdentifier', ], ], ], 'UpdateTargetGroupResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'TargetGroupArn', ], 'config' => [ 'shape' => 'TargetGroupConfig', ], 'id' => [ 'shape' => 'TargetGroupId', ], 'name' => [ 'shape' => 'TargetGroupName', ], 'status' => [ 'shape' => 'TargetGroupStatus', ], 'type' => [ 'shape' => 'TargetGroupType', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', 'reason', ], 'members' => [ 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], 'message' => [ 'shape' => 'String', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'message', 'name', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'unknownOperation', 'cannotParse', 'fieldValidationFailed', 'other', ], ], 'VpcEndpointId' => [ 'type' => 'string', 'max' => 22, 'min' => 22, 'pattern' => '^vpce-[0-9a-f]{17}$', ], 'VpcEndpointOwner' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '^\\d{12}$', ], 'VpcId' => [ 'type' => 'string', 'max' => 50, 'min' => 5, 'pattern' => '^vpc-(([0-9a-z]{8})|([0-9a-z]{17}))$', ], 'WeightedTargetGroup' => [ 'type' => 'structure', 'required' => [ 'targetGroupIdentifier', ], 'members' => [ 'targetGroupIdentifier' => [ 'shape' => 'TargetGroupIdentifier', ], 'weight' => [ 'shape' => 'TargetGroupWeight', ], ], ], 'WeightedTargetGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WeightedTargetGroup', ], 'max' => 10, 'min' => 1, ], 'WildcardArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:[a-z0-9][-.a-z0-9]{0,62}:[a-z0-9][-.a-z0-9]{0,62}:([a-z0-9][-.a-z0-9]{0,62})?:\\d{12}?:[^/].{0,1023}$', ], ],];
