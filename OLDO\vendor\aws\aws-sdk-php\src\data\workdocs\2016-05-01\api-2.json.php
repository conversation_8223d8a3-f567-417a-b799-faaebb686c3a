<?php
// This file was auto-generated from sdk-root/src/data/workdocs/2016-05-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2016-05-01', 'endpointPrefix' => 'workdocs', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'Amazon WorkDocs', 'serviceId' => 'WorkDocs', 'signatureVersion' => 'v4', 'uid' => 'workdocs-2016-05-01', ], 'operations' => [ 'AbortDocumentVersionUpload' => [ 'name' => 'AbortDocumentVersionUpload', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/api/v1/documents/{DocumentId}/versions/{VersionId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'AbortDocumentVersionUploadRequest', ], 'errors' => [ [ 'shape' => 'EntityNotExistsException', ], [ 'shape' => 'ProhibitedStateException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'ActivateUser' => [ 'name' => 'ActivateUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/api/v1/users/{UserId}/activation', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ActivateUserRequest', ], 'output' => [ 'shape' => 'ActivateUserResponse', ], 'errors' => [ [ 'shape' => 'EntityNotExistsException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'AddResourcePermissions' => [ 'name' => 'AddResourcePermissions', 'http' => [ 'method' => 'POST', 'requestUri' => '/api/v1/resources/{ResourceId}/permissions', 'responseCode' => 201, ], 'input' => [ 'shape' => 'AddResourcePermissionsRequest', ], 'output' => [ 'shape' => 'AddResourcePermissionsResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ProhibitedStateException', ], ], ], 'CreateComment' => [ 'name' => 'CreateComment', 'http' => [ 'method' => 'POST', 'requestUri' => '/api/v1/documents/{DocumentId}/versions/{VersionId}/comment', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateCommentRequest', ], 'output' => [ 'shape' => 'CreateCommentResponse', ], 'errors' => [ [ 'shape' => 'EntityNotExistsException', ], [ 'shape' => 'ProhibitedStateException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'DocumentLockedForCommentsException', ], [ 'shape' => 'InvalidCommentOperationException', ], ], ], 'CreateCustomMetadata' => [ 'name' => 'CreateCustomMetadata', 'http' => [ 'method' => 'PUT', 'requestUri' => '/api/v1/resources/{ResourceId}/customMetadata', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateCustomMetadataRequest', ], 'output' => [ 'shape' => 'CreateCustomMetadataResponse', ], 'errors' => [ [ 'shape' => 'EntityNotExistsException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'ProhibitedStateException', ], [ 'shape' => 'CustomMetadataLimitExceededException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'CreateFolder' => [ 'name' => 'CreateFolder', 'http' => [ 'method' => 'POST', 'requestUri' => '/api/v1/folders', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateFolderRequest', ], 'output' => [ 'shape' => 'CreateFolderResponse', ], 'errors' => [ [ 'shape' => 'EntityNotExistsException', ], [ 'shape' => 'EntityAlreadyExistsException', ], [ 'shape' => 'ProhibitedStateException', ], [ 'shape' => 'ConflictingOperationException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'CreateLabels' => [ 'name' => 'CreateLabels', 'http' => [ 'method' => 'PUT', 'requestUri' => '/api/v1/resources/{ResourceId}/labels', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateLabelsRequest', ], 'output' => [ 'shape' => 'CreateLabelsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotExistsException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'TooManyLabelsException', ], ], ], 'CreateNotificationSubscription' => [ 'name' => 'CreateNotificationSubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/api/v1/organizations/{OrganizationId}/subscriptions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateNotificationSubscriptionRequest', ], 'output' => [ 'shape' => 'CreateNotificationSubscriptionResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'TooManySubscriptionsException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidArgumentException', ], ], ], 'CreateUser' => [ 'name' => 'CreateUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/api/v1/users', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateUserRequest', ], 'output' => [ 'shape' => 'CreateUserResponse', ], 'errors' => [ [ 'shape' => 'EntityAlreadyExistsException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DeactivateUser' => [ 'name' => 'DeactivateUser', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/api/v1/users/{UserId}/activation', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeactivateUserRequest', ], 'errors' => [ [ 'shape' => 'EntityNotExistsException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DeleteComment' => [ 'name' => 'DeleteComment', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/api/v1/documents/{DocumentId}/versions/{VersionId}/comment/{CommentId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteCommentRequest', ], 'errors' => [ [ 'shape' => 'EntityNotExistsException', ], [ 'shape' => 'ProhibitedStateException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'DocumentLockedForCommentsException', ], ], ], 'DeleteCustomMetadata' => [ 'name' => 'DeleteCustomMetadata', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/api/v1/resources/{ResourceId}/customMetadata', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteCustomMetadataRequest', ], 'output' => [ 'shape' => 'DeleteCustomMetadataResponse', ], 'errors' => [ [ 'shape' => 'EntityNotExistsException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'ProhibitedStateException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DeleteDocument' => [ 'name' => 'DeleteDocument', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/api/v1/documents/{DocumentId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteDocumentRequest', ], 'errors' => [ [ 'shape' => 'EntityNotExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ProhibitedStateException', ], [ 'shape' => 'ConflictingOperationException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DeleteDocumentVersion' => [ 'name' => 'DeleteDocumentVersion', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/api/v1/documentVersions/{DocumentId}/versions/{VersionId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteDocumentVersionRequest', ], 'errors' => [ [ 'shape' => 'EntityNotExistsException', ], [ 'shape' => 'ProhibitedStateException', ], [ 'shape' => 'ConflictingOperationException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'UnauthorizedOperationException', ], ], ], 'DeleteFolder' => [ 'name' => 'DeleteFolder', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/api/v1/folders/{FolderId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteFolderRequest', ], 'errors' => [ [ 'shape' => 'EntityNotExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ProhibitedStateException', ], [ 'shape' => 'ConflictingOperationException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DeleteFolderContents' => [ 'name' => 'DeleteFolderContents', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/api/v1/folders/{FolderId}/contents', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteFolderContentsRequest', ], 'errors' => [ [ 'shape' => 'EntityNotExistsException', ], [ 'shape' => 'ProhibitedStateException', ], [ 'shape' => 'ConflictingOperationException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DeleteLabels' => [ 'name' => 'DeleteLabels', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/api/v1/resources/{ResourceId}/labels', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteLabelsRequest', ], 'output' => [ 'shape' => 'DeleteLabelsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotExistsException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ProhibitedStateException', ], ], ], 'DeleteNotificationSubscription' => [ 'name' => 'DeleteNotificationSubscription', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/api/v1/organizations/{OrganizationId}/subscriptions/{SubscriptionId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteNotificationSubscriptionRequest', ], 'errors' => [ [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'EntityNotExistsException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ProhibitedStateException', ], ], ], 'DeleteUser' => [ 'name' => 'DeleteUser', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/api/v1/users/{UserId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteUserRequest', ], 'errors' => [ [ 'shape' => 'EntityNotExistsException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeActivities' => [ 'name' => 'DescribeActivities', 'http' => [ 'method' => 'GET', 'requestUri' => '/api/v1/activities', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeActivitiesRequest', ], 'output' => [ 'shape' => 'DescribeActivitiesResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeComments' => [ 'name' => 'DescribeComments', 'http' => [ 'method' => 'GET', 'requestUri' => '/api/v1/documents/{DocumentId}/versions/{VersionId}/comments', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeCommentsRequest', ], 'output' => [ 'shape' => 'DescribeCommentsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotExistsException', ], [ 'shape' => 'ProhibitedStateException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeDocumentVersions' => [ 'name' => 'DescribeDocumentVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/api/v1/documents/{DocumentId}/versions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeDocumentVersionsRequest', ], 'output' => [ 'shape' => 'DescribeDocumentVersionsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotExistsException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ProhibitedStateException', ], [ 'shape' => 'InvalidPasswordException', ], ], ], 'DescribeFolderContents' => [ 'name' => 'DescribeFolderContents', 'http' => [ 'method' => 'GET', 'requestUri' => '/api/v1/folders/{FolderId}/contents', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeFolderContentsRequest', ], 'output' => [ 'shape' => 'DescribeFolderContentsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotExistsException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ProhibitedStateException', ], ], ], 'DescribeGroups' => [ 'name' => 'DescribeGroups', 'http' => [ 'method' => 'GET', 'requestUri' => '/api/v1/groups', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeGroupsRequest', ], 'output' => [ 'shape' => 'DescribeGroupsResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeNotificationSubscriptions' => [ 'name' => 'DescribeNotificationSubscriptions', 'http' => [ 'method' => 'GET', 'requestUri' => '/api/v1/organizations/{OrganizationId}/subscriptions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeNotificationSubscriptionsRequest', ], 'output' => [ 'shape' => 'DescribeNotificationSubscriptionsResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'EntityNotExistsException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeResourcePermissions' => [ 'name' => 'DescribeResourcePermissions', 'http' => [ 'method' => 'GET', 'requestUri' => '/api/v1/resources/{ResourceId}/permissions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeResourcePermissionsRequest', ], 'output' => [ 'shape' => 'DescribeResourcePermissionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeRootFolders' => [ 'name' => 'DescribeRootFolders', 'http' => [ 'method' => 'GET', 'requestUri' => '/api/v1/me/root', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeRootFoldersRequest', ], 'output' => [ 'shape' => 'DescribeRootFoldersResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeUsers' => [ 'name' => 'DescribeUsers', 'http' => [ 'method' => 'GET', 'requestUri' => '/api/v1/users', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeUsersRequest', ], 'output' => [ 'shape' => 'DescribeUsersResponse', ], 'errors' => [ [ 'shape' => 'EntityNotExistsException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'RequestedEntityTooLargeException', ], ], ], 'GetCurrentUser' => [ 'name' => 'GetCurrentUser', 'http' => [ 'method' => 'GET', 'requestUri' => '/api/v1/me', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCurrentUserRequest', ], 'output' => [ 'shape' => 'GetCurrentUserResponse', ], 'errors' => [ [ 'shape' => 'EntityNotExistsException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'GetDocument' => [ 'name' => 'GetDocument', 'http' => [ 'method' => 'GET', 'requestUri' => '/api/v1/documents/{DocumentId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDocumentRequest', ], 'output' => [ 'shape' => 'GetDocumentResponse', ], 'errors' => [ [ 'shape' => 'EntityNotExistsException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidPasswordException', ], ], ], 'GetDocumentPath' => [ 'name' => 'GetDocumentPath', 'http' => [ 'method' => 'GET', 'requestUri' => '/api/v1/documents/{DocumentId}/path', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDocumentPathRequest', ], 'output' => [ 'shape' => 'GetDocumentPathResponse', ], 'errors' => [ [ 'shape' => 'EntityNotExistsException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'GetDocumentVersion' => [ 'name' => 'GetDocumentVersion', 'http' => [ 'method' => 'GET', 'requestUri' => '/api/v1/documents/{DocumentId}/versions/{VersionId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDocumentVersionRequest', ], 'output' => [ 'shape' => 'GetDocumentVersionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotExistsException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ProhibitedStateException', ], [ 'shape' => 'InvalidPasswordException', ], ], ], 'GetFolder' => [ 'name' => 'GetFolder', 'http' => [ 'method' => 'GET', 'requestUri' => '/api/v1/folders/{FolderId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFolderRequest', ], 'output' => [ 'shape' => 'GetFolderResponse', ], 'errors' => [ [ 'shape' => 'EntityNotExistsException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ProhibitedStateException', ], ], ], 'GetFolderPath' => [ 'name' => 'GetFolderPath', 'http' => [ 'method' => 'GET', 'requestUri' => '/api/v1/folders/{FolderId}/path', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFolderPathRequest', ], 'output' => [ 'shape' => 'GetFolderPathResponse', ], 'errors' => [ [ 'shape' => 'EntityNotExistsException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'GetResources' => [ 'name' => 'GetResources', 'http' => [ 'method' => 'GET', 'requestUri' => '/api/v1/resources', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetResourcesRequest', ], 'output' => [ 'shape' => 'GetResourcesResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'InitiateDocumentVersionUpload' => [ 'name' => 'InitiateDocumentVersionUpload', 'http' => [ 'method' => 'POST', 'requestUri' => '/api/v1/documents', 'responseCode' => 201, ], 'input' => [ 'shape' => 'InitiateDocumentVersionUploadRequest', ], 'output' => [ 'shape' => 'InitiateDocumentVersionUploadResponse', ], 'errors' => [ [ 'shape' => 'EntityNotExistsException', ], [ 'shape' => 'EntityAlreadyExistsException', ], [ 'shape' => 'StorageLimitExceededException', ], [ 'shape' => 'StorageLimitWillExceedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ProhibitedStateException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'DraftUploadOutOfSyncException', ], [ 'shape' => 'ResourceAlreadyCheckedOutException', ], [ 'shape' => 'InvalidPasswordException', ], [ 'shape' => 'InvalidArgumentException', ], ], ], 'RemoveAllResourcePermissions' => [ 'name' => 'RemoveAllResourcePermissions', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/api/v1/resources/{ResourceId}/permissions', 'responseCode' => 204, ], 'input' => [ 'shape' => 'RemoveAllResourcePermissionsRequest', ], 'errors' => [ [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'RemoveResourcePermission' => [ 'name' => 'RemoveResourcePermission', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/api/v1/resources/{ResourceId}/permissions/{PrincipalId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'RemoveResourcePermissionRequest', ], 'errors' => [ [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'RestoreDocumentVersions' => [ 'name' => 'RestoreDocumentVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/api/v1/documentVersions/restore/{DocumentId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'RestoreDocumentVersionsRequest', ], 'errors' => [ [ 'shape' => 'EntityNotExistsException', ], [ 'shape' => 'ProhibitedStateException', ], [ 'shape' => 'ConflictingOperationException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'UnauthorizedOperationException', ], ], ], 'SearchResources' => [ 'name' => 'SearchResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/api/v1/search', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchResourcesRequest', ], 'output' => [ 'shape' => 'SearchResourcesResponse', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'UpdateDocument' => [ 'name' => 'UpdateDocument', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/api/v1/documents/{DocumentId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateDocumentRequest', ], 'errors' => [ [ 'shape' => 'EntityNotExistsException', ], [ 'shape' => 'EntityAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ProhibitedStateException', ], [ 'shape' => 'ConflictingOperationException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'UpdateDocumentVersion' => [ 'name' => 'UpdateDocumentVersion', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/api/v1/documents/{DocumentId}/versions/{VersionId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateDocumentVersionRequest', ], 'errors' => [ [ 'shape' => 'EntityNotExistsException', ], [ 'shape' => 'ProhibitedStateException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'UpdateFolder' => [ 'name' => 'UpdateFolder', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/api/v1/folders/{FolderId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateFolderRequest', ], 'errors' => [ [ 'shape' => 'EntityNotExistsException', ], [ 'shape' => 'EntityAlreadyExistsException', ], [ 'shape' => 'ProhibitedStateException', ], [ 'shape' => 'ConflictingOperationException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'UpdateUser' => [ 'name' => 'UpdateUser', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/api/v1/users/{UserId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateUserRequest', ], 'output' => [ 'shape' => 'UpdateUserResponse', ], 'errors' => [ [ 'shape' => 'EntityNotExistsException', ], [ 'shape' => 'UnauthorizedOperationException', ], [ 'shape' => 'UnauthorizedResourceAccessException', ], [ 'shape' => 'IllegalUserStateException', ], [ 'shape' => 'ProhibitedStateException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'DeactivatingLastSystemUserException', ], [ 'shape' => 'InvalidArgumentException', ], ], ], ], 'shapes' => [ 'AbortDocumentVersionUploadRequest' => [ 'type' => 'structure', 'required' => [ 'DocumentId', 'VersionId', ], 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'DocumentId' => [ 'shape' => 'ResourceIdType', 'location' => 'uri', 'locationName' => 'DocumentId', ], 'VersionId' => [ 'shape' => 'DocumentVersionIdType', 'location' => 'uri', 'locationName' => 'VersionId', ], ], ], 'ActivateUserRequest' => [ 'type' => 'structure', 'required' => [ 'UserId', ], 'members' => [ 'UserId' => [ 'shape' => 'IdType', 'location' => 'uri', 'locationName' => 'UserId', ], 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], ], ], 'ActivateUserResponse' => [ 'type' => 'structure', 'members' => [ 'User' => [ 'shape' => 'User', ], ], ], 'Activity' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'ActivityType', ], 'TimeStamp' => [ 'shape' => 'TimestampType', ], 'IsIndirectActivity' => [ 'shape' => 'BooleanType', ], 'OrganizationId' => [ 'shape' => 'IdType', ], 'Initiator' => [ 'shape' => 'UserMetadata', ], 'Participants' => [ 'shape' => 'Participants', ], 'ResourceMetadata' => [ 'shape' => 'ResourceMetadata', ], 'OriginalParent' => [ 'shape' => 'ResourceMetadata', ], 'CommentMetadata' => [ 'shape' => 'CommentMetadata', ], ], ], 'ActivityNamesFilterType' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[\\w,]+', ], 'ActivityType' => [ 'type' => 'string', 'enum' => [ 'DOCUMENT_CHECKED_IN', 'DOCUMENT_CHECKED_OUT', 'DOCUMENT_RENAMED', 'DOCUMENT_VERSION_UPLOADED', 'DOCUMENT_VERSION_DELETED', 'DOCUMENT_VERSION_VIEWED', 'DOCUMENT_VERSION_DOWNLOADED', 'DOCUMENT_RECYCLED', 'DOCUMENT_RESTORED', 'DOCUMENT_REVERTED', 'DOCUMENT_SHARED', 'DOCUMENT_UNSHARED', 'DOCUMENT_SHARE_PERMISSION_CHANGED', 'DOCUMENT_SHAREABLE_LINK_CREATED', 'DOCUMENT_SHAREABLE_LINK_REMOVED', 'DOCUMENT_SHAREABLE_LINK_PERMISSION_CHANGED', 'DOCUMENT_MOVED', 'DOCUMENT_COMMENT_ADDED', 'DOCUMENT_COMMENT_DELETED', 'DOCUMENT_ANNOTATION_ADDED', 'DOCUMENT_ANNOTATION_DELETED', 'FOLDER_CREATED', 'FOLDER_DELETED', 'FOLDER_RENAMED', 'FOLDER_RECYCLED', 'FOLDER_RESTORED', 'FOLDER_SHARED', 'FOLDER_UNSHARED', 'FOLDER_SHARE_PERMISSION_CHANGED', 'FOLDER_SHAREABLE_LINK_CREATED', 'FOLDER_SHAREABLE_LINK_REMOVED', 'FOLDER_SHAREABLE_LINK_PERMISSION_CHANGED', 'FOLDER_MOVED', ], ], 'AddResourcePermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', 'Principals', ], 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'ResourceId' => [ 'shape' => 'ResourceIdType', 'location' => 'uri', 'locationName' => 'ResourceId', ], 'Principals' => [ 'shape' => 'SharePrincipalList', ], 'NotificationOptions' => [ 'shape' => 'NotificationOptions', ], ], ], 'AddResourcePermissionsResponse' => [ 'type' => 'structure', 'members' => [ 'ShareResults' => [ 'shape' => 'ShareResultsList', ], ], ], 'AdditionalResponseFieldType' => [ 'type' => 'string', 'enum' => [ 'WEBURL', ], ], 'AdditionalResponseFieldsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AdditionalResponseFieldType', ], ], 'AuthenticationHeaderType' => [ 'type' => 'string', 'max' => 8199, 'min' => 1, 'sensitive' => true, ], 'BooleanEnumType' => [ 'type' => 'string', 'enum' => [ 'TRUE', 'FALSE', ], ], 'BooleanType' => [ 'type' => 'boolean', ], 'Comment' => [ 'type' => 'structure', 'required' => [ 'CommentId', ], 'members' => [ 'CommentId' => [ 'shape' => 'CommentIdType', ], 'ParentId' => [ 'shape' => 'CommentIdType', ], 'ThreadId' => [ 'shape' => 'CommentIdType', ], 'Text' => [ 'shape' => 'CommentTextType', ], 'Contributor' => [ 'shape' => 'User', ], 'CreatedTimestamp' => [ 'shape' => 'TimestampType', ], 'Status' => [ 'shape' => 'CommentStatusType', ], 'Visibility' => [ 'shape' => 'CommentVisibilityType', ], 'RecipientId' => [ 'shape' => 'IdType', ], ], ], 'CommentIdType' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\w+-.@]+', ], 'CommentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Comment', ], ], 'CommentMetadata' => [ 'type' => 'structure', 'members' => [ 'CommentId' => [ 'shape' => 'CommentIdType', ], 'Contributor' => [ 'shape' => 'User', ], 'CreatedTimestamp' => [ 'shape' => 'TimestampType', ], 'CommentStatus' => [ 'shape' => 'CommentStatusType', ], 'RecipientId' => [ 'shape' => 'IdType', ], 'ContributorId' => [ 'shape' => 'IdType', ], ], ], 'CommentStatusType' => [ 'type' => 'string', 'enum' => [ 'DRAFT', 'PUBLISHED', 'DELETED', ], ], 'CommentTextType' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'sensitive' => true, ], 'CommentVisibilityType' => [ 'type' => 'string', 'enum' => [ 'PUBLIC', 'PRIVATE', ], ], 'ConcurrentModificationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessageType', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ConflictingOperationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessageType', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ContentCategoryType' => [ 'type' => 'string', 'enum' => [ 'IMAGE', 'DOCUMENT', 'PDF', 'SPREADSHEET', 'PRESENTATION', 'AUDIO', 'VIDEO', 'SOURCE_CODE', 'OTHER', ], ], 'CreateCommentRequest' => [ 'type' => 'structure', 'required' => [ 'DocumentId', 'VersionId', 'Text', ], 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'DocumentId' => [ 'shape' => 'ResourceIdType', 'location' => 'uri', 'locationName' => 'DocumentId', ], 'VersionId' => [ 'shape' => 'DocumentVersionIdType', 'location' => 'uri', 'locationName' => 'VersionId', ], 'ParentId' => [ 'shape' => 'CommentIdType', ], 'ThreadId' => [ 'shape' => 'CommentIdType', ], 'Text' => [ 'shape' => 'CommentTextType', ], 'Visibility' => [ 'shape' => 'CommentVisibilityType', ], 'NotifyCollaborators' => [ 'shape' => 'BooleanType', ], ], ], 'CreateCommentResponse' => [ 'type' => 'structure', 'members' => [ 'Comment' => [ 'shape' => 'Comment', ], ], ], 'CreateCustomMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', 'CustomMetadata', ], 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'ResourceId' => [ 'shape' => 'ResourceIdType', 'location' => 'uri', 'locationName' => 'ResourceId', ], 'VersionId' => [ 'shape' => 'DocumentVersionIdType', 'location' => 'querystring', 'locationName' => 'versionid', ], 'CustomMetadata' => [ 'shape' => 'CustomMetadataMap', ], ], ], 'CreateCustomMetadataResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateFolderRequest' => [ 'type' => 'structure', 'required' => [ 'ParentFolderId', ], 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'Name' => [ 'shape' => 'ResourceNameType', ], 'ParentFolderId' => [ 'shape' => 'ResourceIdType', ], ], ], 'CreateFolderResponse' => [ 'type' => 'structure', 'members' => [ 'Metadata' => [ 'shape' => 'FolderMetadata', ], ], ], 'CreateLabelsRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', 'Labels', ], 'members' => [ 'ResourceId' => [ 'shape' => 'ResourceIdType', 'location' => 'uri', 'locationName' => 'ResourceId', ], 'Labels' => [ 'shape' => 'SharedLabels', ], 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], ], ], 'CreateLabelsResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateNotificationSubscriptionRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'Endpoint', 'Protocol', 'SubscriptionType', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'IdType', 'location' => 'uri', 'locationName' => 'OrganizationId', ], 'Endpoint' => [ 'shape' => 'SubscriptionEndPointType', ], 'Protocol' => [ 'shape' => 'SubscriptionProtocolType', ], 'SubscriptionType' => [ 'shape' => 'SubscriptionType', ], ], ], 'CreateNotificationSubscriptionResponse' => [ 'type' => 'structure', 'members' => [ 'Subscription' => [ 'shape' => 'Subscription', ], ], ], 'CreateUserRequest' => [ 'type' => 'structure', 'required' => [ 'Username', 'GivenName', 'Surname', 'Password', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'IdType', ], 'Username' => [ 'shape' => 'UsernameType', ], 'EmailAddress' => [ 'shape' => 'EmailAddressType', ], 'GivenName' => [ 'shape' => 'UserAttributeValueType', ], 'Surname' => [ 'shape' => 'UserAttributeValueType', ], 'Password' => [ 'shape' => 'PasswordType', ], 'TimeZoneId' => [ 'shape' => 'TimeZoneIdType', ], 'StorageRule' => [ 'shape' => 'StorageRuleType', ], 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], ], ], 'CreateUserResponse' => [ 'type' => 'structure', 'members' => [ 'User' => [ 'shape' => 'User', ], ], ], 'CustomMetadataKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomMetadataKeyType', ], 'max' => 8, ], 'CustomMetadataKeyType' => [ 'type' => 'string', 'max' => 56, 'min' => 1, 'pattern' => '[a-zA-Z0-9._+-/=][a-zA-Z0-9 ._+-/=]*', ], 'CustomMetadataLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessageType', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'CustomMetadataMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'CustomMetadataKeyType', ], 'value' => [ 'shape' => 'CustomMetadataValueType', ], 'max' => 8, 'min' => 1, ], 'CustomMetadataValueType' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z0-9._+-/=][a-zA-Z0-9 ._+-/=]*', ], 'DateRangeType' => [ 'type' => 'structure', 'members' => [ 'StartValue' => [ 'shape' => 'TimestampType', ], 'EndValue' => [ 'shape' => 'TimestampType', ], ], ], 'DeactivateUserRequest' => [ 'type' => 'structure', 'required' => [ 'UserId', ], 'members' => [ 'UserId' => [ 'shape' => 'IdType', 'location' => 'uri', 'locationName' => 'UserId', ], 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], ], ], 'DeactivatingLastSystemUserException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'DeleteCommentRequest' => [ 'type' => 'structure', 'required' => [ 'DocumentId', 'VersionId', 'CommentId', ], 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'DocumentId' => [ 'shape' => 'ResourceIdType', 'location' => 'uri', 'locationName' => 'DocumentId', ], 'VersionId' => [ 'shape' => 'DocumentVersionIdType', 'location' => 'uri', 'locationName' => 'VersionId', ], 'CommentId' => [ 'shape' => 'CommentIdType', 'location' => 'uri', 'locationName' => 'CommentId', ], ], ], 'DeleteCustomMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', ], 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'ResourceId' => [ 'shape' => 'ResourceIdType', 'location' => 'uri', 'locationName' => 'ResourceId', ], 'VersionId' => [ 'shape' => 'DocumentVersionIdType', 'location' => 'querystring', 'locationName' => 'versionId', ], 'Keys' => [ 'shape' => 'CustomMetadataKeyList', 'location' => 'querystring', 'locationName' => 'keys', ], 'DeleteAll' => [ 'shape' => 'BooleanType', 'location' => 'querystring', 'locationName' => 'deleteAll', ], ], ], 'DeleteCustomMetadataResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDocumentRequest' => [ 'type' => 'structure', 'required' => [ 'DocumentId', ], 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'DocumentId' => [ 'shape' => 'ResourceIdType', 'location' => 'uri', 'locationName' => 'DocumentId', ], ], ], 'DeleteDocumentVersionRequest' => [ 'type' => 'structure', 'required' => [ 'DocumentId', 'VersionId', 'DeletePriorVersions', ], 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'DocumentId' => [ 'shape' => 'ResourceIdType', 'location' => 'uri', 'locationName' => 'DocumentId', ], 'VersionId' => [ 'shape' => 'DocumentVersionIdType', 'location' => 'uri', 'locationName' => 'VersionId', ], 'DeletePriorVersions' => [ 'shape' => 'BooleanType', 'location' => 'querystring', 'locationName' => 'deletePriorVersions', ], ], ], 'DeleteFolderContentsRequest' => [ 'type' => 'structure', 'required' => [ 'FolderId', ], 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'FolderId' => [ 'shape' => 'ResourceIdType', 'location' => 'uri', 'locationName' => 'FolderId', ], ], ], 'DeleteFolderRequest' => [ 'type' => 'structure', 'required' => [ 'FolderId', ], 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'FolderId' => [ 'shape' => 'ResourceIdType', 'location' => 'uri', 'locationName' => 'FolderId', ], ], ], 'DeleteLabelsRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', ], 'members' => [ 'ResourceId' => [ 'shape' => 'ResourceIdType', 'location' => 'uri', 'locationName' => 'ResourceId', ], 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'Labels' => [ 'shape' => 'SharedLabels', 'location' => 'querystring', 'locationName' => 'labels', ], 'DeleteAll' => [ 'shape' => 'BooleanType', 'location' => 'querystring', 'locationName' => 'deleteAll', ], ], ], 'DeleteLabelsResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteNotificationSubscriptionRequest' => [ 'type' => 'structure', 'required' => [ 'SubscriptionId', 'OrganizationId', ], 'members' => [ 'SubscriptionId' => [ 'shape' => 'IdType', 'location' => 'uri', 'locationName' => 'SubscriptionId', ], 'OrganizationId' => [ 'shape' => 'IdType', 'location' => 'uri', 'locationName' => 'OrganizationId', ], ], ], 'DeleteUserRequest' => [ 'type' => 'structure', 'required' => [ 'UserId', ], 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'UserId' => [ 'shape' => 'IdType', 'location' => 'uri', 'locationName' => 'UserId', ], ], ], 'DescribeActivitiesRequest' => [ 'type' => 'structure', 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'StartTime' => [ 'shape' => 'TimestampType', 'location' => 'querystring', 'locationName' => 'startTime', ], 'EndTime' => [ 'shape' => 'TimestampType', 'location' => 'querystring', 'locationName' => 'endTime', ], 'OrganizationId' => [ 'shape' => 'IdType', 'location' => 'querystring', 'locationName' => 'organizationId', ], 'ActivityTypes' => [ 'shape' => 'ActivityNamesFilterType', 'location' => 'querystring', 'locationName' => 'activityTypes', ], 'ResourceId' => [ 'shape' => 'IdType', 'location' => 'querystring', 'locationName' => 'resourceId', ], 'UserId' => [ 'shape' => 'IdType', 'location' => 'querystring', 'locationName' => 'userId', ], 'IncludeIndirectActivities' => [ 'shape' => 'BooleanType', 'location' => 'querystring', 'locationName' => 'includeIndirectActivities', ], 'Limit' => [ 'shape' => 'LimitType', 'location' => 'querystring', 'locationName' => 'limit', ], 'Marker' => [ 'shape' => 'SearchMarkerType', 'location' => 'querystring', 'locationName' => 'marker', ], ], ], 'DescribeActivitiesResponse' => [ 'type' => 'structure', 'members' => [ 'UserActivities' => [ 'shape' => 'UserActivities', ], 'Marker' => [ 'shape' => 'SearchMarkerType', ], ], ], 'DescribeCommentsRequest' => [ 'type' => 'structure', 'required' => [ 'DocumentId', 'VersionId', ], 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'DocumentId' => [ 'shape' => 'ResourceIdType', 'location' => 'uri', 'locationName' => 'DocumentId', ], 'VersionId' => [ 'shape' => 'DocumentVersionIdType', 'location' => 'uri', 'locationName' => 'VersionId', ], 'Limit' => [ 'shape' => 'LimitType', 'location' => 'querystring', 'locationName' => 'limit', ], 'Marker' => [ 'shape' => 'MarkerType', 'location' => 'querystring', 'locationName' => 'marker', ], ], ], 'DescribeCommentsResponse' => [ 'type' => 'structure', 'members' => [ 'Comments' => [ 'shape' => 'CommentList', ], 'Marker' => [ 'shape' => 'MarkerType', ], ], ], 'DescribeDocumentVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'DocumentId', ], 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'DocumentId' => [ 'shape' => 'ResourceIdType', 'location' => 'uri', 'locationName' => 'DocumentId', ], 'Marker' => [ 'shape' => 'PageMarkerType', 'location' => 'querystring', 'locationName' => 'marker', ], 'Limit' => [ 'shape' => 'LimitType', 'location' => 'querystring', 'locationName' => 'limit', ], 'Include' => [ 'shape' => 'FieldNamesType', 'location' => 'querystring', 'locationName' => 'include', ], 'Fields' => [ 'shape' => 'FieldNamesType', 'location' => 'querystring', 'locationName' => 'fields', ], ], ], 'DescribeDocumentVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'DocumentVersions' => [ 'shape' => 'DocumentVersionMetadataList', ], 'Marker' => [ 'shape' => 'PageMarkerType', ], ], ], 'DescribeFolderContentsRequest' => [ 'type' => 'structure', 'required' => [ 'FolderId', ], 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'FolderId' => [ 'shape' => 'ResourceIdType', 'location' => 'uri', 'locationName' => 'FolderId', ], 'Sort' => [ 'shape' => 'ResourceSortType', 'location' => 'querystring', 'locationName' => 'sort', ], 'Order' => [ 'shape' => 'OrderType', 'location' => 'querystring', 'locationName' => 'order', ], 'Limit' => [ 'shape' => 'LimitType', 'location' => 'querystring', 'locationName' => 'limit', ], 'Marker' => [ 'shape' => 'PageMarkerType', 'location' => 'querystring', 'locationName' => 'marker', ], 'Type' => [ 'shape' => 'FolderContentType', 'location' => 'querystring', 'locationName' => 'type', ], 'Include' => [ 'shape' => 'FieldNamesType', 'location' => 'querystring', 'locationName' => 'include', ], ], ], 'DescribeFolderContentsResponse' => [ 'type' => 'structure', 'members' => [ 'Folders' => [ 'shape' => 'FolderMetadataList', ], 'Documents' => [ 'shape' => 'DocumentMetadataList', ], 'Marker' => [ 'shape' => 'PageMarkerType', ], ], ], 'DescribeGroupsRequest' => [ 'type' => 'structure', 'required' => [ 'SearchQuery', ], 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'SearchQuery' => [ 'shape' => 'SearchQueryType', 'location' => 'querystring', 'locationName' => 'searchQuery', ], 'OrganizationId' => [ 'shape' => 'IdType', 'location' => 'querystring', 'locationName' => 'organizationId', ], 'Marker' => [ 'shape' => 'MarkerType', 'location' => 'querystring', 'locationName' => 'marker', ], 'Limit' => [ 'shape' => 'PositiveIntegerType', 'location' => 'querystring', 'locationName' => 'limit', ], ], ], 'DescribeGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'Groups' => [ 'shape' => 'GroupMetadataList', ], 'Marker' => [ 'shape' => 'MarkerType', ], ], ], 'DescribeNotificationSubscriptionsRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'IdType', 'location' => 'uri', 'locationName' => 'OrganizationId', ], 'Marker' => [ 'shape' => 'PageMarkerType', 'location' => 'querystring', 'locationName' => 'marker', ], 'Limit' => [ 'shape' => 'LimitType', 'location' => 'querystring', 'locationName' => 'limit', ], ], ], 'DescribeNotificationSubscriptionsResponse' => [ 'type' => 'structure', 'members' => [ 'Subscriptions' => [ 'shape' => 'SubscriptionList', ], 'Marker' => [ 'shape' => 'PageMarkerType', ], ], ], 'DescribeResourcePermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', ], 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'ResourceId' => [ 'shape' => 'ResourceIdType', 'location' => 'uri', 'locationName' => 'ResourceId', ], 'PrincipalId' => [ 'shape' => 'IdType', 'location' => 'querystring', 'locationName' => 'principalId', ], 'Limit' => [ 'shape' => 'LimitType', 'location' => 'querystring', 'locationName' => 'limit', ], 'Marker' => [ 'shape' => 'PageMarkerType', 'location' => 'querystring', 'locationName' => 'marker', ], ], ], 'DescribeResourcePermissionsResponse' => [ 'type' => 'structure', 'members' => [ 'Principals' => [ 'shape' => 'PrincipalList', ], 'Marker' => [ 'shape' => 'PageMarkerType', ], ], ], 'DescribeRootFoldersRequest' => [ 'type' => 'structure', 'required' => [ 'AuthenticationToken', ], 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'Limit' => [ 'shape' => 'LimitType', 'location' => 'querystring', 'locationName' => 'limit', ], 'Marker' => [ 'shape' => 'PageMarkerType', 'location' => 'querystring', 'locationName' => 'marker', ], ], ], 'DescribeRootFoldersResponse' => [ 'type' => 'structure', 'members' => [ 'Folders' => [ 'shape' => 'FolderMetadataList', ], 'Marker' => [ 'shape' => 'PageMarkerType', ], ], ], 'DescribeUsersRequest' => [ 'type' => 'structure', 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'OrganizationId' => [ 'shape' => 'IdType', 'location' => 'querystring', 'locationName' => 'organizationId', ], 'UserIds' => [ 'shape' => 'UserIdsType', 'location' => 'querystring', 'locationName' => 'userIds', ], 'Query' => [ 'shape' => 'SearchQueryType', 'location' => 'querystring', 'locationName' => 'query', ], 'Include' => [ 'shape' => 'UserFilterType', 'location' => 'querystring', 'locationName' => 'include', ], 'Order' => [ 'shape' => 'OrderType', 'location' => 'querystring', 'locationName' => 'order', ], 'Sort' => [ 'shape' => 'UserSortType', 'location' => 'querystring', 'locationName' => 'sort', ], 'Marker' => [ 'shape' => 'PageMarkerType', 'location' => 'querystring', 'locationName' => 'marker', ], 'Limit' => [ 'shape' => 'LimitType', 'location' => 'querystring', 'locationName' => 'limit', ], 'Fields' => [ 'shape' => 'FieldNamesType', 'location' => 'querystring', 'locationName' => 'fields', ], ], ], 'DescribeUsersResponse' => [ 'type' => 'structure', 'members' => [ 'Users' => [ 'shape' => 'OrganizationUserList', ], 'TotalNumberOfUsers' => [ 'shape' => 'SizeType', 'deprecated' => true, ], 'Marker' => [ 'shape' => 'PageMarkerType', ], ], ], 'DocumentContentType' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'DocumentLockedForCommentsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessageType', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'DocumentMetadata' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ResourceIdType', ], 'CreatorId' => [ 'shape' => 'IdType', ], 'ParentFolderId' => [ 'shape' => 'ResourceIdType', ], 'CreatedTimestamp' => [ 'shape' => 'TimestampType', ], 'ModifiedTimestamp' => [ 'shape' => 'TimestampType', ], 'LatestVersionMetadata' => [ 'shape' => 'DocumentVersionMetadata', ], 'ResourceState' => [ 'shape' => 'ResourceStateType', ], 'Labels' => [ 'shape' => 'SharedLabels', ], ], ], 'DocumentMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentMetadata', ], ], 'DocumentSourceType' => [ 'type' => 'string', 'enum' => [ 'ORIGINAL', 'WITH_COMMENTS', ], ], 'DocumentSourceUrlMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'DocumentSourceType', ], 'value' => [ 'shape' => 'UrlType', ], ], 'DocumentStatusType' => [ 'type' => 'string', 'enum' => [ 'INITIALIZED', 'ACTIVE', ], ], 'DocumentThumbnailType' => [ 'type' => 'string', 'enum' => [ 'SMALL', 'SMALL_HQ', 'LARGE', ], ], 'DocumentThumbnailUrlMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'DocumentThumbnailType', ], 'value' => [ 'shape' => 'UrlType', ], ], 'DocumentVersionIdType' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\w+-.@]+', ], 'DocumentVersionMetadata' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'DocumentVersionIdType', ], 'Name' => [ 'shape' => 'ResourceNameType', ], 'ContentType' => [ 'shape' => 'DocumentContentType', ], 'Size' => [ 'shape' => 'SizeType', ], 'Signature' => [ 'shape' => 'HashType', ], 'Status' => [ 'shape' => 'DocumentStatusType', ], 'CreatedTimestamp' => [ 'shape' => 'TimestampType', ], 'ModifiedTimestamp' => [ 'shape' => 'TimestampType', ], 'ContentCreatedTimestamp' => [ 'shape' => 'TimestampType', ], 'ContentModifiedTimestamp' => [ 'shape' => 'TimestampType', ], 'CreatorId' => [ 'shape' => 'IdType', ], 'Thumbnail' => [ 'shape' => 'DocumentThumbnailUrlMap', ], 'Source' => [ 'shape' => 'DocumentSourceUrlMap', ], ], ], 'DocumentVersionMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentVersionMetadata', ], ], 'DocumentVersionStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', ], ], 'DraftUploadOutOfSyncException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessageType', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'EmailAddressType' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}', 'sensitive' => true, ], 'EntityAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessageType', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'EntityIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdType', ], ], 'EntityNotExistsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessageType', ], 'EntityIds' => [ 'shape' => 'EntityIdList', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'ErrorMessageType' => [ 'type' => 'string', ], 'FailedDependencyException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessageType', ], ], 'error' => [ 'httpStatusCode' => 424, ], 'exception' => true, ], 'FieldNamesType' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\w,]+', ], 'Filters' => [ 'type' => 'structure', 'members' => [ 'TextLocales' => [ 'shape' => 'TextLocaleTypeList', ], 'ContentCategories' => [ 'shape' => 'SearchContentCategoryTypeList', ], 'ResourceTypes' => [ 'shape' => 'SearchResourceTypeList', ], 'Labels' => [ 'shape' => 'SearchLabelList', ], 'Principals' => [ 'shape' => 'SearchPrincipalTypeList', ], 'AncestorIds' => [ 'shape' => 'SearchAncestorIdList', ], 'SearchCollectionTypes' => [ 'shape' => 'SearchCollectionTypeList', ], 'SizeRange' => [ 'shape' => 'LongRangeType', ], 'CreatedRange' => [ 'shape' => 'DateRangeType', ], 'ModifiedRange' => [ 'shape' => 'DateRangeType', ], ], ], 'FolderContentType' => [ 'type' => 'string', 'enum' => [ 'ALL', 'DOCUMENT', 'FOLDER', ], ], 'FolderMetadata' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ResourceIdType', ], 'Name' => [ 'shape' => 'ResourceNameType', ], 'CreatorId' => [ 'shape' => 'IdType', ], 'ParentFolderId' => [ 'shape' => 'ResourceIdType', ], 'CreatedTimestamp' => [ 'shape' => 'TimestampType', ], 'ModifiedTimestamp' => [ 'shape' => 'TimestampType', ], 'ResourceState' => [ 'shape' => 'ResourceStateType', ], 'Signature' => [ 'shape' => 'HashType', ], 'Labels' => [ 'shape' => 'SharedLabels', ], 'Size' => [ 'shape' => 'SizeType', ], 'LatestVersionSize' => [ 'shape' => 'SizeType', ], ], ], 'FolderMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FolderMetadata', ], ], 'GetCurrentUserRequest' => [ 'type' => 'structure', 'required' => [ 'AuthenticationToken', ], 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], ], ], 'GetCurrentUserResponse' => [ 'type' => 'structure', 'members' => [ 'User' => [ 'shape' => 'User', ], ], ], 'GetDocumentPathRequest' => [ 'type' => 'structure', 'required' => [ 'DocumentId', ], 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'DocumentId' => [ 'shape' => 'IdType', 'location' => 'uri', 'locationName' => 'DocumentId', ], 'Limit' => [ 'shape' => 'LimitType', 'location' => 'querystring', 'locationName' => 'limit', ], 'Fields' => [ 'shape' => 'FieldNamesType', 'location' => 'querystring', 'locationName' => 'fields', ], 'Marker' => [ 'shape' => 'PageMarkerType', 'location' => 'querystring', 'locationName' => 'marker', ], ], ], 'GetDocumentPathResponse' => [ 'type' => 'structure', 'members' => [ 'Path' => [ 'shape' => 'ResourcePath', ], ], ], 'GetDocumentRequest' => [ 'type' => 'structure', 'required' => [ 'DocumentId', ], 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'DocumentId' => [ 'shape' => 'ResourceIdType', 'location' => 'uri', 'locationName' => 'DocumentId', ], 'IncludeCustomMetadata' => [ 'shape' => 'BooleanType', 'location' => 'querystring', 'locationName' => 'includeCustomMetadata', ], ], ], 'GetDocumentResponse' => [ 'type' => 'structure', 'members' => [ 'Metadata' => [ 'shape' => 'DocumentMetadata', ], 'CustomMetadata' => [ 'shape' => 'CustomMetadataMap', ], ], ], 'GetDocumentVersionRequest' => [ 'type' => 'structure', 'required' => [ 'DocumentId', 'VersionId', ], 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'DocumentId' => [ 'shape' => 'ResourceIdType', 'location' => 'uri', 'locationName' => 'DocumentId', ], 'VersionId' => [ 'shape' => 'DocumentVersionIdType', 'location' => 'uri', 'locationName' => 'VersionId', ], 'Fields' => [ 'shape' => 'FieldNamesType', 'location' => 'querystring', 'locationName' => 'fields', ], 'IncludeCustomMetadata' => [ 'shape' => 'BooleanType', 'location' => 'querystring', 'locationName' => 'includeCustomMetadata', ], ], ], 'GetDocumentVersionResponse' => [ 'type' => 'structure', 'members' => [ 'Metadata' => [ 'shape' => 'DocumentVersionMetadata', ], 'CustomMetadata' => [ 'shape' => 'CustomMetadataMap', ], ], ], 'GetFolderPathRequest' => [ 'type' => 'structure', 'required' => [ 'FolderId', ], 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'FolderId' => [ 'shape' => 'IdType', 'location' => 'uri', 'locationName' => 'FolderId', ], 'Limit' => [ 'shape' => 'LimitType', 'location' => 'querystring', 'locationName' => 'limit', ], 'Fields' => [ 'shape' => 'FieldNamesType', 'location' => 'querystring', 'locationName' => 'fields', ], 'Marker' => [ 'shape' => 'PageMarkerType', 'location' => 'querystring', 'locationName' => 'marker', ], ], ], 'GetFolderPathResponse' => [ 'type' => 'structure', 'members' => [ 'Path' => [ 'shape' => 'ResourcePath', ], ], ], 'GetFolderRequest' => [ 'type' => 'structure', 'required' => [ 'FolderId', ], 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'FolderId' => [ 'shape' => 'ResourceIdType', 'location' => 'uri', 'locationName' => 'FolderId', ], 'IncludeCustomMetadata' => [ 'shape' => 'BooleanType', 'location' => 'querystring', 'locationName' => 'includeCustomMetadata', ], ], ], 'GetFolderResponse' => [ 'type' => 'structure', 'members' => [ 'Metadata' => [ 'shape' => 'FolderMetadata', ], 'CustomMetadata' => [ 'shape' => 'CustomMetadataMap', ], ], ], 'GetResourcesRequest' => [ 'type' => 'structure', 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'UserId' => [ 'shape' => 'IdType', 'location' => 'querystring', 'locationName' => 'userId', ], 'CollectionType' => [ 'shape' => 'ResourceCollectionType', 'location' => 'querystring', 'locationName' => 'collectionType', ], 'Limit' => [ 'shape' => 'LimitType', 'location' => 'querystring', 'locationName' => 'limit', ], 'Marker' => [ 'shape' => 'PageMarkerType', 'location' => 'querystring', 'locationName' => 'marker', ], ], ], 'GetResourcesResponse' => [ 'type' => 'structure', 'members' => [ 'Folders' => [ 'shape' => 'FolderMetadataList', ], 'Documents' => [ 'shape' => 'DocumentMetadataList', ], 'Marker' => [ 'shape' => 'PageMarkerType', ], ], ], 'GroupMetadata' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'IdType', ], 'Name' => [ 'shape' => 'GroupNameType', ], ], ], 'GroupMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroupMetadata', ], ], 'GroupNameType' => [ 'type' => 'string', ], 'HashType' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '[&\\w+-.@]+', ], 'HeaderNameType' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\w-]+', ], 'HeaderValueType' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'IdType' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[&\\w+-.@]+', ], 'IllegalUserStateException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessageType', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'InitiateDocumentVersionUploadRequest' => [ 'type' => 'structure', 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'Id' => [ 'shape' => 'ResourceIdType', ], 'Name' => [ 'shape' => 'ResourceNameType', ], 'ContentCreatedTimestamp' => [ 'shape' => 'TimestampType', ], 'ContentModifiedTimestamp' => [ 'shape' => 'TimestampType', ], 'ContentType' => [ 'shape' => 'DocumentContentType', ], 'DocumentSizeInBytes' => [ 'shape' => 'SizeType', ], 'ParentFolderId' => [ 'shape' => 'ResourceIdType', ], ], ], 'InitiateDocumentVersionUploadResponse' => [ 'type' => 'structure', 'members' => [ 'Metadata' => [ 'shape' => 'DocumentMetadata', ], 'UploadMetadata' => [ 'shape' => 'UploadMetadata', ], ], ], 'InvalidArgumentException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessageType', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidCommentOperationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessageType', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'InvalidOperationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessageType', ], ], 'error' => [ 'httpStatusCode' => 405, ], 'exception' => true, ], 'InvalidPasswordException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessageType', ], ], 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], 'LanguageCodeType' => [ 'type' => 'string', 'enum' => [ 'AR', 'BG', 'BN', 'DA', 'DE', 'CS', 'EL', 'EN', 'ES', 'FA', 'FI', 'FR', 'HI', 'HU', 'ID', 'IT', 'JA', 'KO', 'LT', 'LV', 'NL', 'NO', 'PT', 'RO', 'RU', 'SV', 'SW', 'TH', 'TR', 'ZH', 'DEFAULT', ], ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessageType', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'LimitType' => [ 'type' => 'integer', 'max' => 999, 'min' => 1, ], 'LocaleType' => [ 'type' => 'string', 'enum' => [ 'en', 'fr', 'ko', 'de', 'es', 'ja', 'ru', 'zh_CN', 'zh_TW', 'pt_BR', 'default', ], ], 'LongRangeType' => [ 'type' => 'structure', 'members' => [ 'StartValue' => [ 'shape' => 'LongType', ], 'EndValue' => [ 'shape' => 'LongType', ], ], ], 'LongType' => [ 'type' => 'long', ], 'MarkerType' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '[\\u0000-\\u00FF]+', ], 'MessageType' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'sensitive' => true, ], 'NextMarkerType' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '[\\d]+', ], 'NotificationOptions' => [ 'type' => 'structure', 'members' => [ 'SendEmail' => [ 'shape' => 'BooleanType', ], 'EmailMessage' => [ 'shape' => 'MessageType', ], ], ], 'OrderByFieldType' => [ 'type' => 'string', 'enum' => [ 'RELEVANCE', 'NAME', 'SIZE', 'CREATED_TIMESTAMP', 'MODIFIED_TIMESTAMP', ], ], 'OrderType' => [ 'type' => 'string', 'enum' => [ 'ASCENDING', 'DESCENDING', ], ], 'OrganizationUserList' => [ 'type' => 'list', 'member' => [ 'shape' => 'User', ], ], 'PageMarkerType' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'Participants' => [ 'type' => 'structure', 'members' => [ 'Users' => [ 'shape' => 'UserMetadataList', ], 'Groups' => [ 'shape' => 'GroupMetadataList', ], ], ], 'PasswordType' => [ 'type' => 'string', 'max' => 32, 'min' => 4, 'pattern' => '[\\u0020-\\u00FF]+', 'sensitive' => true, ], 'PermissionInfo' => [ 'type' => 'structure', 'members' => [ 'Role' => [ 'shape' => 'RoleType', ], 'Type' => [ 'shape' => 'RolePermissionType', ], ], ], 'PermissionInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PermissionInfo', ], ], 'PositiveIntegerType' => [ 'type' => 'integer', 'min' => 1, ], 'PositiveSizeType' => [ 'type' => 'long', 'min' => 0, ], 'Principal' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'IdType', ], 'Type' => [ 'shape' => 'PrincipalType', ], 'Roles' => [ 'shape' => 'PermissionInfoList', ], ], ], 'PrincipalList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Principal', ], ], 'PrincipalRoleType' => [ 'type' => 'string', 'enum' => [ 'VIEWER', 'CONTRIBUTOR', 'OWNER', 'COOWNER', ], ], 'PrincipalType' => [ 'type' => 'string', 'enum' => [ 'USER', 'GROUP', 'INVITE', 'ANONYMOUS', 'ORGANIZATION', ], ], 'ProhibitedStateException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessageType', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'RemoveAllResourcePermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', ], 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'ResourceId' => [ 'shape' => 'ResourceIdType', 'location' => 'uri', 'locationName' => 'ResourceId', ], ], ], 'RemoveResourcePermissionRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', 'PrincipalId', ], 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'ResourceId' => [ 'shape' => 'ResourceIdType', 'location' => 'uri', 'locationName' => 'ResourceId', ], 'PrincipalId' => [ 'shape' => 'IdType', 'location' => 'uri', 'locationName' => 'PrincipalId', ], 'PrincipalType' => [ 'shape' => 'PrincipalType', 'location' => 'querystring', 'locationName' => 'type', ], ], ], 'RequestedEntityTooLargeException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessageType', ], ], 'error' => [ 'httpStatusCode' => 413, ], 'exception' => true, ], 'ResourceAlreadyCheckedOutException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessageType', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ResourceCollectionType' => [ 'type' => 'string', 'enum' => [ 'SHARED_WITH_ME', ], ], 'ResourceIdType' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\w+-.@]+', ], 'ResourceMetadata' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'ResourceType', ], 'Name' => [ 'shape' => 'ResourceNameType', ], 'OriginalName' => [ 'shape' => 'ResourceNameType', ], 'Id' => [ 'shape' => 'ResourceIdType', ], 'VersionId' => [ 'shape' => 'DocumentVersionIdType', ], 'Owner' => [ 'shape' => 'UserMetadata', ], 'ParentId' => [ 'shape' => 'ResourceIdType', ], ], ], 'ResourceNameType' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\u202D\\u202F-\\uFFFF]+', 'sensitive' => true, ], 'ResourcePath' => [ 'type' => 'structure', 'members' => [ 'Components' => [ 'shape' => 'ResourcePathComponentList', ], ], ], 'ResourcePathComponent' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'IdType', ], 'Name' => [ 'shape' => 'ResourceNameType', ], ], ], 'ResourcePathComponentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourcePathComponent', ], ], 'ResourceSortType' => [ 'type' => 'string', 'enum' => [ 'DATE', 'NAME', ], ], 'ResourceStateType' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'RESTORING', 'RECYCLING', 'RECYCLED', ], ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'FOLDER', 'DOCUMENT', ], ], 'ResponseItem' => [ 'type' => 'structure', 'members' => [ 'ResourceType' => [ 'shape' => 'ResponseItemType', ], 'WebUrl' => [ 'shape' => 'ResponseItemWebUrl', ], 'DocumentMetadata' => [ 'shape' => 'DocumentMetadata', ], 'FolderMetadata' => [ 'shape' => 'FolderMetadata', ], 'CommentMetadata' => [ 'shape' => 'CommentMetadata', ], 'DocumentVersionMetadata' => [ 'shape' => 'DocumentVersionMetadata', ], ], ], 'ResponseItemType' => [ 'type' => 'string', 'enum' => [ 'DOCUMENT', 'FOLDER', 'COMMENT', 'DOCUMENT_VERSION', ], ], 'ResponseItemWebUrl' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '[\\u0020-\\uFFFF]+', 'sensitive' => true, ], 'ResponseItemsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResponseItem', ], 'max' => 100, ], 'RestoreDocumentVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'DocumentId', ], 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'DocumentId' => [ 'shape' => 'ResourceIdType', 'location' => 'uri', 'locationName' => 'DocumentId', ], ], ], 'RolePermissionType' => [ 'type' => 'string', 'enum' => [ 'DIRECT', 'INHERITED', ], ], 'RoleType' => [ 'type' => 'string', 'enum' => [ 'VIEWER', 'CONTRIBUTOR', 'OWNER', 'COOWNER', ], ], 'SearchAncestorId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'SearchAncestorIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchAncestorId', ], 'max' => 10, ], 'SearchCollectionType' => [ 'type' => 'string', 'enum' => [ 'OWNED', 'SHARED_WITH_ME', ], ], 'SearchCollectionTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchCollectionType', ], 'max' => 2, ], 'SearchContentCategoryTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContentCategoryType', ], 'max' => 9, ], 'SearchLabel' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'SearchLabelList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchLabel', ], 'max' => 10, ], 'SearchMarkerType' => [ 'type' => 'string', 'max' => 12288, 'min' => 1, 'pattern' => '[\\u0000-\\u00FF]+', ], 'SearchPrincipalRoleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PrincipalRoleType', ], 'max' => 4, ], 'SearchPrincipalType' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'IdType', ], 'Roles' => [ 'shape' => 'SearchPrincipalRoleList', ], ], ], 'SearchPrincipalTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchPrincipalType', ], 'max' => 10, ], 'SearchQueryScopeType' => [ 'type' => 'string', 'enum' => [ 'NAME', 'CONTENT', ], ], 'SearchQueryScopeTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchQueryScopeType', ], 'max' => 2, ], 'SearchQueryType' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '[\\u0020-\\uFFFF]+', 'sensitive' => true, ], 'SearchResourceType' => [ 'type' => 'string', 'enum' => [ 'FOLDER', 'DOCUMENT', 'COMMENT', 'DOCUMENT_VERSION', ], ], 'SearchResourceTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchResourceType', ], 'max' => 4, ], 'SearchResourcesRequest' => [ 'type' => 'structure', 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'QueryText' => [ 'shape' => 'SearchQueryType', ], 'QueryScopes' => [ 'shape' => 'SearchQueryScopeTypeList', ], 'OrganizationId' => [ 'shape' => 'IdType', ], 'AdditionalResponseFields' => [ 'shape' => 'AdditionalResponseFieldsList', ], 'Filters' => [ 'shape' => 'Filters', ], 'OrderBy' => [ 'shape' => 'SearchResultSortList', ], 'Limit' => [ 'shape' => 'SearchResultsLimitType', ], 'Marker' => [ 'shape' => 'NextMarkerType', ], ], ], 'SearchResourcesResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'ResponseItemsList', ], 'Marker' => [ 'shape' => 'NextMarkerType', ], ], ], 'SearchResultSortList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchSortResult', ], 'max' => 1, ], 'SearchResultsLimitType' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'SearchSortResult' => [ 'type' => 'structure', 'members' => [ 'Field' => [ 'shape' => 'OrderByFieldType', ], 'Order' => [ 'shape' => 'SortOrder', ], ], ], 'ServiceUnavailableException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessageType', ], ], 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], 'SharePrincipal' => [ 'type' => 'structure', 'required' => [ 'Id', 'Type', 'Role', ], 'members' => [ 'Id' => [ 'shape' => 'IdType', ], 'Type' => [ 'shape' => 'PrincipalType', ], 'Role' => [ 'shape' => 'RoleType', ], ], ], 'SharePrincipalList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SharePrincipal', ], ], 'ShareResult' => [ 'type' => 'structure', 'members' => [ 'PrincipalId' => [ 'shape' => 'IdType', ], 'InviteePrincipalId' => [ 'shape' => 'IdType', ], 'Role' => [ 'shape' => 'RoleType', ], 'Status' => [ 'shape' => 'ShareStatusType', ], 'ShareId' => [ 'shape' => 'ResourceIdType', ], 'StatusMessage' => [ 'shape' => 'MessageType', ], ], ], 'ShareResultsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ShareResult', ], ], 'ShareStatusType' => [ 'type' => 'string', 'enum' => [ 'SUCCESS', 'FAILURE', ], ], 'SharedLabel' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '[a-zA-Z0-9._+-/=][a-zA-Z0-9 ._+-/=]*', ], 'SharedLabels' => [ 'type' => 'list', 'member' => [ 'shape' => 'SharedLabel', ], 'max' => 20, ], 'SignedHeaderMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'HeaderNameType', ], 'value' => [ 'shape' => 'HeaderValueType', ], ], 'SizeType' => [ 'type' => 'long', ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'ASC', 'DESC', ], ], 'StorageLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessageType', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'StorageLimitWillExceedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessageType', ], ], 'error' => [ 'httpStatusCode' => 413, ], 'exception' => true, ], 'StorageRuleType' => [ 'type' => 'structure', 'members' => [ 'StorageAllocatedInBytes' => [ 'shape' => 'PositiveSizeType', ], 'StorageType' => [ 'shape' => 'StorageType', ], ], ], 'StorageType' => [ 'type' => 'string', 'enum' => [ 'UNLIMITED', 'QUOTA', ], ], 'Subscription' => [ 'type' => 'structure', 'members' => [ 'SubscriptionId' => [ 'shape' => 'IdType', ], 'EndPoint' => [ 'shape' => 'SubscriptionEndPointType', ], 'Protocol' => [ 'shape' => 'SubscriptionProtocolType', ], ], ], 'SubscriptionEndPointType' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'SubscriptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Subscription', ], 'max' => 256, ], 'SubscriptionProtocolType' => [ 'type' => 'string', 'enum' => [ 'HTTPS', 'SQS', ], ], 'SubscriptionType' => [ 'type' => 'string', 'enum' => [ 'ALL', ], ], 'TextLocaleTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LanguageCodeType', ], 'max' => 1, ], 'TimeZoneIdType' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'TimestampType' => [ 'type' => 'timestamp', ], 'TooManyLabelsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessageType', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'TooManySubscriptionsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessageType', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'UnauthorizedOperationException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'UnauthorizedResourceAccessException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessageType', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'UpdateDocumentRequest' => [ 'type' => 'structure', 'required' => [ 'DocumentId', ], 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'DocumentId' => [ 'shape' => 'ResourceIdType', 'location' => 'uri', 'locationName' => 'DocumentId', ], 'Name' => [ 'shape' => 'ResourceNameType', ], 'ParentFolderId' => [ 'shape' => 'ResourceIdType', ], 'ResourceState' => [ 'shape' => 'ResourceStateType', ], ], ], 'UpdateDocumentVersionRequest' => [ 'type' => 'structure', 'required' => [ 'DocumentId', 'VersionId', ], 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'DocumentId' => [ 'shape' => 'ResourceIdType', 'location' => 'uri', 'locationName' => 'DocumentId', ], 'VersionId' => [ 'shape' => 'DocumentVersionIdType', 'location' => 'uri', 'locationName' => 'VersionId', ], 'VersionStatus' => [ 'shape' => 'DocumentVersionStatus', ], ], ], 'UpdateFolderRequest' => [ 'type' => 'structure', 'required' => [ 'FolderId', ], 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'FolderId' => [ 'shape' => 'ResourceIdType', 'location' => 'uri', 'locationName' => 'FolderId', ], 'Name' => [ 'shape' => 'ResourceNameType', ], 'ParentFolderId' => [ 'shape' => 'ResourceIdType', ], 'ResourceState' => [ 'shape' => 'ResourceStateType', ], ], ], 'UpdateUserRequest' => [ 'type' => 'structure', 'required' => [ 'UserId', ], 'members' => [ 'AuthenticationToken' => [ 'shape' => 'AuthenticationHeaderType', 'location' => 'header', 'locationName' => 'Authentication', ], 'UserId' => [ 'shape' => 'IdType', 'location' => 'uri', 'locationName' => 'UserId', ], 'GivenName' => [ 'shape' => 'UserAttributeValueType', ], 'Surname' => [ 'shape' => 'UserAttributeValueType', ], 'Type' => [ 'shape' => 'UserType', ], 'StorageRule' => [ 'shape' => 'StorageRuleType', ], 'TimeZoneId' => [ 'shape' => 'TimeZoneIdType', ], 'Locale' => [ 'shape' => 'LocaleType', ], 'GrantPoweruserPrivileges' => [ 'shape' => 'BooleanEnumType', ], ], ], 'UpdateUserResponse' => [ 'type' => 'structure', 'members' => [ 'User' => [ 'shape' => 'User', ], ], ], 'UploadMetadata' => [ 'type' => 'structure', 'members' => [ 'UploadUrl' => [ 'shape' => 'UrlType', ], 'SignedHeaders' => [ 'shape' => 'SignedHeaderMap', ], ], ], 'UrlType' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'sensitive' => true, ], 'User' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'IdType', ], 'Username' => [ 'shape' => 'UsernameType', ], 'EmailAddress' => [ 'shape' => 'EmailAddressType', ], 'GivenName' => [ 'shape' => 'UserAttributeValueType', ], 'Surname' => [ 'shape' => 'UserAttributeValueType', ], 'OrganizationId' => [ 'shape' => 'IdType', ], 'RootFolderId' => [ 'shape' => 'ResourceIdType', ], 'RecycleBinFolderId' => [ 'shape' => 'ResourceIdType', ], 'Status' => [ 'shape' => 'UserStatusType', ], 'Type' => [ 'shape' => 'UserType', ], 'CreatedTimestamp' => [ 'shape' => 'TimestampType', ], 'ModifiedTimestamp' => [ 'shape' => 'TimestampType', ], 'TimeZoneId' => [ 'shape' => 'TimeZoneIdType', ], 'Locale' => [ 'shape' => 'LocaleType', ], 'Storage' => [ 'shape' => 'UserStorageMetadata', ], ], ], 'UserActivities' => [ 'type' => 'list', 'member' => [ 'shape' => 'Activity', ], ], 'UserAttributeValueType' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'sensitive' => true, ], 'UserFilterType' => [ 'type' => 'string', 'enum' => [ 'ALL', 'ACTIVE_PENDING', ], ], 'UserIdsType' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, 'pattern' => '[&\\w+-.@, ]+', ], 'UserMetadata' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'IdType', ], 'Username' => [ 'shape' => 'UsernameType', ], 'GivenName' => [ 'shape' => 'UserAttributeValueType', ], 'Surname' => [ 'shape' => 'UserAttributeValueType', ], 'EmailAddress' => [ 'shape' => 'EmailAddressType', ], ], ], 'UserMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserMetadata', ], ], 'UserSortType' => [ 'type' => 'string', 'enum' => [ 'USER_NAME', 'FULL_NAME', 'STORAGE_LIMIT', 'USER_STATUS', 'STORAGE_USED', ], ], 'UserStatusType' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', 'PENDING', ], ], 'UserStorageMetadata' => [ 'type' => 'structure', 'members' => [ 'StorageUtilizedInBytes' => [ 'shape' => 'SizeType', ], 'StorageRule' => [ 'shape' => 'StorageRuleType', ], ], ], 'UserType' => [ 'type' => 'string', 'enum' => [ 'USER', 'ADMIN', 'POWERUSER', 'MINIMALUSER', 'WORKSPACESUSER', ], ], 'UsernameType' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\w\\-+.]+(@[a-zA-Z0-9.\\-]+\\.[a-zA-Z]+)?', 'sensitive' => true, ], ],];
