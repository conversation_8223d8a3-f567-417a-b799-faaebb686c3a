<?php
// This file was auto-generated from sdk-root/src/data/workspaces/2015-04-08/paginators-1.json
return [ 'pagination' => [ 'DescribeApplicationAssociations' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', ], 'DescribeApplications' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', ], 'DescribeWorkspaceBundles' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'result_key' => 'Bundles', ], 'DescribeWorkspaceDirectories' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'result_key' => 'Directories', ], 'DescribeWorkspaces' => [ 'input_token' => 'NextToken', 'limit_key' => 'Limit', 'output_token' => 'NextToken', 'result_key' => 'Workspaces', ], 'ListAccountLinks' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'AccountLinks', ], ],];
