<?php
// This file was auto-generated from sdk-root/src/data/xray/2016-04-12/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2016-04-12', 'endpointPrefix' => 'xray', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'AWS X-Ray', 'serviceId' => 'XRay', 'signatureVersion' => 'v4', 'uid' => 'xray-2016-04-12', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'BatchGetTraces' => [ 'name' => 'BatchGetTraces', 'http' => [ 'method' => 'POST', 'requestUri' => '/Traces', ], 'input' => [ 'shape' => 'BatchGetTracesRequest', ], 'output' => [ 'shape' => 'BatchGetTracesResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], ], ], 'CancelTraceRetrieval' => [ 'name' => 'CancelTraceRetrieval', 'http' => [ 'method' => 'POST', 'requestUri' => '/CancelTraceRetrieval', ], 'input' => [ 'shape' => 'CancelTraceRetrievalRequest', ], 'output' => [ 'shape' => 'CancelTraceRetrievalResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'CreateGroup' => [ 'name' => 'CreateGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreateGroup', ], 'input' => [ 'shape' => 'CreateGroupRequest', ], 'output' => [ 'shape' => 'CreateGroupResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], ], ], 'CreateSamplingRule' => [ 'name' => 'CreateSamplingRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreateSamplingRule', ], 'input' => [ 'shape' => 'CreateSamplingRuleRequest', ], 'output' => [ 'shape' => 'CreateSamplingRuleResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], [ 'shape' => 'RuleLimitExceededException', ], ], ], 'DeleteGroup' => [ 'name' => 'DeleteGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteGroup', ], 'input' => [ 'shape' => 'DeleteGroupRequest', ], 'output' => [ 'shape' => 'DeleteGroupResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], ], ], 'DeleteResourcePolicy' => [ 'name' => 'DeleteResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteResourcePolicy', ], 'input' => [ 'shape' => 'DeleteResourcePolicyRequest', ], 'output' => [ 'shape' => 'DeleteResourcePolicyResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidPolicyRevisionIdException', ], [ 'shape' => 'ThrottledException', ], ], ], 'DeleteSamplingRule' => [ 'name' => 'DeleteSamplingRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteSamplingRule', ], 'input' => [ 'shape' => 'DeleteSamplingRuleRequest', ], 'output' => [ 'shape' => 'DeleteSamplingRuleResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], ], ], 'GetEncryptionConfig' => [ 'name' => 'GetEncryptionConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/EncryptionConfig', ], 'input' => [ 'shape' => 'GetEncryptionConfigRequest', ], 'output' => [ 'shape' => 'GetEncryptionConfigResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], ], ], 'GetGroup' => [ 'name' => 'GetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetGroup', ], 'input' => [ 'shape' => 'GetGroupRequest', ], 'output' => [ 'shape' => 'GetGroupResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], ], ], 'GetGroups' => [ 'name' => 'GetGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/Groups', ], 'input' => [ 'shape' => 'GetGroupsRequest', ], 'output' => [ 'shape' => 'GetGroupsResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], ], ], 'GetIndexingRules' => [ 'name' => 'GetIndexingRules', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetIndexingRules', ], 'input' => [ 'shape' => 'GetIndexingRulesRequest', ], 'output' => [ 'shape' => 'GetIndexingRulesResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], ], ], 'GetInsight' => [ 'name' => 'GetInsight', 'http' => [ 'method' => 'POST', 'requestUri' => '/Insight', ], 'input' => [ 'shape' => 'GetInsightRequest', ], 'output' => [ 'shape' => 'GetInsightResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], ], ], 'GetInsightEvents' => [ 'name' => 'GetInsightEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/InsightEvents', ], 'input' => [ 'shape' => 'GetInsightEventsRequest', ], 'output' => [ 'shape' => 'GetInsightEventsResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], ], ], 'GetInsightImpactGraph' => [ 'name' => 'GetInsightImpactGraph', 'http' => [ 'method' => 'POST', 'requestUri' => '/InsightImpactGraph', ], 'input' => [ 'shape' => 'GetInsightImpactGraphRequest', ], 'output' => [ 'shape' => 'GetInsightImpactGraphResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], ], ], 'GetInsightSummaries' => [ 'name' => 'GetInsightSummaries', 'http' => [ 'method' => 'POST', 'requestUri' => '/InsightSummaries', ], 'input' => [ 'shape' => 'GetInsightSummariesRequest', ], 'output' => [ 'shape' => 'GetInsightSummariesResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], ], ], 'GetRetrievedTracesGraph' => [ 'name' => 'GetRetrievedTracesGraph', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetRetrievedTracesGraph', ], 'input' => [ 'shape' => 'GetRetrievedTracesGraphRequest', ], 'output' => [ 'shape' => 'GetRetrievedTracesGraphResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetSamplingRules' => [ 'name' => 'GetSamplingRules', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetSamplingRules', ], 'input' => [ 'shape' => 'GetSamplingRulesRequest', ], 'output' => [ 'shape' => 'GetSamplingRulesResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], ], ], 'GetSamplingStatisticSummaries' => [ 'name' => 'GetSamplingStatisticSummaries', 'http' => [ 'method' => 'POST', 'requestUri' => '/SamplingStatisticSummaries', ], 'input' => [ 'shape' => 'GetSamplingStatisticSummariesRequest', ], 'output' => [ 'shape' => 'GetSamplingStatisticSummariesResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], ], ], 'GetSamplingTargets' => [ 'name' => 'GetSamplingTargets', 'http' => [ 'method' => 'POST', 'requestUri' => '/SamplingTargets', ], 'input' => [ 'shape' => 'GetSamplingTargetsRequest', ], 'output' => [ 'shape' => 'GetSamplingTargetsResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], ], ], 'GetServiceGraph' => [ 'name' => 'GetServiceGraph', 'http' => [ 'method' => 'POST', 'requestUri' => '/ServiceGraph', ], 'input' => [ 'shape' => 'GetServiceGraphRequest', ], 'output' => [ 'shape' => 'GetServiceGraphResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], ], ], 'GetTimeSeriesServiceStatistics' => [ 'name' => 'GetTimeSeriesServiceStatistics', 'http' => [ 'method' => 'POST', 'requestUri' => '/TimeSeriesServiceStatistics', ], 'input' => [ 'shape' => 'GetTimeSeriesServiceStatisticsRequest', ], 'output' => [ 'shape' => 'GetTimeSeriesServiceStatisticsResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], ], ], 'GetTraceGraph' => [ 'name' => 'GetTraceGraph', 'http' => [ 'method' => 'POST', 'requestUri' => '/TraceGraph', ], 'input' => [ 'shape' => 'GetTraceGraphRequest', ], 'output' => [ 'shape' => 'GetTraceGraphResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], ], ], 'GetTraceSegmentDestination' => [ 'name' => 'GetTraceSegmentDestination', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetTraceSegmentDestination', ], 'input' => [ 'shape' => 'GetTraceSegmentDestinationRequest', ], 'output' => [ 'shape' => 'GetTraceSegmentDestinationResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], ], ], 'GetTraceSummaries' => [ 'name' => 'GetTraceSummaries', 'http' => [ 'method' => 'POST', 'requestUri' => '/TraceSummaries', ], 'input' => [ 'shape' => 'GetTraceSummariesRequest', ], 'output' => [ 'shape' => 'GetTraceSummariesResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], ], ], 'ListResourcePolicies' => [ 'name' => 'ListResourcePolicies', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListResourcePolicies', ], 'input' => [ 'shape' => 'ListResourcePoliciesRequest', ], 'output' => [ 'shape' => 'ListResourcePoliciesResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], ], ], 'ListRetrievedTraces' => [ 'name' => 'ListRetrievedTraces', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListRetrievedTraces', ], 'input' => [ 'shape' => 'ListRetrievedTracesRequest', ], 'output' => [ 'shape' => 'ListRetrievedTracesResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListTagsForResource', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'PutEncryptionConfig' => [ 'name' => 'PutEncryptionConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/PutEncryptionConfig', ], 'input' => [ 'shape' => 'PutEncryptionConfigRequest', ], 'output' => [ 'shape' => 'PutEncryptionConfigResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], ], ], 'PutResourcePolicy' => [ 'name' => 'PutResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/PutResourcePolicy', ], 'input' => [ 'shape' => 'PutResourcePolicyRequest', ], 'output' => [ 'shape' => 'PutResourcePolicyResult', ], 'errors' => [ [ 'shape' => 'MalformedPolicyDocumentException', ], [ 'shape' => 'LockoutPreventionException', ], [ 'shape' => 'InvalidPolicyRevisionIdException', ], [ 'shape' => 'PolicySizeLimitExceededException', ], [ 'shape' => 'PolicyCountLimitExceededException', ], [ 'shape' => 'ThrottledException', ], ], ], 'PutTelemetryRecords' => [ 'name' => 'PutTelemetryRecords', 'http' => [ 'method' => 'POST', 'requestUri' => '/TelemetryRecords', ], 'input' => [ 'shape' => 'PutTelemetryRecordsRequest', ], 'output' => [ 'shape' => 'PutTelemetryRecordsResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], ], ], 'PutTraceSegments' => [ 'name' => 'PutTraceSegments', 'http' => [ 'method' => 'POST', 'requestUri' => '/TraceSegments', ], 'input' => [ 'shape' => 'PutTraceSegmentsRequest', ], 'output' => [ 'shape' => 'PutTraceSegmentsResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], ], ], 'StartTraceRetrieval' => [ 'name' => 'StartTraceRetrieval', 'http' => [ 'method' => 'POST', 'requestUri' => '/StartTraceRetrieval', ], 'input' => [ 'shape' => 'StartTraceRetrievalRequest', ], 'output' => [ 'shape' => 'StartTraceRetrievalResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/TagResource', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyTagsException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/UntagResource', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateGroup' => [ 'name' => 'UpdateGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateGroup', ], 'input' => [ 'shape' => 'UpdateGroupRequest', ], 'output' => [ 'shape' => 'UpdateGroupResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], ], ], 'UpdateIndexingRule' => [ 'name' => 'UpdateIndexingRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateIndexingRule', ], 'input' => [ 'shape' => 'UpdateIndexingRuleRequest', ], 'output' => [ 'shape' => 'UpdateIndexingRuleResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateSamplingRule' => [ 'name' => 'UpdateSamplingRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateSamplingRule', ], 'input' => [ 'shape' => 'UpdateSamplingRuleRequest', ], 'output' => [ 'shape' => 'UpdateSamplingRuleResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], ], ], 'UpdateTraceSegmentDestination' => [ 'name' => 'UpdateTraceSegmentDestination', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateTraceSegmentDestination', ], 'input' => [ 'shape' => 'UpdateTraceSegmentDestinationRequest', ], 'output' => [ 'shape' => 'UpdateTraceSegmentDestinationResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottledException', ], ], ], ], 'shapes' => [ 'Alias' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Names' => [ 'shape' => 'AliasNames', ], 'Type' => [ 'shape' => 'String', ], ], ], 'AliasList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Alias', ], ], 'AliasNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'AmazonResourceName' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, ], 'AnnotationKey' => [ 'type' => 'string', ], 'AnnotationValue' => [ 'type' => 'structure', 'members' => [ 'NumberValue' => [ 'shape' => 'NullableDouble', ], 'BooleanValue' => [ 'shape' => 'NullableBoolean', ], 'StringValue' => [ 'shape' => 'String', ], ], ], 'Annotations' => [ 'type' => 'map', 'key' => [ 'shape' => 'AnnotationKey', ], 'value' => [ 'shape' => 'ValuesWithServiceIds', ], ], 'AnomalousService' => [ 'type' => 'structure', 'members' => [ 'ServiceId' => [ 'shape' => 'ServiceId', ], ], ], 'AnomalousServiceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnomalousService', ], ], 'AttributeKey' => [ 'type' => 'string', 'max' => 32, 'min' => 1, ], 'AttributeMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'AttributeKey', ], 'value' => [ 'shape' => 'AttributeValue', ], 'max' => 5, ], 'AttributeValue' => [ 'type' => 'string', 'max' => 32, 'min' => 1, ], 'AvailabilityZoneDetail' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], ], ], 'BackendConnectionErrors' => [ 'type' => 'structure', 'members' => [ 'TimeoutCount' => [ 'shape' => 'NullableInteger', ], 'ConnectionRefusedCount' => [ 'shape' => 'NullableInteger', ], 'HTTPCode4XXCount' => [ 'shape' => 'NullableInteger', ], 'HTTPCode5XXCount' => [ 'shape' => 'NullableInteger', ], 'UnknownHostCount' => [ 'shape' => 'NullableInteger', ], 'OtherCount' => [ 'shape' => 'NullableInteger', ], ], ], 'BatchGetTracesRequest' => [ 'type' => 'structure', 'required' => [ 'TraceIds', ], 'members' => [ 'TraceIds' => [ 'shape' => 'TraceIdList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'BatchGetTracesResult' => [ 'type' => 'structure', 'members' => [ 'Traces' => [ 'shape' => 'TraceList', ], 'UnprocessedTraceIds' => [ 'shape' => 'UnprocessedTraceIdList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'Boolean' => [ 'type' => 'boolean', ], 'BorrowCount' => [ 'type' => 'integer', 'min' => 0, ], 'CancelTraceRetrievalRequest' => [ 'type' => 'structure', 'required' => [ 'RetrievalToken', ], 'members' => [ 'RetrievalToken' => [ 'shape' => 'RetrievalToken', ], ], ], 'CancelTraceRetrievalResult' => [ 'type' => 'structure', 'members' => [], ], 'ClientID' => [ 'type' => 'string', 'max' => 24, 'min' => 24, ], 'CreateGroupRequest' => [ 'type' => 'structure', 'required' => [ 'GroupName', ], 'members' => [ 'GroupName' => [ 'shape' => 'GroupName', ], 'FilterExpression' => [ 'shape' => 'FilterExpression', ], 'InsightsConfiguration' => [ 'shape' => 'InsightsConfiguration', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateGroupResult' => [ 'type' => 'structure', 'members' => [ 'Group' => [ 'shape' => 'Group', ], ], ], 'CreateSamplingRuleRequest' => [ 'type' => 'structure', 'required' => [ 'SamplingRule', ], 'members' => [ 'SamplingRule' => [ 'shape' => 'SamplingRule', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateSamplingRuleResult' => [ 'type' => 'structure', 'members' => [ 'SamplingRuleRecord' => [ 'shape' => 'SamplingRuleRecord', ], ], ], 'DeleteGroupRequest' => [ 'type' => 'structure', 'members' => [ 'GroupName' => [ 'shape' => 'GroupName', ], 'GroupARN' => [ 'shape' => 'GroupARN', ], ], ], 'DeleteGroupResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'PolicyName', ], 'members' => [ 'PolicyName' => [ 'shape' => 'PolicyName', ], 'PolicyRevisionId' => [ 'shape' => 'PolicyRevisionId', ], ], ], 'DeleteResourcePolicyResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSamplingRuleRequest' => [ 'type' => 'structure', 'members' => [ 'RuleName' => [ 'shape' => 'String', ], 'RuleARN' => [ 'shape' => 'String', ], ], ], 'DeleteSamplingRuleResult' => [ 'type' => 'structure', 'members' => [ 'SamplingRuleRecord' => [ 'shape' => 'SamplingRuleRecord', ], ], ], 'Double' => [ 'type' => 'double', ], 'EC2InstanceId' => [ 'type' => 'string', 'max' => 20, ], 'Edge' => [ 'type' => 'structure', 'members' => [ 'ReferenceId' => [ 'shape' => 'NullableInteger', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'SummaryStatistics' => [ 'shape' => 'EdgeStatistics', ], 'ResponseTimeHistogram' => [ 'shape' => 'Histogram', ], 'Aliases' => [ 'shape' => 'AliasList', ], 'EdgeType' => [ 'shape' => 'String', ], 'ReceivedEventAgeHistogram' => [ 'shape' => 'Histogram', ], ], ], 'EdgeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Edge', ], ], 'EdgeStatistics' => [ 'type' => 'structure', 'members' => [ 'OkCount' => [ 'shape' => 'NullableLong', ], 'ErrorStatistics' => [ 'shape' => 'ErrorStatistics', ], 'FaultStatistics' => [ 'shape' => 'FaultStatistics', ], 'TotalCount' => [ 'shape' => 'NullableLong', ], 'TotalResponseTime' => [ 'shape' => 'NullableDouble', ], ], ], 'EncryptionConfig' => [ 'type' => 'structure', 'members' => [ 'KeyId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'EncryptionStatus', ], 'Type' => [ 'shape' => 'EncryptionType', ], ], ], 'EncryptionKeyId' => [ 'type' => 'string', 'max' => 3000, 'min' => 1, ], 'EncryptionStatus' => [ 'type' => 'string', 'enum' => [ 'UPDATING', 'ACTIVE', ], ], 'EncryptionType' => [ 'type' => 'string', 'enum' => [ 'NONE', 'KMS', ], ], 'EntitySelectorExpression' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'ErrorMessage' => [ 'type' => 'string', ], 'ErrorRootCause' => [ 'type' => 'structure', 'members' => [ 'Services' => [ 'shape' => 'ErrorRootCauseServices', ], 'ClientImpacting' => [ 'shape' => 'NullableBoolean', ], ], ], 'ErrorRootCauseEntity' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Exceptions' => [ 'shape' => 'RootCauseExceptions', ], 'Remote' => [ 'shape' => 'NullableBoolean', ], ], ], 'ErrorRootCauseEntityPath' => [ 'type' => 'list', 'member' => [ 'shape' => 'ErrorRootCauseEntity', ], ], 'ErrorRootCauseService' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Names' => [ 'shape' => 'ServiceNames', ], 'Type' => [ 'shape' => 'String', ], 'AccountId' => [ 'shape' => 'String', ], 'EntityPath' => [ 'shape' => 'ErrorRootCauseEntityPath', ], 'Inferred' => [ 'shape' => 'NullableBoolean', ], ], ], 'ErrorRootCauseServices' => [ 'type' => 'list', 'member' => [ 'shape' => 'ErrorRootCauseService', ], ], 'ErrorRootCauses' => [ 'type' => 'list', 'member' => [ 'shape' => 'ErrorRootCause', ], ], 'ErrorStatistics' => [ 'type' => 'structure', 'members' => [ 'ThrottleCount' => [ 'shape' => 'NullableLong', ], 'OtherCount' => [ 'shape' => 'NullableLong', ], 'TotalCount' => [ 'shape' => 'NullableLong', ], ], ], 'EventSummaryText' => [ 'type' => 'string', ], 'FaultRootCause' => [ 'type' => 'structure', 'members' => [ 'Services' => [ 'shape' => 'FaultRootCauseServices', ], 'ClientImpacting' => [ 'shape' => 'NullableBoolean', ], ], ], 'FaultRootCauseEntity' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Exceptions' => [ 'shape' => 'RootCauseExceptions', ], 'Remote' => [ 'shape' => 'NullableBoolean', ], ], ], 'FaultRootCauseEntityPath' => [ 'type' => 'list', 'member' => [ 'shape' => 'FaultRootCauseEntity', ], ], 'FaultRootCauseService' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Names' => [ 'shape' => 'ServiceNames', ], 'Type' => [ 'shape' => 'String', ], 'AccountId' => [ 'shape' => 'String', ], 'EntityPath' => [ 'shape' => 'FaultRootCauseEntityPath', ], 'Inferred' => [ 'shape' => 'NullableBoolean', ], ], ], 'FaultRootCauseServices' => [ 'type' => 'list', 'member' => [ 'shape' => 'FaultRootCauseService', ], ], 'FaultRootCauses' => [ 'type' => 'list', 'member' => [ 'shape' => 'FaultRootCause', ], ], 'FaultStatistics' => [ 'type' => 'structure', 'members' => [ 'OtherCount' => [ 'shape' => 'NullableLong', ], 'TotalCount' => [ 'shape' => 'NullableLong', ], ], ], 'FilterExpression' => [ 'type' => 'string', ], 'FixedRate' => [ 'type' => 'double', 'max' => 1, 'min' => 0, ], 'ForecastStatistics' => [ 'type' => 'structure', 'members' => [ 'FaultCountHigh' => [ 'shape' => 'NullableLong', ], 'FaultCountLow' => [ 'shape' => 'NullableLong', ], ], ], 'GetEncryptionConfigRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetEncryptionConfigResult' => [ 'type' => 'structure', 'members' => [ 'EncryptionConfig' => [ 'shape' => 'EncryptionConfig', ], ], ], 'GetGroupRequest' => [ 'type' => 'structure', 'members' => [ 'GroupName' => [ 'shape' => 'GroupName', ], 'GroupARN' => [ 'shape' => 'GroupARN', ], ], ], 'GetGroupResult' => [ 'type' => 'structure', 'members' => [ 'Group' => [ 'shape' => 'Group', ], ], ], 'GetGroupsNextToken' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'GetGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'GetGroupsNextToken', ], ], ], 'GetGroupsResult' => [ 'type' => 'structure', 'members' => [ 'Groups' => [ 'shape' => 'GroupSummaryList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'GetIndexingRulesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], ], ], 'GetIndexingRulesResult' => [ 'type' => 'structure', 'members' => [ 'IndexingRules' => [ 'shape' => 'IndexingRuleList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'GetInsightEventsMaxResults' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'GetInsightEventsRequest' => [ 'type' => 'structure', 'required' => [ 'InsightId', ], 'members' => [ 'InsightId' => [ 'shape' => 'InsightId', ], 'MaxResults' => [ 'shape' => 'GetInsightEventsMaxResults', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetInsightEventsResult' => [ 'type' => 'structure', 'members' => [ 'InsightEvents' => [ 'shape' => 'InsightEventList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetInsightImpactGraphRequest' => [ 'type' => 'structure', 'required' => [ 'InsightId', 'StartTime', 'EndTime', ], 'members' => [ 'InsightId' => [ 'shape' => 'InsightId', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetInsightImpactGraphResult' => [ 'type' => 'structure', 'members' => [ 'InsightId' => [ 'shape' => 'InsightId', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'ServiceGraphStartTime' => [ 'shape' => 'Timestamp', ], 'ServiceGraphEndTime' => [ 'shape' => 'Timestamp', ], 'Services' => [ 'shape' => 'InsightImpactGraphServiceList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetInsightRequest' => [ 'type' => 'structure', 'required' => [ 'InsightId', ], 'members' => [ 'InsightId' => [ 'shape' => 'InsightId', ], ], ], 'GetInsightResult' => [ 'type' => 'structure', 'members' => [ 'Insight' => [ 'shape' => 'Insight', ], ], ], 'GetInsightSummariesMaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'GetInsightSummariesRequest' => [ 'type' => 'structure', 'required' => [ 'StartTime', 'EndTime', ], 'members' => [ 'States' => [ 'shape' => 'InsightStateList', ], 'GroupARN' => [ 'shape' => 'GroupARN', ], 'GroupName' => [ 'shape' => 'GroupName', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'MaxResults' => [ 'shape' => 'GetInsightSummariesMaxResults', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetInsightSummariesResult' => [ 'type' => 'structure', 'members' => [ 'InsightSummaries' => [ 'shape' => 'InsightSummaryList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetRetrievedTracesGraphRequest' => [ 'type' => 'structure', 'required' => [ 'RetrievalToken', ], 'members' => [ 'RetrievalToken' => [ 'shape' => 'RetrievalToken', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'GetRetrievedTracesGraphResult' => [ 'type' => 'structure', 'members' => [ 'RetrievalStatus' => [ 'shape' => 'RetrievalStatus', ], 'Services' => [ 'shape' => 'RetrievedServicesList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'GetSamplingRulesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], ], ], 'GetSamplingRulesResult' => [ 'type' => 'structure', 'members' => [ 'SamplingRuleRecords' => [ 'shape' => 'SamplingRuleRecordList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'GetSamplingStatisticSummariesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], ], ], 'GetSamplingStatisticSummariesResult' => [ 'type' => 'structure', 'members' => [ 'SamplingStatisticSummaries' => [ 'shape' => 'SamplingStatisticSummaryList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'GetSamplingTargetsRequest' => [ 'type' => 'structure', 'required' => [ 'SamplingStatisticsDocuments', ], 'members' => [ 'SamplingStatisticsDocuments' => [ 'shape' => 'SamplingStatisticsDocumentList', ], ], ], 'GetSamplingTargetsResult' => [ 'type' => 'structure', 'members' => [ 'SamplingTargetDocuments' => [ 'shape' => 'SamplingTargetDocumentList', ], 'LastRuleModification' => [ 'shape' => 'Timestamp', ], 'UnprocessedStatistics' => [ 'shape' => 'UnprocessedStatisticsList', ], ], ], 'GetServiceGraphRequest' => [ 'type' => 'structure', 'required' => [ 'StartTime', 'EndTime', ], 'members' => [ 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'GroupName' => [ 'shape' => 'GroupName', ], 'GroupARN' => [ 'shape' => 'GroupARN', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'GetServiceGraphResult' => [ 'type' => 'structure', 'members' => [ 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'Services' => [ 'shape' => 'ServiceList', ], 'ContainsOldGroupVersions' => [ 'shape' => 'Boolean', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'GetTimeSeriesServiceStatisticsRequest' => [ 'type' => 'structure', 'required' => [ 'StartTime', 'EndTime', ], 'members' => [ 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'GroupName' => [ 'shape' => 'GroupName', ], 'GroupARN' => [ 'shape' => 'GroupARN', ], 'EntitySelectorExpression' => [ 'shape' => 'EntitySelectorExpression', ], 'Period' => [ 'shape' => 'NullableInteger', ], 'ForecastStatistics' => [ 'shape' => 'NullableBoolean', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'GetTimeSeriesServiceStatisticsResult' => [ 'type' => 'structure', 'members' => [ 'TimeSeriesServiceStatistics' => [ 'shape' => 'TimeSeriesServiceStatisticsList', ], 'ContainsOldGroupVersions' => [ 'shape' => 'Boolean', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'GetTraceGraphRequest' => [ 'type' => 'structure', 'required' => [ 'TraceIds', ], 'members' => [ 'TraceIds' => [ 'shape' => 'TraceIdList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'GetTraceGraphResult' => [ 'type' => 'structure', 'members' => [ 'Services' => [ 'shape' => 'ServiceList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'GetTraceSegmentDestinationRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetTraceSegmentDestinationResult' => [ 'type' => 'structure', 'members' => [ 'Destination' => [ 'shape' => 'TraceSegmentDestination', ], 'Status' => [ 'shape' => 'TraceSegmentDestinationStatus', ], ], ], 'GetTraceSummariesRequest' => [ 'type' => 'structure', 'required' => [ 'StartTime', 'EndTime', ], 'members' => [ 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'TimeRangeType' => [ 'shape' => 'TimeRangeType', ], 'Sampling' => [ 'shape' => 'NullableBoolean', ], 'SamplingStrategy' => [ 'shape' => 'SamplingStrategy', ], 'FilterExpression' => [ 'shape' => 'FilterExpression', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'GetTraceSummariesResult' => [ 'type' => 'structure', 'members' => [ 'TraceSummaries' => [ 'shape' => 'TraceSummaryList', ], 'ApproximateTime' => [ 'shape' => 'Timestamp', ], 'TracesProcessedCount' => [ 'shape' => 'NullableLong', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'GraphLink' => [ 'type' => 'structure', 'members' => [ 'ReferenceType' => [ 'shape' => 'String', ], 'SourceTraceId' => [ 'shape' => 'String', ], 'DestinationTraceIds' => [ 'shape' => 'TraceIdList', ], ], ], 'Group' => [ 'type' => 'structure', 'members' => [ 'GroupName' => [ 'shape' => 'String', ], 'GroupARN' => [ 'shape' => 'String', ], 'FilterExpression' => [ 'shape' => 'String', ], 'InsightsConfiguration' => [ 'shape' => 'InsightsConfiguration', ], ], ], 'GroupARN' => [ 'type' => 'string', 'max' => 400, 'min' => 1, ], 'GroupName' => [ 'type' => 'string', 'max' => 32, 'min' => 1, ], 'GroupSummary' => [ 'type' => 'structure', 'members' => [ 'GroupName' => [ 'shape' => 'String', ], 'GroupARN' => [ 'shape' => 'String', ], 'FilterExpression' => [ 'shape' => 'String', ], 'InsightsConfiguration' => [ 'shape' => 'InsightsConfiguration', ], ], ], 'GroupSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroupSummary', ], ], 'HTTPMethod' => [ 'type' => 'string', 'max' => 10, ], 'Histogram' => [ 'type' => 'list', 'member' => [ 'shape' => 'HistogramEntry', ], ], 'HistogramEntry' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'Double', ], 'Count' => [ 'shape' => 'Integer', ], ], ], 'Host' => [ 'type' => 'string', 'max' => 64, ], 'Hostname' => [ 'type' => 'string', 'max' => 255, ], 'Http' => [ 'type' => 'structure', 'members' => [ 'HttpURL' => [ 'shape' => 'String', ], 'HttpStatus' => [ 'shape' => 'NullableInteger', ], 'HttpMethod' => [ 'shape' => 'String', ], 'UserAgent' => [ 'shape' => 'String', ], 'ClientIp' => [ 'shape' => 'String', ], ], ], 'IndexingRule' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'RuleName', ], 'ModifiedAt' => [ 'shape' => 'Timestamp', ], 'Rule' => [ 'shape' => 'IndexingRuleValue', ], ], ], 'IndexingRuleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IndexingRule', ], 'max' => 10, 'min' => 1, ], 'IndexingRuleValue' => [ 'type' => 'structure', 'members' => [ 'Probabilistic' => [ 'shape' => 'ProbabilisticRuleValue', ], ], 'union' => true, ], 'IndexingRuleValueUpdate' => [ 'type' => 'structure', 'members' => [ 'Probabilistic' => [ 'shape' => 'ProbabilisticRuleValueUpdate', ], ], 'union' => true, ], 'Insight' => [ 'type' => 'structure', 'members' => [ 'InsightId' => [ 'shape' => 'InsightId', ], 'GroupARN' => [ 'shape' => 'GroupARN', ], 'GroupName' => [ 'shape' => 'GroupName', ], 'RootCauseServiceId' => [ 'shape' => 'ServiceId', ], 'Categories' => [ 'shape' => 'InsightCategoryList', ], 'State' => [ 'shape' => 'InsightState', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'Summary' => [ 'shape' => 'InsightSummaryText', ], 'ClientRequestImpactStatistics' => [ 'shape' => 'RequestImpactStatistics', ], 'RootCauseServiceRequestImpactStatistics' => [ 'shape' => 'RequestImpactStatistics', ], 'TopAnomalousServices' => [ 'shape' => 'AnomalousServiceList', ], ], ], 'InsightCategory' => [ 'type' => 'string', 'enum' => [ 'FAULT', ], ], 'InsightCategoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InsightCategory', ], ], 'InsightEvent' => [ 'type' => 'structure', 'members' => [ 'Summary' => [ 'shape' => 'EventSummaryText', ], 'EventTime' => [ 'shape' => 'Timestamp', ], 'ClientRequestImpactStatistics' => [ 'shape' => 'RequestImpactStatistics', ], 'RootCauseServiceRequestImpactStatistics' => [ 'shape' => 'RequestImpactStatistics', ], 'TopAnomalousServices' => [ 'shape' => 'AnomalousServiceList', ], ], ], 'InsightEventList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InsightEvent', ], ], 'InsightId' => [ 'type' => 'string', 'pattern' => '[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-5][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}', ], 'InsightImpactGraphEdge' => [ 'type' => 'structure', 'members' => [ 'ReferenceId' => [ 'shape' => 'NullableInteger', ], ], ], 'InsightImpactGraphEdgeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InsightImpactGraphEdge', ], ], 'InsightImpactGraphService' => [ 'type' => 'structure', 'members' => [ 'ReferenceId' => [ 'shape' => 'NullableInteger', ], 'Type' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'String', ], 'Names' => [ 'shape' => 'ServiceNames', ], 'AccountId' => [ 'shape' => 'String', ], 'Edges' => [ 'shape' => 'InsightImpactGraphEdgeList', ], ], ], 'InsightImpactGraphServiceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InsightImpactGraphService', ], ], 'InsightState' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'CLOSED', ], ], 'InsightStateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InsightState', ], 'max' => 1, 'min' => 0, ], 'InsightSummary' => [ 'type' => 'structure', 'members' => [ 'InsightId' => [ 'shape' => 'InsightId', ], 'GroupARN' => [ 'shape' => 'GroupARN', ], 'GroupName' => [ 'shape' => 'GroupName', ], 'RootCauseServiceId' => [ 'shape' => 'ServiceId', ], 'Categories' => [ 'shape' => 'InsightCategoryList', ], 'State' => [ 'shape' => 'InsightState', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'Summary' => [ 'shape' => 'InsightSummaryText', ], 'ClientRequestImpactStatistics' => [ 'shape' => 'RequestImpactStatistics', ], 'RootCauseServiceRequestImpactStatistics' => [ 'shape' => 'RequestImpactStatistics', ], 'TopAnomalousServices' => [ 'shape' => 'AnomalousServiceList', ], 'LastUpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'InsightSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InsightSummary', ], ], 'InsightSummaryText' => [ 'type' => 'string', ], 'InsightsConfiguration' => [ 'type' => 'structure', 'members' => [ 'InsightsEnabled' => [ 'shape' => 'NullableBoolean', ], 'NotificationsEnabled' => [ 'shape' => 'NullableBoolean', ], ], ], 'InstanceIdDetail' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'String', ], ], ], 'Integer' => [ 'type' => 'integer', ], 'InvalidPolicyRevisionIdException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidRequestException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'LinksList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GraphLink', ], 'max' => 100, 'min' => 0, ], 'ListResourcePoliciesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'ResourcePolicyNextToken', ], ], ], 'ListResourcePoliciesResult' => [ 'type' => 'structure', 'members' => [ 'ResourcePolicies' => [ 'shape' => 'ResourcePolicyList', ], 'NextToken' => [ 'shape' => 'ResourcePolicyNextToken', ], ], ], 'ListRetrievedTracesRequest' => [ 'type' => 'structure', 'required' => [ 'RetrievalToken', ], 'members' => [ 'RetrievalToken' => [ 'shape' => 'RetrievalToken', ], 'TraceFormat' => [ 'shape' => 'TraceFormatType', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListRetrievedTracesResult' => [ 'type' => 'structure', 'members' => [ 'RetrievalStatus' => [ 'shape' => 'RetrievalStatus', ], 'TraceFormat' => [ 'shape' => 'TraceFormatType', ], 'Traces' => [ 'shape' => 'TraceSpanList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'LockoutPreventionException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'MalformedPolicyDocumentException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'NullableBoolean' => [ 'type' => 'boolean', ], 'NullableDouble' => [ 'type' => 'double', ], 'NullableInteger' => [ 'type' => 'integer', ], 'NullableLong' => [ 'type' => 'long', ], 'PolicyCountLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'PolicyDocument' => [ 'type' => 'string', ], 'PolicyName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\w+=,.@-]+', ], 'PolicyRevisionId' => [ 'type' => 'string', ], 'PolicySizeLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'Priority' => [ 'type' => 'integer', 'max' => 9999, 'min' => 1, ], 'ProbabilisticRuleValue' => [ 'type' => 'structure', 'required' => [ 'DesiredSamplingPercentage', ], 'members' => [ 'DesiredSamplingPercentage' => [ 'shape' => 'NullableDouble', ], 'ActualSamplingPercentage' => [ 'shape' => 'NullableDouble', ], ], ], 'ProbabilisticRuleValueUpdate' => [ 'type' => 'structure', 'required' => [ 'DesiredSamplingPercentage', ], 'members' => [ 'DesiredSamplingPercentage' => [ 'shape' => 'NullableDouble', ], ], ], 'PutEncryptionConfigRequest' => [ 'type' => 'structure', 'required' => [ 'Type', ], 'members' => [ 'KeyId' => [ 'shape' => 'EncryptionKeyId', ], 'Type' => [ 'shape' => 'EncryptionType', ], ], ], 'PutEncryptionConfigResult' => [ 'type' => 'structure', 'members' => [ 'EncryptionConfig' => [ 'shape' => 'EncryptionConfig', ], ], ], 'PutResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'PolicyName', 'PolicyDocument', ], 'members' => [ 'PolicyName' => [ 'shape' => 'PolicyName', ], 'PolicyDocument' => [ 'shape' => 'PolicyDocument', ], 'PolicyRevisionId' => [ 'shape' => 'PolicyRevisionId', ], 'BypassPolicyLockoutCheck' => [ 'shape' => 'Boolean', ], ], ], 'PutResourcePolicyResult' => [ 'type' => 'structure', 'members' => [ 'ResourcePolicy' => [ 'shape' => 'ResourcePolicy', ], ], ], 'PutTelemetryRecordsRequest' => [ 'type' => 'structure', 'required' => [ 'TelemetryRecords', ], 'members' => [ 'TelemetryRecords' => [ 'shape' => 'TelemetryRecordList', ], 'EC2InstanceId' => [ 'shape' => 'EC2InstanceId', ], 'Hostname' => [ 'shape' => 'Hostname', ], 'ResourceARN' => [ 'shape' => 'ResourceARN', ], ], ], 'PutTelemetryRecordsResult' => [ 'type' => 'structure', 'members' => [], ], 'PutTraceSegmentsRequest' => [ 'type' => 'structure', 'required' => [ 'TraceSegmentDocuments', ], 'members' => [ 'TraceSegmentDocuments' => [ 'shape' => 'TraceSegmentDocumentList', ], ], ], 'PutTraceSegmentsResult' => [ 'type' => 'structure', 'members' => [ 'UnprocessedTraceSegments' => [ 'shape' => 'UnprocessedTraceSegmentList', ], ], ], 'RequestCount' => [ 'type' => 'integer', 'min' => 0, ], 'RequestImpactStatistics' => [ 'type' => 'structure', 'members' => [ 'FaultCount' => [ 'shape' => 'NullableLong', ], 'OkCount' => [ 'shape' => 'NullableLong', ], 'TotalCount' => [ 'shape' => 'NullableLong', ], ], ], 'ReservoirSize' => [ 'type' => 'integer', 'min' => 0, ], 'ResourceARN' => [ 'type' => 'string', 'max' => 500, ], 'ResourceARNDetail' => [ 'type' => 'structure', 'members' => [ 'ARN' => [ 'shape' => 'String', ], ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], 'ResourceName' => [ 'shape' => 'AmazonResourceName', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'ResourcePolicy' => [ 'type' => 'structure', 'members' => [ 'PolicyName' => [ 'shape' => 'PolicyName', ], 'PolicyDocument' => [ 'shape' => 'PolicyDocument', ], 'PolicyRevisionId' => [ 'shape' => 'PolicyRevisionId', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], ], ], 'ResourcePolicyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourcePolicy', ], ], 'ResourcePolicyNextToken' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'ResponseTimeRootCause' => [ 'type' => 'structure', 'members' => [ 'Services' => [ 'shape' => 'ResponseTimeRootCauseServices', ], 'ClientImpacting' => [ 'shape' => 'NullableBoolean', ], ], ], 'ResponseTimeRootCauseEntity' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Coverage' => [ 'shape' => 'NullableDouble', ], 'Remote' => [ 'shape' => 'NullableBoolean', ], ], ], 'ResponseTimeRootCauseEntityPath' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResponseTimeRootCauseEntity', ], ], 'ResponseTimeRootCauseService' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Names' => [ 'shape' => 'ServiceNames', ], 'Type' => [ 'shape' => 'String', ], 'AccountId' => [ 'shape' => 'String', ], 'EntityPath' => [ 'shape' => 'ResponseTimeRootCauseEntityPath', ], 'Inferred' => [ 'shape' => 'NullableBoolean', ], ], ], 'ResponseTimeRootCauseServices' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResponseTimeRootCauseService', ], ], 'ResponseTimeRootCauses' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResponseTimeRootCause', ], ], 'RetrievalStatus' => [ 'type' => 'string', 'enum' => [ 'SCHEDULED', 'RUNNING', 'COMPLETE', 'FAILED', 'CANCELLED', 'TIMEOUT', ], ], 'RetrievalToken' => [ 'type' => 'string', 'max' => 1020, 'min' => 0, ], 'RetrievedService' => [ 'type' => 'structure', 'members' => [ 'Service' => [ 'shape' => 'Service', ], 'Links' => [ 'shape' => 'LinksList', ], ], ], 'RetrievedServicesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RetrievedService', ], 'max' => 1000, 'min' => 0, ], 'RetrievedTrace' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'TraceId', ], 'Duration' => [ 'shape' => 'NullableDouble', ], 'Spans' => [ 'shape' => 'SpanList', ], ], ], 'RootCauseException' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], ], 'RootCauseExceptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'RootCauseException', ], ], 'RuleLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'RuleName' => [ 'type' => 'string', 'max' => 32, 'min' => 1, ], 'SampledCount' => [ 'type' => 'integer', 'min' => 0, ], 'SamplingRule' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'Priority', 'FixedRate', 'ReservoirSize', 'ServiceName', 'ServiceType', 'Host', 'HTTPMethod', 'URLPath', 'Version', ], 'members' => [ 'RuleName' => [ 'shape' => 'RuleName', ], 'RuleARN' => [ 'shape' => 'String', ], 'ResourceARN' => [ 'shape' => 'ResourceARN', ], 'Priority' => [ 'shape' => 'Priority', ], 'FixedRate' => [ 'shape' => 'FixedRate', ], 'ReservoirSize' => [ 'shape' => 'ReservoirSize', ], 'ServiceName' => [ 'shape' => 'ServiceName', ], 'ServiceType' => [ 'shape' => 'ServiceType', ], 'Host' => [ 'shape' => 'Host', ], 'HTTPMethod' => [ 'shape' => 'HTTPMethod', ], 'URLPath' => [ 'shape' => 'URLPath', ], 'Version' => [ 'shape' => 'Version', ], 'Attributes' => [ 'shape' => 'AttributeMap', ], ], ], 'SamplingRuleRecord' => [ 'type' => 'structure', 'members' => [ 'SamplingRule' => [ 'shape' => 'SamplingRule', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'ModifiedAt' => [ 'shape' => 'Timestamp', ], ], ], 'SamplingRuleRecordList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SamplingRuleRecord', ], ], 'SamplingRuleUpdate' => [ 'type' => 'structure', 'members' => [ 'RuleName' => [ 'shape' => 'RuleName', ], 'RuleARN' => [ 'shape' => 'String', ], 'ResourceARN' => [ 'shape' => 'ResourceARN', ], 'Priority' => [ 'shape' => 'NullableInteger', ], 'FixedRate' => [ 'shape' => 'NullableDouble', ], 'ReservoirSize' => [ 'shape' => 'NullableInteger', ], 'Host' => [ 'shape' => 'Host', ], 'ServiceName' => [ 'shape' => 'ServiceName', ], 'ServiceType' => [ 'shape' => 'ServiceType', ], 'HTTPMethod' => [ 'shape' => 'HTTPMethod', ], 'URLPath' => [ 'shape' => 'URLPath', ], 'Attributes' => [ 'shape' => 'AttributeMap', ], ], ], 'SamplingStatisticSummary' => [ 'type' => 'structure', 'members' => [ 'RuleName' => [ 'shape' => 'String', ], 'Timestamp' => [ 'shape' => 'Timestamp', ], 'RequestCount' => [ 'shape' => 'Integer', ], 'BorrowCount' => [ 'shape' => 'Integer', ], 'SampledCount' => [ 'shape' => 'Integer', ], ], ], 'SamplingStatisticSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SamplingStatisticSummary', ], ], 'SamplingStatisticsDocument' => [ 'type' => 'structure', 'required' => [ 'RuleName', 'ClientID', 'Timestamp', 'RequestCount', 'SampledCount', ], 'members' => [ 'RuleName' => [ 'shape' => 'RuleName', ], 'ClientID' => [ 'shape' => 'ClientID', ], 'Timestamp' => [ 'shape' => 'Timestamp', ], 'RequestCount' => [ 'shape' => 'RequestCount', ], 'SampledCount' => [ 'shape' => 'SampledCount', ], 'BorrowCount' => [ 'shape' => 'BorrowCount', ], ], ], 'SamplingStatisticsDocumentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SamplingStatisticsDocument', ], 'max' => 25, ], 'SamplingStrategy' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'SamplingStrategyName', ], 'Value' => [ 'shape' => 'NullableDouble', ], ], ], 'SamplingStrategyName' => [ 'type' => 'string', 'enum' => [ 'PartialScan', 'FixedRate', ], ], 'SamplingTargetDocument' => [ 'type' => 'structure', 'members' => [ 'RuleName' => [ 'shape' => 'String', ], 'FixedRate' => [ 'shape' => 'Double', ], 'ReservoirQuota' => [ 'shape' => 'NullableInteger', ], 'ReservoirQuotaTTL' => [ 'shape' => 'Timestamp', ], 'Interval' => [ 'shape' => 'NullableInteger', ], ], ], 'SamplingTargetDocumentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SamplingTargetDocument', ], ], 'Segment' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'SegmentId', ], 'Document' => [ 'shape' => 'SegmentDocument', ], ], ], 'SegmentDocument' => [ 'type' => 'string', 'min' => 1, ], 'SegmentId' => [ 'type' => 'string', ], 'SegmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Segment', ], ], 'Service' => [ 'type' => 'structure', 'members' => [ 'ReferenceId' => [ 'shape' => 'NullableInteger', ], 'Name' => [ 'shape' => 'String', ], 'Names' => [ 'shape' => 'ServiceNames', ], 'Root' => [ 'shape' => 'NullableBoolean', ], 'AccountId' => [ 'shape' => 'String', ], 'Type' => [ 'shape' => 'String', ], 'State' => [ 'shape' => 'String', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'Edges' => [ 'shape' => 'EdgeList', ], 'SummaryStatistics' => [ 'shape' => 'ServiceStatistics', ], 'DurationHistogram' => [ 'shape' => 'Histogram', ], 'ResponseTimeHistogram' => [ 'shape' => 'Histogram', ], ], ], 'ServiceId' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Names' => [ 'shape' => 'ServiceNames', ], 'AccountId' => [ 'shape' => 'String', ], 'Type' => [ 'shape' => 'String', ], ], ], 'ServiceIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServiceId', ], ], 'ServiceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Service', ], ], 'ServiceName' => [ 'type' => 'string', 'max' => 64, ], 'ServiceNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ServiceStatistics' => [ 'type' => 'structure', 'members' => [ 'OkCount' => [ 'shape' => 'NullableLong', ], 'ErrorStatistics' => [ 'shape' => 'ErrorStatistics', ], 'FaultStatistics' => [ 'shape' => 'FaultStatistics', ], 'TotalCount' => [ 'shape' => 'NullableLong', ], 'TotalResponseTime' => [ 'shape' => 'NullableDouble', ], ], ], 'ServiceType' => [ 'type' => 'string', 'max' => 64, ], 'Span' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'SpanId', ], 'Document' => [ 'shape' => 'SpanDocument', ], ], ], 'SpanDocument' => [ 'type' => 'string', 'max' => 204800, ], 'SpanId' => [ 'type' => 'string', 'max' => 16, 'min' => 1, ], 'SpanList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Span', ], 'max' => 100, 'min' => 0, ], 'StartTraceRetrievalRequest' => [ 'type' => 'structure', 'required' => [ 'TraceIds', 'StartTime', 'EndTime', ], 'members' => [ 'TraceIds' => [ 'shape' => 'TraceIdListForRetrieval', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], ], ], 'StartTraceRetrievalResult' => [ 'type' => 'structure', 'members' => [ 'RetrievalToken' => [ 'shape' => 'RetrievalToken', ], ], ], 'String' => [ 'type' => 'string', ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'Tags', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TelemetryRecord' => [ 'type' => 'structure', 'required' => [ 'Timestamp', ], 'members' => [ 'Timestamp' => [ 'shape' => 'Timestamp', ], 'SegmentsReceivedCount' => [ 'shape' => 'NullableInteger', ], 'SegmentsSentCount' => [ 'shape' => 'NullableInteger', ], 'SegmentsSpilloverCount' => [ 'shape' => 'NullableInteger', ], 'SegmentsRejectedCount' => [ 'shape' => 'NullableInteger', ], 'BackendConnectionErrors' => [ 'shape' => 'BackendConnectionErrors', ], ], ], 'TelemetryRecordList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TelemetryRecord', ], ], 'ThrottledException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'TimeRangeType' => [ 'type' => 'string', 'enum' => [ 'TraceId', 'Event', 'Service', ], ], 'TimeSeriesServiceStatistics' => [ 'type' => 'structure', 'members' => [ 'Timestamp' => [ 'shape' => 'Timestamp', ], 'EdgeSummaryStatistics' => [ 'shape' => 'EdgeStatistics', ], 'ServiceSummaryStatistics' => [ 'shape' => 'ServiceStatistics', ], 'ServiceForecastStatistics' => [ 'shape' => 'ForecastStatistics', ], 'ResponseTimeHistogram' => [ 'shape' => 'Histogram', ], ], ], 'TimeSeriesServiceStatisticsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TimeSeriesServiceStatistics', ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'Token' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, ], 'TooManyTagsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], 'ResourceName' => [ 'shape' => 'AmazonResourceName', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'Trace' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'TraceId', ], 'Duration' => [ 'shape' => 'NullableDouble', ], 'LimitExceeded' => [ 'shape' => 'NullableBoolean', ], 'Segments' => [ 'shape' => 'SegmentList', ], ], ], 'TraceAvailabilityZones' => [ 'type' => 'list', 'member' => [ 'shape' => 'AvailabilityZoneDetail', ], ], 'TraceFormatType' => [ 'type' => 'string', 'enum' => [ 'XRAY', 'OTEL', ], ], 'TraceId' => [ 'type' => 'string', 'max' => 35, 'min' => 1, ], 'TraceIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TraceId', ], ], 'TraceIdListForRetrieval' => [ 'type' => 'list', 'member' => [ 'shape' => 'TraceId', ], 'max' => 100, 'min' => 0, ], 'TraceInstanceIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceIdDetail', ], ], 'TraceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Trace', ], ], 'TraceResourceARNs' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceARNDetail', ], ], 'TraceSegmentDestination' => [ 'type' => 'string', 'enum' => [ 'XRay', 'CloudWatchLogs', ], ], 'TraceSegmentDestinationStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'ACTIVE', ], ], 'TraceSegmentDocument' => [ 'type' => 'string', ], 'TraceSegmentDocumentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TraceSegmentDocument', ], ], 'TraceSpanList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RetrievedTrace', ], 'max' => 5, 'min' => 0, ], 'TraceSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'TraceId', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'Duration' => [ 'shape' => 'NullableDouble', ], 'ResponseTime' => [ 'shape' => 'NullableDouble', ], 'HasFault' => [ 'shape' => 'NullableBoolean', ], 'HasError' => [ 'shape' => 'NullableBoolean', ], 'HasThrottle' => [ 'shape' => 'NullableBoolean', ], 'IsPartial' => [ 'shape' => 'NullableBoolean', ], 'Http' => [ 'shape' => 'Http', ], 'Annotations' => [ 'shape' => 'Annotations', ], 'Users' => [ 'shape' => 'TraceUsers', ], 'ServiceIds' => [ 'shape' => 'ServiceIds', ], 'ResourceARNs' => [ 'shape' => 'TraceResourceARNs', ], 'InstanceIds' => [ 'shape' => 'TraceInstanceIds', ], 'AvailabilityZones' => [ 'shape' => 'TraceAvailabilityZones', ], 'EntryPoint' => [ 'shape' => 'ServiceId', ], 'FaultRootCauses' => [ 'shape' => 'FaultRootCauses', ], 'ErrorRootCauses' => [ 'shape' => 'ErrorRootCauses', ], 'ResponseTimeRootCauses' => [ 'shape' => 'ResponseTimeRootCauses', ], 'Revision' => [ 'shape' => 'Integer', ], 'MatchedEventTime' => [ 'shape' => 'Timestamp', ], ], ], 'TraceSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TraceSummary', ], ], 'TraceUser' => [ 'type' => 'structure', 'members' => [ 'UserName' => [ 'shape' => 'String', ], 'ServiceIds' => [ 'shape' => 'ServiceIds', ], ], ], 'TraceUsers' => [ 'type' => 'list', 'member' => [ 'shape' => 'TraceUser', ], ], 'URLPath' => [ 'type' => 'string', 'max' => 128, ], 'UnprocessedStatistics' => [ 'type' => 'structure', 'members' => [ 'RuleName' => [ 'shape' => 'String', ], 'ErrorCode' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], ], 'UnprocessedStatisticsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UnprocessedStatistics', ], ], 'UnprocessedTraceIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TraceId', ], ], 'UnprocessedTraceSegment' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'String', ], 'ErrorCode' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], ], 'UnprocessedTraceSegmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UnprocessedTraceSegment', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'TagKeys', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateGroupRequest' => [ 'type' => 'structure', 'members' => [ 'GroupName' => [ 'shape' => 'GroupName', ], 'GroupARN' => [ 'shape' => 'GroupARN', ], 'FilterExpression' => [ 'shape' => 'FilterExpression', ], 'InsightsConfiguration' => [ 'shape' => 'InsightsConfiguration', ], ], ], 'UpdateGroupResult' => [ 'type' => 'structure', 'members' => [ 'Group' => [ 'shape' => 'Group', ], ], ], 'UpdateIndexingRuleRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Rule', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Rule' => [ 'shape' => 'IndexingRuleValueUpdate', ], ], ], 'UpdateIndexingRuleResult' => [ 'type' => 'structure', 'members' => [ 'IndexingRule' => [ 'shape' => 'IndexingRule', ], ], ], 'UpdateSamplingRuleRequest' => [ 'type' => 'structure', 'required' => [ 'SamplingRuleUpdate', ], 'members' => [ 'SamplingRuleUpdate' => [ 'shape' => 'SamplingRuleUpdate', ], ], ], 'UpdateSamplingRuleResult' => [ 'type' => 'structure', 'members' => [ 'SamplingRuleRecord' => [ 'shape' => 'SamplingRuleRecord', ], ], ], 'UpdateTraceSegmentDestinationRequest' => [ 'type' => 'structure', 'members' => [ 'Destination' => [ 'shape' => 'TraceSegmentDestination', ], ], ], 'UpdateTraceSegmentDestinationResult' => [ 'type' => 'structure', 'members' => [ 'Destination' => [ 'shape' => 'TraceSegmentDestination', ], 'Status' => [ 'shape' => 'TraceSegmentDestinationStatus', ], ], ], 'ValueWithServiceIds' => [ 'type' => 'structure', 'members' => [ 'AnnotationValue' => [ 'shape' => 'AnnotationValue', ], 'ServiceIds' => [ 'shape' => 'ServiceIds', ], ], ], 'ValuesWithServiceIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValueWithServiceIds', ], ], 'Version' => [ 'type' => 'integer', 'min' => 1, ], ],];
