{"name": "barryvdh/laravel-dompdf", "description": "A DOMPDF Wrapper for Laravel", "license": "MIT", "keywords": ["laravel", "dompdf", "pdf"], "authors": [{"name": "Barry vd. Heuvel", "email": "<EMAIL>"}], "require": {"php": "^8.1", "dompdf/dompdf": "^3.0", "illuminate/support": "^9|^10|^11|^12"}, "require-dev": {"orchestra/testbench": "^7|^8|^9|^10", "squizlabs/php_codesniffer": "^3.5", "phpro/grumphp": "^2.5", "larastan/larastan": "^2.7|^3.0"}, "autoload": {"psr-4": {"Barryvdh\\DomPDF\\": "src"}}, "autoload-dev": {"psr-4": {"Barryvdh\\DomPDF\\Tests\\": "tests"}}, "extra": {"branch-alias": {"dev-master": "3.0-dev"}, "laravel": {"providers": ["Barryvdh\\DomPDF\\ServiceProvider"], "aliases": {"Pdf": "Barryvdh\\DomPDF\\Facade\\Pdf", "PDF": "Barryvdh\\DomPDF\\Facade\\Pdf"}}}, "scripts": {"test": "phpunit", "check-style": "phpcs -p --standard=psr12 src/", "fix-style": "phpcbf -p --standard=psr12 src/", "phpstan": "phpstan analyze --memory-limit=-1"}, "minimum-stability": "dev", "prefer-stable": true, "config": {"allow-plugins": {"phpro/grumphp": true}}}