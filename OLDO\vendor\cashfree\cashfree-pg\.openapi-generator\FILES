.gitignore
.openapi-generator-ignore
.php-cs-fixer.dist.php
.travis.yml
README.md
composer.json
docs/Api/CustomersApi.md
docs/Api/EasySplitApi.md
docs/Api/EligibilityApi.md
docs/Api/OffersApi.md
docs/Api/OrdersApi.md
docs/Api/PGReconciliationApi.md
docs/Api/PaymentLinksApi.md
docs/Api/PaymentsApi.md
docs/Api/RefundsApi.md
docs/Api/SettlementReconciliationApi.md
docs/Api/SettlementsApi.md
docs/Api/SimulationApi.md
docs/Api/SoftPOSApi.md
docs/Api/SubscriptionApi.md
docs/Api/TokenVaultApi.md
docs/Model/AdjustVendorBalanceRequest.md
docs/Model/AdjustVendorBalanceResponse.md
docs/Model/ApiError.md
docs/Model/ApiError404.md
docs/Model/ApiError409.md
docs/Model/ApiError502.md
docs/Model/App.md
docs/Model/AppPaymentMethod.md
docs/Model/AuthenticationError.md
docs/Model/AuthorizationDetails.md
docs/Model/AuthorizationInPaymentsEntity.md
docs/Model/AuthorizeOrderRequest.md
docs/Model/BadRequestError.md
docs/Model/BalanceDetails.md
docs/Model/BankDetails.md
docs/Model/Banktransfer.md
docs/Model/BanktransferPaymentMethod.md
docs/Model/Card.md
docs/Model/CardEMI.md
docs/Model/CardEMIPaymentMethod.md
docs/Model/CardOffer.md
docs/Model/CardPaymentMethod.md
docs/Model/CardlessEMI.md
docs/Model/CardlessEMIEntity.md
docs/Model/CardlessEMIPaymentMethod.md
docs/Model/CardlessEMIQueries.md
docs/Model/CashbackDetails.md
docs/Model/ChargesDetails.md
docs/Model/CreateCustomerRequest.md
docs/Model/CreateLinkRequest.md
docs/Model/CreateOfferRequest.md
docs/Model/CreateOrderRequest.md
docs/Model/CreateOrderSettlementRequestBody.md
docs/Model/CreateOrderSettlementRequestBodyMetaData.md
docs/Model/CreatePlanRequest.md
docs/Model/CreateSubscriptionPaymentAuthResponse.md
docs/Model/CreateSubscriptionPaymentAuthResponseFailureDetails.md
docs/Model/CreateSubscriptionPaymentChargeResponse.md
docs/Model/CreateSubscriptionPaymentRequest.md
docs/Model/CreateSubscriptionRefundRequest.md
docs/Model/CreateSubscriptionRequest.md
docs/Model/CreateSubscriptionRequestAuthorizationDetails.md
docs/Model/CreateSubscriptionRequestPlanDetails.md
docs/Model/CreateSubscriptionRequestSubscriptionMeta.md
docs/Model/CreateTerminalRequest.md
docs/Model/CreateTerminalRequestTerminalMeta.md
docs/Model/CreateTerminalTransactionRequest.md
docs/Model/CreateVendorRequest.md
docs/Model/CreateVendorResponse.md
docs/Model/CryptogramEntity.md
docs/Model/CustomerDetails.md
docs/Model/CustomerDetailsCardlessEMI.md
docs/Model/CustomerDetailsResponse.md
docs/Model/CustomerEntity.md
docs/Model/DiscountDetails.md
docs/Model/EMIOffer.md
docs/Model/EMIPlansArray.md
docs/Model/ESOrderReconRequest.md
docs/Model/ESOrderReconRequestFilters.md
docs/Model/ESOrderReconRequestPagination.md
docs/Model/ESOrderReconResponse.md
docs/Model/ESOrderReconResponseDataInner.md
docs/Model/ESOrderReconResponseDataInnerOrderSplitsInner.md
docs/Model/ESOrderReconResponseDataInnerOrderSplitsInnerSplitInner.md
docs/Model/EligibilityCardlessEMIEntity.md
docs/Model/EligibilityFetchCardlessEMIRequest.md
docs/Model/EligibilityFetchOffersRequest.md
docs/Model/EligibilityFetchPaylaterRequest.md
docs/Model/EligibilityFetchPaymentMethodsRequest.md
docs/Model/EligibilityMethodItem.md
docs/Model/EligibilityMethodItemEntityDetails.md
docs/Model/EligibilityMethodItemEntityDetailsAvailableHandlesInner.md
docs/Model/EligibilityOfferEntity.md
docs/Model/EligibilityPaylaterEntity.md
docs/Model/EligibilityPaymentMethodsEntity.md
docs/Model/EligibilityPaymentMethodsEntityEntityDetails.md
docs/Model/EntitySimulationRequest.md
docs/Model/EntitySimulationResponse.md
docs/Model/ErrorDetailsInPaymentsEntity.md
docs/Model/FetchReconRequest.md
docs/Model/FetchReconRequestFilters.md
docs/Model/FetchReconRequestPagination.md
docs/Model/FetchSettlementsRequest.md
docs/Model/FetchSettlementsRequestFilters.md
docs/Model/FetchSettlementsRequestPagination.md
docs/Model/FetchTerminalQRCodesEntity.md
docs/Model/IdempotencyError.md
docs/Model/InstrumentEntity.md
docs/Model/InstrumentWebhook.md
docs/Model/InstrumentWebhookData.md
docs/Model/InstrumentWebhookDataEntity.md
docs/Model/KycDetails.md
docs/Model/LinkCustomerDetailsEntity.md
docs/Model/LinkEntity.md
docs/Model/LinkMetaResponseEntity.md
docs/Model/LinkNotifyEntity.md
docs/Model/ManageSubscriptionPaymentRequest.md
docs/Model/ManageSubscriptionPaymentRequestActionDetails.md
docs/Model/ManageSubscriptionRequest.md
docs/Model/ManageSubscriptionRequestActionDetails.md
docs/Model/NetBankingPaymentMethod.md
docs/Model/Netbanking.md
docs/Model/OfferAll.md
docs/Model/OfferCard.md
docs/Model/OfferDetails.md
docs/Model/OfferEMI.md
docs/Model/OfferEntity.md
docs/Model/OfferFilters.md
docs/Model/OfferMeta.md
docs/Model/OfferNB.md
docs/Model/OfferNBNetbanking.md
docs/Model/OfferPaylater.md
docs/Model/OfferQueries.md
docs/Model/OfferTnc.md
docs/Model/OfferType.md
docs/Model/OfferUPI.md
docs/Model/OfferValidations.md
docs/Model/OfferValidationsPaymentMethod.md
docs/Model/OfferWallet.md
docs/Model/OrderAuthenticateEntity.md
docs/Model/OrderAuthenticatePaymentRequest.md
docs/Model/OrderCreateRefundRequest.md
docs/Model/OrderEntity.md
docs/Model/OrderMeta.md
docs/Model/OrderPayData.md
docs/Model/PayOrderEntity.md
docs/Model/PayOrderRequest.md
docs/Model/PayOrderRequestPaymentMethod.md
docs/Model/Paylater.md
docs/Model/PaylaterEntity.md
docs/Model/PaylaterOffer.md
docs/Model/PaylaterPaymentMethod.md
docs/Model/PaymentEntity.md
docs/Model/PaymentEntityPaymentMethod.md
docs/Model/PaymentLinkCustomerDetails.md
docs/Model/PaymentLinkOrderEntity.md
docs/Model/PaymentMethodAppInPaymentsEntity.md
docs/Model/PaymentMethodAppInPaymentsEntityApp.md
docs/Model/PaymentMethodBankTransferInPaymentsEntity.md
docs/Model/PaymentMethodBankTransferInPaymentsEntityBanktransfer.md
docs/Model/PaymentMethodCardEMIInPaymentsEntity.md
docs/Model/PaymentMethodCardEMIInPaymentsEntityEmi.md
docs/Model/PaymentMethodCardEMIInPaymentsEntityEmiEmiDetails.md
docs/Model/PaymentMethodCardInPaymentsEntity.md
docs/Model/PaymentMethodCardInPaymentsEntityCard.md
docs/Model/PaymentMethodCardlessEMIInPaymentsEntity.md
docs/Model/PaymentMethodNetBankingInPaymentsEntity.md
docs/Model/PaymentMethodNetBankingInPaymentsEntityNetbanking.md
docs/Model/PaymentMethodPaylaterInPaymentsEntity.md
docs/Model/PaymentMethodUPIInPaymentsEntity.md
docs/Model/PaymentMethodUPIInPaymentsEntityUpi.md
docs/Model/PaymentMethodsFilters.md
docs/Model/PaymentMethodsQueries.md
docs/Model/PaymentModeDetails.md
docs/Model/PaymentWebhook.md
docs/Model/PaymentWebhookCustomerEntity.md
docs/Model/PaymentWebhookDataEntity.md
docs/Model/PaymentWebhookErrorEntity.md
docs/Model/PaymentWebhookGatewayDetailsEntity.md
docs/Model/PaymentWebhookOrderEntity.md
docs/Model/PlanEntity.md
docs/Model/RateLimitError.md
docs/Model/ReconEntity.md
docs/Model/ReconEntityDataInner.md
docs/Model/RefundEntity.md
docs/Model/RefundSpeed.md
docs/Model/RefundWebhook.md
docs/Model/RefundWebhookDataEntity.md
docs/Model/SavedInstrumentMeta.md
docs/Model/ScheduleOption.md
docs/Model/SettlementEntity.md
docs/Model/SettlementFetchReconRequest.md
docs/Model/SettlementReconEntity.md
docs/Model/SettlementReconEntityDataInner.md
docs/Model/SettlementWebhook.md
docs/Model/SettlementWebhookDataEntity.md
docs/Model/SimulateRequest.md
docs/Model/SimulationResponse.md
docs/Model/SplitAfterPaymentRequest.md
docs/Model/SplitAfterPaymentRequestSplitInner.md
docs/Model/SplitAfterPaymentResponse.md
docs/Model/StaticSplitRequest.md
docs/Model/StaticSplitRequestSchemeInner.md
docs/Model/StaticSplitResponse.md
docs/Model/StaticSplitResponseSchemeInner.md
docs/Model/SubsCreatePayment200Response.md
docs/Model/SubscriptionBankDetails.md
docs/Model/SubscriptionCustomerDetails.md
docs/Model/SubscriptionEligibilityRequest.md
docs/Model/SubscriptionEligibilityRequestFilters.md
docs/Model/SubscriptionEligibilityRequestQueries.md
docs/Model/SubscriptionEligibilityResponse.md
docs/Model/SubscriptionEntity.md
docs/Model/SubscriptionEntitySubscriptionMeta.md
docs/Model/SubscriptionPaymentEntity.md
docs/Model/SubscriptionPaymentRefundEntity.md
docs/Model/SubscriptionPaymentSplitItem.md
docs/Model/TerminalDetails.md
docs/Model/TerminalEntity.md
docs/Model/TerminalPaymentEntity.md
docs/Model/TerminalTransactionEntity.md
docs/Model/TerminateOrderRequest.md
docs/Model/TransferDetails.md
docs/Model/TransferDetailsTagsInner.md
docs/Model/UPIAuthorizeDetails.md
docs/Model/UPIPaymentMethod.md
docs/Model/UpdateTerminalEntity.md
docs/Model/UpdateTerminalRequest.md
docs/Model/UpdateTerminalRequestTerminalMeta.md
docs/Model/UpdateTerminalStatusRequest.md
docs/Model/UpdateVendorRequest.md
docs/Model/UpdateVendorResponse.md
docs/Model/UpdateVendorResponseRelatedDocsInner.md
docs/Model/Upi.md
docs/Model/UpiDetails.md
docs/Model/UploadPnachImageResponse.md
docs/Model/UploadTerminalDocs.md
docs/Model/UploadTerminalDocsEntity.md
docs/Model/UploadVendorDocumentsResponse.md
docs/Model/VendorBalance.md
docs/Model/VendorBalanceTransferCharges.md
docs/Model/VendorDocumentDownloadResponse.md
docs/Model/VendorDocumentsResponse.md
docs/Model/VendorEntity.md
docs/Model/VendorSplit.md
docs/Model/WalletOffer.md
git_push.sh
lib/Api/CustomersApi.php
lib/Api/EasySplitApi.php
lib/Api/EligibilityApi.php
lib/Api/OffersApi.php
lib/Api/OrdersApi.php
lib/Api/PGReconciliationApi.php
lib/Api/PaymentLinksApi.php
lib/Api/PaymentsApi.php
lib/Api/RefundsApi.php
lib/Api/SettlementReconciliationApi.php
lib/Api/SettlementsApi.php
lib/Api/SimulationApi.php
lib/Api/SoftPOSApi.php
lib/Api/SubscriptionApi.php
lib/Api/TokenVaultApi.php
lib/ApiException.php
lib/Configuration.php
lib/HeaderSelector.php
lib/Model/AdjustVendorBalanceRequest.php
lib/Model/AdjustVendorBalanceResponse.php
lib/Model/ApiError.php
lib/Model/ApiError404.php
lib/Model/ApiError409.php
lib/Model/ApiError502.php
lib/Model/App.php
lib/Model/AppPaymentMethod.php
lib/Model/AuthenticationError.php
lib/Model/AuthorizationDetails.php
lib/Model/AuthorizationInPaymentsEntity.php
lib/Model/AuthorizeOrderRequest.php
lib/Model/BadRequestError.php
lib/Model/BalanceDetails.php
lib/Model/BankDetails.php
lib/Model/Banktransfer.php
lib/Model/BanktransferPaymentMethod.php
lib/Model/Card.php
lib/Model/CardEMI.php
lib/Model/CardEMIPaymentMethod.php
lib/Model/CardOffer.php
lib/Model/CardPaymentMethod.php
lib/Model/CardlessEMI.php
lib/Model/CardlessEMIEntity.php
lib/Model/CardlessEMIPaymentMethod.php
lib/Model/CardlessEMIQueries.php
lib/Model/CashbackDetails.php
lib/Model/ChargesDetails.php
lib/Model/CreateCustomerRequest.php
lib/Model/CreateLinkRequest.php
lib/Model/CreateOfferRequest.php
lib/Model/CreateOrderRequest.php
lib/Model/CreateOrderSettlementRequestBody.php
lib/Model/CreateOrderSettlementRequestBodyMetaData.php
lib/Model/CreatePlanRequest.php
lib/Model/CreateSubscriptionPaymentAuthResponse.php
lib/Model/CreateSubscriptionPaymentAuthResponseFailureDetails.php
lib/Model/CreateSubscriptionPaymentChargeResponse.php
lib/Model/CreateSubscriptionPaymentRequest.php
lib/Model/CreateSubscriptionRefundRequest.php
lib/Model/CreateSubscriptionRequest.php
lib/Model/CreateSubscriptionRequestAuthorizationDetails.php
lib/Model/CreateSubscriptionRequestPlanDetails.php
lib/Model/CreateSubscriptionRequestSubscriptionMeta.php
lib/Model/CreateTerminalRequest.php
lib/Model/CreateTerminalRequestTerminalMeta.php
lib/Model/CreateTerminalTransactionRequest.php
lib/Model/CreateVendorRequest.php
lib/Model/CreateVendorResponse.php
lib/Model/CryptogramEntity.php
lib/Model/CustomerDetails.php
lib/Model/CustomerDetailsCardlessEMI.php
lib/Model/CustomerDetailsResponse.php
lib/Model/CustomerEntity.php
lib/Model/DiscountDetails.php
lib/Model/EMIOffer.php
lib/Model/EMIPlansArray.php
lib/Model/ESOrderReconRequest.php
lib/Model/ESOrderReconRequestFilters.php
lib/Model/ESOrderReconRequestPagination.php
lib/Model/ESOrderReconResponse.php
lib/Model/ESOrderReconResponseDataInner.php
lib/Model/ESOrderReconResponseDataInnerOrderSplitsInner.php
lib/Model/ESOrderReconResponseDataInnerOrderSplitsInnerSplitInner.php
lib/Model/EligibilityCardlessEMIEntity.php
lib/Model/EligibilityFetchCardlessEMIRequest.php
lib/Model/EligibilityFetchOffersRequest.php
lib/Model/EligibilityFetchPaylaterRequest.php
lib/Model/EligibilityFetchPaymentMethodsRequest.php
lib/Model/EligibilityMethodItem.php
lib/Model/EligibilityMethodItemEntityDetails.php
lib/Model/EligibilityMethodItemEntityDetailsAvailableHandlesInner.php
lib/Model/EligibilityOfferEntity.php
lib/Model/EligibilityPaylaterEntity.php
lib/Model/EligibilityPaymentMethodsEntity.php
lib/Model/EligibilityPaymentMethodsEntityEntityDetails.php
lib/Model/EntitySimulationRequest.php
lib/Model/EntitySimulationResponse.php
lib/Model/ErrorDetailsInPaymentsEntity.php
lib/Model/FetchReconRequest.php
lib/Model/FetchReconRequestFilters.php
lib/Model/FetchReconRequestPagination.php
lib/Model/FetchSettlementsRequest.php
lib/Model/FetchSettlementsRequestFilters.php
lib/Model/FetchSettlementsRequestPagination.php
lib/Model/FetchTerminalQRCodesEntity.php
lib/Model/IdempotencyError.php
lib/Model/InstrumentEntity.php
lib/Model/InstrumentWebhook.php
lib/Model/InstrumentWebhookData.php
lib/Model/InstrumentWebhookDataEntity.php
lib/Model/KycDetails.php
lib/Model/LinkCustomerDetailsEntity.php
lib/Model/LinkEntity.php
lib/Model/LinkMetaResponseEntity.php
lib/Model/LinkNotifyEntity.php
lib/Model/ManageSubscriptionPaymentRequest.php
lib/Model/ManageSubscriptionPaymentRequestActionDetails.php
lib/Model/ManageSubscriptionRequest.php
lib/Model/ManageSubscriptionRequestActionDetails.php
lib/Model/ModelInterface.php
lib/Model/NetBankingPaymentMethod.php
lib/Model/Netbanking.php
lib/Model/OfferAll.php
lib/Model/OfferCard.php
lib/Model/OfferDetails.php
lib/Model/OfferEMI.php
lib/Model/OfferEntity.php
lib/Model/OfferFilters.php
lib/Model/OfferMeta.php
lib/Model/OfferNB.php
lib/Model/OfferNBNetbanking.php
lib/Model/OfferPaylater.php
lib/Model/OfferQueries.php
lib/Model/OfferTnc.php
lib/Model/OfferType.php
lib/Model/OfferUPI.php
lib/Model/OfferValidations.php
lib/Model/OfferValidationsPaymentMethod.php
lib/Model/OfferWallet.php
lib/Model/OrderAuthenticateEntity.php
lib/Model/OrderAuthenticatePaymentRequest.php
lib/Model/OrderCreateRefundRequest.php
lib/Model/OrderEntity.php
lib/Model/OrderMeta.php
lib/Model/OrderPayData.php
lib/Model/PayOrderEntity.php
lib/Model/PayOrderRequest.php
lib/Model/PayOrderRequestPaymentMethod.php
lib/Model/Paylater.php
lib/Model/PaylaterEntity.php
lib/Model/PaylaterOffer.php
lib/Model/PaylaterPaymentMethod.php
lib/Model/PaymentEntity.php
lib/Model/PaymentEntityPaymentMethod.php
lib/Model/PaymentLinkCustomerDetails.php
lib/Model/PaymentLinkOrderEntity.php
lib/Model/PaymentMethodAppInPaymentsEntity.php
lib/Model/PaymentMethodAppInPaymentsEntityApp.php
lib/Model/PaymentMethodBankTransferInPaymentsEntity.php
lib/Model/PaymentMethodBankTransferInPaymentsEntityBanktransfer.php
lib/Model/PaymentMethodCardEMIInPaymentsEntity.php
lib/Model/PaymentMethodCardEMIInPaymentsEntityEmi.php
lib/Model/PaymentMethodCardEMIInPaymentsEntityEmiEmiDetails.php
lib/Model/PaymentMethodCardInPaymentsEntity.php
lib/Model/PaymentMethodCardInPaymentsEntityCard.php
lib/Model/PaymentMethodCardlessEMIInPaymentsEntity.php
lib/Model/PaymentMethodNetBankingInPaymentsEntity.php
lib/Model/PaymentMethodNetBankingInPaymentsEntityNetbanking.php
lib/Model/PaymentMethodPaylaterInPaymentsEntity.php
lib/Model/PaymentMethodUPIInPaymentsEntity.php
lib/Model/PaymentMethodUPIInPaymentsEntityUpi.php
lib/Model/PaymentMethodsFilters.php
lib/Model/PaymentMethodsQueries.php
lib/Model/PaymentModeDetails.php
lib/Model/PaymentWebhook.php
lib/Model/PaymentWebhookCustomerEntity.php
lib/Model/PaymentWebhookDataEntity.php
lib/Model/PaymentWebhookErrorEntity.php
lib/Model/PaymentWebhookGatewayDetailsEntity.php
lib/Model/PaymentWebhookOrderEntity.php
lib/Model/PlanEntity.php
lib/Model/RateLimitError.php
lib/Model/ReconEntity.php
lib/Model/ReconEntityDataInner.php
lib/Model/RefundEntity.php
lib/Model/RefundSpeed.php
lib/Model/RefundWebhook.php
lib/Model/RefundWebhookDataEntity.php
lib/Model/SavedInstrumentMeta.php
lib/Model/ScheduleOption.php
lib/Model/SettlementEntity.php
lib/Model/SettlementFetchReconRequest.php
lib/Model/SettlementReconEntity.php
lib/Model/SettlementReconEntityDataInner.php
lib/Model/SettlementWebhook.php
lib/Model/SettlementWebhookDataEntity.php
lib/Model/SimulateRequest.php
lib/Model/SimulationResponse.php
lib/Model/SplitAfterPaymentRequest.php
lib/Model/SplitAfterPaymentRequestSplitInner.php
lib/Model/SplitAfterPaymentResponse.php
lib/Model/StaticSplitRequest.php
lib/Model/StaticSplitRequestSchemeInner.php
lib/Model/StaticSplitResponse.php
lib/Model/StaticSplitResponseSchemeInner.php
lib/Model/SubsCreatePayment200Response.php
lib/Model/SubscriptionBankDetails.php
lib/Model/SubscriptionCustomerDetails.php
lib/Model/SubscriptionEligibilityRequest.php
lib/Model/SubscriptionEligibilityRequestFilters.php
lib/Model/SubscriptionEligibilityRequestQueries.php
lib/Model/SubscriptionEligibilityResponse.php
lib/Model/SubscriptionEntity.php
lib/Model/SubscriptionEntitySubscriptionMeta.php
lib/Model/SubscriptionPaymentEntity.php
lib/Model/SubscriptionPaymentRefundEntity.php
lib/Model/SubscriptionPaymentSplitItem.php
lib/Model/TerminalDetails.php
lib/Model/TerminalEntity.php
lib/Model/TerminalPaymentEntity.php
lib/Model/TerminalTransactionEntity.php
lib/Model/TerminateOrderRequest.php
lib/Model/TransferDetails.php
lib/Model/TransferDetailsTagsInner.php
lib/Model/UPIAuthorizeDetails.php
lib/Model/UPIPaymentMethod.php
lib/Model/UpdateTerminalEntity.php
lib/Model/UpdateTerminalRequest.php
lib/Model/UpdateTerminalRequestTerminalMeta.php
lib/Model/UpdateTerminalStatusRequest.php
lib/Model/UpdateVendorRequest.php
lib/Model/UpdateVendorResponse.php
lib/Model/UpdateVendorResponseRelatedDocsInner.php
lib/Model/Upi.php
lib/Model/UpiDetails.php
lib/Model/UploadPnachImageResponse.php
lib/Model/UploadTerminalDocs.php
lib/Model/UploadTerminalDocsEntity.php
lib/Model/UploadVendorDocumentsResponse.php
lib/Model/VendorBalance.php
lib/Model/VendorBalanceTransferCharges.php
lib/Model/VendorDocumentDownloadResponse.php
lib/Model/VendorDocumentsResponse.php
lib/Model/VendorEntity.php
lib/Model/VendorSplit.php
lib/Model/WalletOffer.php
lib/ObjectSerializer.php
phpunit.xml.dist
test/Api/CustomersApiTest.php
test/Api/EasySplitApiTest.php
test/Api/EligibilityApiTest.php
test/Api/OffersApiTest.php
test/Api/OrdersApiTest.php
test/Api/PGReconciliationApiTest.php
test/Api/PaymentLinksApiTest.php
test/Api/PaymentsApiTest.php
test/Api/RefundsApiTest.php
test/Api/SettlementReconciliationApiTest.php
test/Api/SettlementsApiTest.php
test/Api/SimulationApiTest.php
test/Api/SoftPOSApiTest.php
test/Api/SubscriptionApiTest.php
test/Api/TokenVaultApiTest.php
test/Model/AdjustVendorBalanceRequestTest.php
test/Model/AdjustVendorBalanceResponseTest.php
test/Model/ApiError404Test.php
test/Model/ApiError409Test.php
test/Model/ApiError502Test.php
test/Model/ApiErrorTest.php
test/Model/AppPaymentMethodTest.php
test/Model/AppTest.php
test/Model/AuthenticationErrorTest.php
test/Model/AuthorizationDetailsTest.php
test/Model/AuthorizationInPaymentsEntityTest.php
test/Model/AuthorizeOrderRequestTest.php
test/Model/BadRequestErrorTest.php
test/Model/BalanceDetailsTest.php
test/Model/BankDetailsTest.php
test/Model/BanktransferPaymentMethodTest.php
test/Model/BanktransferTest.php
test/Model/CardEMIPaymentMethodTest.php
test/Model/CardEMITest.php
test/Model/CardOfferTest.php
test/Model/CardPaymentMethodTest.php
test/Model/CardTest.php
test/Model/CardlessEMIEntityTest.php
test/Model/CardlessEMIPaymentMethodTest.php
test/Model/CardlessEMIQueriesTest.php
test/Model/CardlessEMITest.php
test/Model/CashbackDetailsTest.php
test/Model/ChargesDetailsTest.php
test/Model/CreateCustomerRequestTest.php
test/Model/CreateLinkRequestTest.php
test/Model/CreateOfferRequestTest.php
test/Model/CreateOrderRequestTest.php
test/Model/CreateOrderSettlementRequestBodyMetaDataTest.php
test/Model/CreateOrderSettlementRequestBodyTest.php
test/Model/CreatePlanRequestTest.php
test/Model/CreateSubscriptionPaymentAuthResponseFailureDetailsTest.php
test/Model/CreateSubscriptionPaymentAuthResponseTest.php
test/Model/CreateSubscriptionPaymentChargeResponseTest.php
test/Model/CreateSubscriptionPaymentRequestTest.php
test/Model/CreateSubscriptionRefundRequestTest.php
test/Model/CreateSubscriptionRequestAuthorizationDetailsTest.php
test/Model/CreateSubscriptionRequestPlanDetailsTest.php
test/Model/CreateSubscriptionRequestSubscriptionMetaTest.php
test/Model/CreateSubscriptionRequestTest.php
test/Model/CreateTerminalRequestTerminalMetaTest.php
test/Model/CreateTerminalRequestTest.php
test/Model/CreateTerminalTransactionRequestTest.php
test/Model/CreateVendorRequestTest.php
test/Model/CreateVendorResponseTest.php
test/Model/CryptogramEntityTest.php
test/Model/CustomerDetailsCardlessEMITest.php
test/Model/CustomerDetailsResponseTest.php
test/Model/CustomerDetailsTest.php
test/Model/CustomerEntityTest.php
test/Model/DiscountDetailsTest.php
test/Model/EMIOfferTest.php
test/Model/EMIPlansArrayTest.php
test/Model/ESOrderReconRequestFiltersTest.php
test/Model/ESOrderReconRequestPaginationTest.php
test/Model/ESOrderReconRequestTest.php
test/Model/ESOrderReconResponseDataInnerOrderSplitsInnerSplitInnerTest.php
test/Model/ESOrderReconResponseDataInnerOrderSplitsInnerTest.php
test/Model/ESOrderReconResponseDataInnerTest.php
test/Model/ESOrderReconResponseTest.php
test/Model/EligibilityCardlessEMIEntityTest.php
test/Model/EligibilityFetchCardlessEMIRequestTest.php
test/Model/EligibilityFetchOffersRequestTest.php
test/Model/EligibilityFetchPaylaterRequestTest.php
test/Model/EligibilityFetchPaymentMethodsRequestTest.php
test/Model/EligibilityMethodItemEntityDetailsAvailableHandlesInnerTest.php
test/Model/EligibilityMethodItemEntityDetailsTest.php
test/Model/EligibilityMethodItemTest.php
test/Model/EligibilityOfferEntityTest.php
test/Model/EligibilityPaylaterEntityTest.php
test/Model/EligibilityPaymentMethodsEntityEntityDetailsTest.php
test/Model/EligibilityPaymentMethodsEntityTest.php
test/Model/EntitySimulationRequestTest.php
test/Model/EntitySimulationResponseTest.php
test/Model/ErrorDetailsInPaymentsEntityTest.php
test/Model/FetchReconRequestFiltersTest.php
test/Model/FetchReconRequestPaginationTest.php
test/Model/FetchReconRequestTest.php
test/Model/FetchSettlementsRequestFiltersTest.php
test/Model/FetchSettlementsRequestPaginationTest.php
test/Model/FetchSettlementsRequestTest.php
test/Model/FetchTerminalQRCodesEntityTest.php
test/Model/IdempotencyErrorTest.php
test/Model/InstrumentEntityTest.php
test/Model/InstrumentWebhookDataEntityTest.php
test/Model/InstrumentWebhookDataTest.php
test/Model/InstrumentWebhookTest.php
test/Model/KycDetailsTest.php
test/Model/LinkCustomerDetailsEntityTest.php
test/Model/LinkEntityTest.php
test/Model/LinkMetaResponseEntityTest.php
test/Model/LinkNotifyEntityTest.php
test/Model/ManageSubscriptionPaymentRequestActionDetailsTest.php
test/Model/ManageSubscriptionPaymentRequestTest.php
test/Model/ManageSubscriptionRequestActionDetailsTest.php
test/Model/ManageSubscriptionRequestTest.php
test/Model/NetBankingPaymentMethodTest.php
test/Model/NetbankingTest.php
test/Model/OfferAllTest.php
test/Model/OfferCardTest.php
test/Model/OfferDetailsTest.php
test/Model/OfferEMITest.php
test/Model/OfferEntityTest.php
test/Model/OfferFiltersTest.php
test/Model/OfferMetaTest.php
test/Model/OfferNBNetbankingTest.php
test/Model/OfferNBTest.php
test/Model/OfferPaylaterTest.php
test/Model/OfferQueriesTest.php
test/Model/OfferTncTest.php
test/Model/OfferTypeTest.php
test/Model/OfferUPITest.php
test/Model/OfferValidationsPaymentMethodTest.php
test/Model/OfferValidationsTest.php
test/Model/OfferWalletTest.php
test/Model/OrderAuthenticateEntityTest.php
test/Model/OrderAuthenticatePaymentRequestTest.php
test/Model/OrderCreateRefundRequestTest.php
test/Model/OrderEntityTest.php
test/Model/OrderMetaTest.php
test/Model/OrderPayDataTest.php
test/Model/PayOrderEntityTest.php
test/Model/PayOrderRequestPaymentMethodTest.php
test/Model/PayOrderRequestTest.php
test/Model/PaylaterEntityTest.php
test/Model/PaylaterOfferTest.php
test/Model/PaylaterPaymentMethodTest.php
test/Model/PaylaterTest.php
test/Model/PaymentEntityPaymentMethodTest.php
test/Model/PaymentEntityTest.php
test/Model/PaymentLinkCustomerDetailsTest.php
test/Model/PaymentLinkOrderEntityTest.php
test/Model/PaymentMethodAppInPaymentsEntityAppTest.php
test/Model/PaymentMethodAppInPaymentsEntityTest.php
test/Model/PaymentMethodBankTransferInPaymentsEntityBanktransferTest.php
test/Model/PaymentMethodBankTransferInPaymentsEntityTest.php
test/Model/PaymentMethodCardEMIInPaymentsEntityEmiEmiDetailsTest.php
test/Model/PaymentMethodCardEMIInPaymentsEntityEmiTest.php
test/Model/PaymentMethodCardEMIInPaymentsEntityTest.php
test/Model/PaymentMethodCardInPaymentsEntityCardTest.php
test/Model/PaymentMethodCardInPaymentsEntityTest.php
test/Model/PaymentMethodCardlessEMIInPaymentsEntityTest.php
test/Model/PaymentMethodNetBankingInPaymentsEntityNetbankingTest.php
test/Model/PaymentMethodNetBankingInPaymentsEntityTest.php
test/Model/PaymentMethodPaylaterInPaymentsEntityTest.php
test/Model/PaymentMethodUPIInPaymentsEntityTest.php
test/Model/PaymentMethodUPIInPaymentsEntityUpiTest.php
test/Model/PaymentMethodsFiltersTest.php
test/Model/PaymentMethodsQueriesTest.php
test/Model/PaymentModeDetailsTest.php
test/Model/PaymentWebhookCustomerEntityTest.php
test/Model/PaymentWebhookDataEntityTest.php
test/Model/PaymentWebhookErrorEntityTest.php
test/Model/PaymentWebhookGatewayDetailsEntityTest.php
test/Model/PaymentWebhookOrderEntityTest.php
test/Model/PaymentWebhookTest.php
test/Model/PlanEntityTest.php
test/Model/RateLimitErrorTest.php
test/Model/ReconEntityDataInnerTest.php
test/Model/ReconEntityTest.php
test/Model/RefundEntityTest.php
test/Model/RefundSpeedTest.php
test/Model/RefundWebhookDataEntityTest.php
test/Model/RefundWebhookTest.php
test/Model/SavedInstrumentMetaTest.php
test/Model/ScheduleOptionTest.php
test/Model/SettlementEntityTest.php
test/Model/SettlementFetchReconRequestTest.php
test/Model/SettlementReconEntityDataInnerTest.php
test/Model/SettlementReconEntityTest.php
test/Model/SettlementWebhookDataEntityTest.php
test/Model/SettlementWebhookTest.php
test/Model/SimulateRequestTest.php
test/Model/SimulationResponseTest.php
test/Model/SplitAfterPaymentRequestSplitInnerTest.php
test/Model/SplitAfterPaymentRequestTest.php
test/Model/SplitAfterPaymentResponseTest.php
test/Model/StaticSplitRequestSchemeInnerTest.php
test/Model/StaticSplitRequestTest.php
test/Model/StaticSplitResponseSchemeInnerTest.php
test/Model/StaticSplitResponseTest.php
test/Model/SubsCreatePayment200ResponseTest.php
test/Model/SubscriptionBankDetailsTest.php
test/Model/SubscriptionCustomerDetailsTest.php
test/Model/SubscriptionEligibilityRequestFiltersTest.php
test/Model/SubscriptionEligibilityRequestQueriesTest.php
test/Model/SubscriptionEligibilityRequestTest.php
test/Model/SubscriptionEligibilityResponseTest.php
test/Model/SubscriptionEntitySubscriptionMetaTest.php
test/Model/SubscriptionEntityTest.php
test/Model/SubscriptionPaymentEntityTest.php
test/Model/SubscriptionPaymentRefundEntityTest.php
test/Model/SubscriptionPaymentSplitItemTest.php
test/Model/TerminalDetailsTest.php
test/Model/TerminalEntityTest.php
test/Model/TerminalPaymentEntityTest.php
test/Model/TerminalTransactionEntityTest.php
test/Model/TerminateOrderRequestTest.php
test/Model/TransferDetailsTagsInnerTest.php
test/Model/TransferDetailsTest.php
test/Model/UPIAuthorizeDetailsTest.php
test/Model/UPIPaymentMethodTest.php
test/Model/UpdateTerminalEntityTest.php
test/Model/UpdateTerminalRequestTerminalMetaTest.php
test/Model/UpdateTerminalRequestTest.php
test/Model/UpdateTerminalStatusRequestTest.php
test/Model/UpdateVendorRequestTest.php
test/Model/UpdateVendorResponseRelatedDocsInnerTest.php
test/Model/UpdateVendorResponseTest.php
test/Model/UpiDetailsTest.php
test/Model/UpiTest.php
test/Model/UploadPnachImageResponseTest.php
test/Model/UploadTerminalDocsEntityTest.php
test/Model/UploadTerminalDocsTest.php
test/Model/UploadVendorDocumentsResponseTest.php
test/Model/VendorBalanceTest.php
test/Model/VendorBalanceTransferChargesTest.php
test/Model/VendorDocumentDownloadResponseTest.php
test/Model/VendorDocumentsResponseTest.php
test/Model/VendorEntityTest.php
test/Model/VendorSplitTest.php
test/Model/WalletOfferTest.php
