<?php
/**
 * AuthorizationDetails
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * Cashfree Payment Gateway APIs
 *
 * Cashfree's Payment Gateway APIs provide developers with a streamlined pathway to integrate advanced payment processing capabilities into their applications, platforms and websites.
 *
 * The version of the OpenAPI document: 2023-08-01
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 7.0.0
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Cashfree\Model;

use \ArrayAccess;
use \Cashfree\ObjectSerializer;

/**
 * AuthorizationDetails Class Doc Comment
 *
 * @category Class
 * @description Details of the authorization done for the subscription. Returned in Get subscription and auth payments.
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<string, mixed>
 */
class AuthorizationDetails implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'AuthorizationDetails';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'authorization_amount' => 'float',
        'authorization_amount_refund' => 'bool',
        'authorization_reference' => 'string',
        'authorization_time' => 'string',
        'authorization_status' => 'string',
        'payment_id' => 'string',
        'payment_method' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'authorization_amount' => 'float64',
        'authorization_amount_refund' => null,
        'authorization_reference' => null,
        'authorization_time' => null,
        'authorization_status' => null,
        'payment_id' => null,
        'payment_method' => null
    ];

    /**
      * Array of nullable properties. Used for (de)serialization
      *
      * @var boolean[]
      */
    protected static $openAPINullables = [
        'authorization_amount' => false,
		'authorization_amount_refund' => false,
		'authorization_reference' => false,
		'authorization_time' => false,
		'authorization_status' => false,
		'payment_id' => false,
		'payment_method' => false
    ];

    /**
      * If a nullable field gets set to null, insert it here
      *
      * @var boolean[]
      */
    protected $openAPINullablesSetToNull = [];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of nullable properties
     *
     * @return array
     */
    protected static function openAPINullables(): array
    {
        return self::$openAPINullables;
    }

    /**
     * Array of nullable field names deliberately set to null
     *
     * @return boolean[]
     */
    private function getOpenAPINullablesSetToNull(): array
    {
        return $this->openAPINullablesSetToNull;
    }

    /**
     * Setter - Array of nullable field names deliberately set to null
     *
     * @param boolean[] $openAPINullablesSetToNull
     */
    private function setOpenAPINullablesSetToNull(array $openAPINullablesSetToNull): void
    {
        $this->openAPINullablesSetToNull = $openAPINullablesSetToNull;
    }

    /**
     * Checks if a property is nullable
     *
     * @param string $property
     * @return bool
     */
    public static function isNullable(string $property): bool
    {
        return self::openAPINullables()[$property] ?? false;
    }

    /**
     * Checks if a nullable property is set to null.
     *
     * @param string $property
     * @return bool
     */
    public function isNullableSetToNull(string $property): bool
    {
        return in_array($property, $this->getOpenAPINullablesSetToNull(), true);
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'authorization_amount' => 'authorization_amount',
        'authorization_amount_refund' => 'authorization_amount_refund',
        'authorization_reference' => 'authorization_reference',
        'authorization_time' => 'authorization_time',
        'authorization_status' => 'authorization_status',
        'payment_id' => 'payment_id',
        'payment_method' => 'payment_method'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'authorization_amount' => 'setAuthorizationAmount',
        'authorization_amount_refund' => 'setAuthorizationAmountRefund',
        'authorization_reference' => 'setAuthorizationReference',
        'authorization_time' => 'setAuthorizationTime',
        'authorization_status' => 'setAuthorizationStatus',
        'payment_id' => 'setPaymentId',
        'payment_method' => 'setPaymentMethod'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'authorization_amount' => 'getAuthorizationAmount',
        'authorization_amount_refund' => 'getAuthorizationAmountRefund',
        'authorization_reference' => 'getAuthorizationReference',
        'authorization_time' => 'getAuthorizationTime',
        'authorization_status' => 'getAuthorizationStatus',
        'payment_id' => 'getPaymentId',
        'payment_method' => 'getPaymentMethod'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->setIfExists('authorization_amount', $data ?? [], null);
        $this->setIfExists('authorization_amount_refund', $data ?? [], null);
        $this->setIfExists('authorization_reference', $data ?? [], null);
        $this->setIfExists('authorization_time', $data ?? [], null);
        $this->setIfExists('authorization_status', $data ?? [], null);
        $this->setIfExists('payment_id', $data ?? [], null);
        $this->setIfExists('payment_method', $data ?? [], null);
    }

    /**
    * Sets $this->container[$variableName] to the given data or to the given default Value; if $variableName
    * is nullable and its value is set to null in the $fields array, then mark it as "set to null" in the
    * $this->openAPINullablesSetToNull array
    *
    * @param string $variableName
    * @param array  $fields
    * @param mixed  $defaultValue
    */
    private function setIfExists(string $variableName, array $fields, $defaultValue): void
    {
        if (self::isNullable($variableName) && array_key_exists($variableName, $fields) && is_null($fields[$variableName])) {
            $this->openAPINullablesSetToNull[] = $variableName;
        }

        $this->container[$variableName] = $fields[$variableName] ?? $defaultValue;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets authorization_amount
     *
     * @return float|null
     */
    public function getAuthorizationAmount()
    {
        return $this->container['authorization_amount'];
    }

    /**
     * Sets authorization_amount
     *
     * @param float|null $authorization_amount Authorization amount for the auth payment.
     *
     * @return self
     */
    public function setAuthorizationAmount($authorization_amount)
    {
        if (is_null($authorization_amount)) {
            throw new \InvalidArgumentException('non-nullable authorization_amount cannot be null');
        }
        $this->container['authorization_amount'] = $authorization_amount;

        return $this;
    }

    /**
     * Gets authorization_amount_refund
     *
     * @return bool|null
     */
    public function getAuthorizationAmountRefund()
    {
        return $this->container['authorization_amount_refund'];
    }

    /**
     * Sets authorization_amount_refund
     *
     * @param bool|null $authorization_amount_refund Indicates whether the authorization amount should be refunded to the customer automatically. Merchants can use this field to specify if the authorized funds should be returned to the customer after authorization of the subscription.
     *
     * @return self
     */
    public function setAuthorizationAmountRefund($authorization_amount_refund)
    {
        if (is_null($authorization_amount_refund)) {
            throw new \InvalidArgumentException('non-nullable authorization_amount_refund cannot be null');
        }
        $this->container['authorization_amount_refund'] = $authorization_amount_refund;

        return $this;
    }

    /**
     * Gets authorization_reference
     *
     * @return string|null
     */
    public function getAuthorizationReference()
    {
        return $this->container['authorization_reference'];
    }

    /**
     * Sets authorization_reference
     *
     * @param string|null $authorization_reference Authorization reference. UMN for UPI, UMRN for EMandate/Physical Mandate and Enrollment ID for cards.
     *
     * @return self
     */
    public function setAuthorizationReference($authorization_reference)
    {
        if (is_null($authorization_reference)) {
            throw new \InvalidArgumentException('non-nullable authorization_reference cannot be null');
        }
        $this->container['authorization_reference'] = $authorization_reference;

        return $this;
    }

    /**
     * Gets authorization_time
     *
     * @return string|null
     */
    public function getAuthorizationTime()
    {
        return $this->container['authorization_time'];
    }

    /**
     * Sets authorization_time
     *
     * @param string|null $authorization_time Authorization time.
     *
     * @return self
     */
    public function setAuthorizationTime($authorization_time)
    {
        if (is_null($authorization_time)) {
            throw new \InvalidArgumentException('non-nullable authorization_time cannot be null');
        }
        $this->container['authorization_time'] = $authorization_time;

        return $this;
    }

    /**
     * Gets authorization_status
     *
     * @return string|null
     */
    public function getAuthorizationStatus()
    {
        return $this->container['authorization_status'];
    }

    /**
     * Sets authorization_status
     *
     * @param string|null $authorization_status Status of the authorization.
     *
     * @return self
     */
    public function setAuthorizationStatus($authorization_status)
    {
        if (is_null($authorization_status)) {
            throw new \InvalidArgumentException('non-nullable authorization_status cannot be null');
        }
        $this->container['authorization_status'] = $authorization_status;

        return $this;
    }

    /**
     * Gets payment_id
     *
     * @return string|null
     */
    public function getPaymentId()
    {
        return $this->container['payment_id'];
    }

    /**
     * Sets payment_id
     *
     * @param string|null $payment_id A unique ID passed by merchant for identifying the transaction.
     *
     * @return self
     */
    public function setPaymentId($payment_id)
    {
        if (is_null($payment_id)) {
            throw new \InvalidArgumentException('non-nullable payment_id cannot be null');
        }
        $this->container['payment_id'] = $payment_id;

        return $this;
    }

    /**
     * Gets payment_method
     *
     * @return string|null
     */
    public function getPaymentMethod()
    {
        return $this->container['payment_method'];
    }

    /**
     * Sets payment_method
     *
     * @param string|null $payment_method Payment method used for the authorization.
     *
     * @return self
     */
    public function setPaymentMethod($payment_method)
    {
        if (is_null($payment_method)) {
            throw new \InvalidArgumentException('non-nullable payment_method cannot be null');
        }
        $this->container['payment_method'] = $payment_method;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


