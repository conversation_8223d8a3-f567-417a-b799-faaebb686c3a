<?php
/**
 * CreateLinkRequest
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * Cashfree Payment Gateway APIs
 *
 * Cashfree's Payment Gateway APIs provide developers with a streamlined pathway to integrate advanced payment processing capabilities into their applications, platforms and websites.
 *
 * The version of the OpenAPI document: 2023-08-01
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 7.0.0
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Cashfree\Model;

use \ArrayAccess;
use \Cashfree\ObjectSerializer;

/**
 * CreateLinkRequest Class Doc Comment
 *
 * @category Class
 * @description Request paramenters for link creation
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<string, mixed>
 */
class CreateLinkRequest implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'CreateLinkRequest';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'link_id' => 'string',
        'link_amount' => 'float',
        'link_currency' => 'string',
        'link_purpose' => 'string',
        'customer_details' => '\Cashfree\Model\LinkCustomerDetailsEntity',
        'link_partial_payments' => 'bool',
        'link_minimum_partial_amount' => 'float',
        'link_expiry_time' => 'string',
        'link_notify' => '\Cashfree\Model\LinkNotifyEntity',
        'link_auto_reminders' => 'bool',
        'link_notes' => 'array<string,string>',
        'link_meta' => '\Cashfree\Model\LinkMetaResponseEntity',
        'order_splits' => '\Cashfree\Model\VendorSplit[]'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'link_id' => null,
        'link_amount' => 'double',
        'link_currency' => null,
        'link_purpose' => null,
        'customer_details' => null,
        'link_partial_payments' => null,
        'link_minimum_partial_amount' => 'double',
        'link_expiry_time' => null,
        'link_notify' => null,
        'link_auto_reminders' => null,
        'link_notes' => null,
        'link_meta' => null,
        'order_splits' => null
    ];

    /**
      * Array of nullable properties. Used for (de)serialization
      *
      * @var boolean[]
      */
    protected static $openAPINullables = [
        'link_id' => false,
		'link_amount' => false,
		'link_currency' => false,
		'link_purpose' => false,
		'customer_details' => false,
		'link_partial_payments' => false,
		'link_minimum_partial_amount' => false,
		'link_expiry_time' => false,
		'link_notify' => false,
		'link_auto_reminders' => false,
		'link_notes' => false,
		'link_meta' => false,
		'order_splits' => false
    ];

    /**
      * If a nullable field gets set to null, insert it here
      *
      * @var boolean[]
      */
    protected $openAPINullablesSetToNull = [];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of nullable properties
     *
     * @return array
     */
    protected static function openAPINullables(): array
    {
        return self::$openAPINullables;
    }

    /**
     * Array of nullable field names deliberately set to null
     *
     * @return boolean[]
     */
    private function getOpenAPINullablesSetToNull(): array
    {
        return $this->openAPINullablesSetToNull;
    }

    /**
     * Setter - Array of nullable field names deliberately set to null
     *
     * @param boolean[] $openAPINullablesSetToNull
     */
    private function setOpenAPINullablesSetToNull(array $openAPINullablesSetToNull): void
    {
        $this->openAPINullablesSetToNull = $openAPINullablesSetToNull;
    }

    /**
     * Checks if a property is nullable
     *
     * @param string $property
     * @return bool
     */
    public static function isNullable(string $property): bool
    {
        return self::openAPINullables()[$property] ?? false;
    }

    /**
     * Checks if a nullable property is set to null.
     *
     * @param string $property
     * @return bool
     */
    public function isNullableSetToNull(string $property): bool
    {
        return in_array($property, $this->getOpenAPINullablesSetToNull(), true);
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'link_id' => 'link_id',
        'link_amount' => 'link_amount',
        'link_currency' => 'link_currency',
        'link_purpose' => 'link_purpose',
        'customer_details' => 'customer_details',
        'link_partial_payments' => 'link_partial_payments',
        'link_minimum_partial_amount' => 'link_minimum_partial_amount',
        'link_expiry_time' => 'link_expiry_time',
        'link_notify' => 'link_notify',
        'link_auto_reminders' => 'link_auto_reminders',
        'link_notes' => 'link_notes',
        'link_meta' => 'link_meta',
        'order_splits' => 'order_splits'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'link_id' => 'setLinkId',
        'link_amount' => 'setLinkAmount',
        'link_currency' => 'setLinkCurrency',
        'link_purpose' => 'setLinkPurpose',
        'customer_details' => 'setCustomerDetails',
        'link_partial_payments' => 'setLinkPartialPayments',
        'link_minimum_partial_amount' => 'setLinkMinimumPartialAmount',
        'link_expiry_time' => 'setLinkExpiryTime',
        'link_notify' => 'setLinkNotify',
        'link_auto_reminders' => 'setLinkAutoReminders',
        'link_notes' => 'setLinkNotes',
        'link_meta' => 'setLinkMeta',
        'order_splits' => 'setOrderSplits'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'link_id' => 'getLinkId',
        'link_amount' => 'getLinkAmount',
        'link_currency' => 'getLinkCurrency',
        'link_purpose' => 'getLinkPurpose',
        'customer_details' => 'getCustomerDetails',
        'link_partial_payments' => 'getLinkPartialPayments',
        'link_minimum_partial_amount' => 'getLinkMinimumPartialAmount',
        'link_expiry_time' => 'getLinkExpiryTime',
        'link_notify' => 'getLinkNotify',
        'link_auto_reminders' => 'getLinkAutoReminders',
        'link_notes' => 'getLinkNotes',
        'link_meta' => 'getLinkMeta',
        'order_splits' => 'getOrderSplits'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->setIfExists('link_id', $data ?? [], null);
        $this->setIfExists('link_amount', $data ?? [], null);
        $this->setIfExists('link_currency', $data ?? [], null);
        $this->setIfExists('link_purpose', $data ?? [], null);
        $this->setIfExists('customer_details', $data ?? [], null);
        $this->setIfExists('link_partial_payments', $data ?? [], null);
        $this->setIfExists('link_minimum_partial_amount', $data ?? [], null);
        $this->setIfExists('link_expiry_time', $data ?? [], null);
        $this->setIfExists('link_notify', $data ?? [], null);
        $this->setIfExists('link_auto_reminders', $data ?? [], null);
        $this->setIfExists('link_notes', $data ?? [], null);
        $this->setIfExists('link_meta', $data ?? [], null);
        $this->setIfExists('order_splits', $data ?? [], null);
    }

    /**
    * Sets $this->container[$variableName] to the given data or to the given default Value; if $variableName
    * is nullable and its value is set to null in the $fields array, then mark it as "set to null" in the
    * $this->openAPINullablesSetToNull array
    *
    * @param string $variableName
    * @param array  $fields
    * @param mixed  $defaultValue
    */
    private function setIfExists(string $variableName, array $fields, $defaultValue): void
    {
        if (self::isNullable($variableName) && array_key_exists($variableName, $fields) && is_null($fields[$variableName])) {
            $this->openAPINullablesSetToNull[] = $variableName;
        }

        $this->container[$variableName] = $fields[$variableName] ?? $defaultValue;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if ($this->container['link_id'] === null) {
            $invalidProperties[] = "'link_id' can't be null";
        }
        if ((mb_strlen($this->container['link_id']) > 50)) {
            $invalidProperties[] = "invalid value for 'link_id', the character length must be smaller than or equal to 50.";
        }

        if ($this->container['link_amount'] === null) {
            $invalidProperties[] = "'link_amount' can't be null";
        }
        if ($this->container['link_currency'] === null) {
            $invalidProperties[] = "'link_currency' can't be null";
        }
        if ($this->container['link_purpose'] === null) {
            $invalidProperties[] = "'link_purpose' can't be null";
        }
        if ((mb_strlen($this->container['link_purpose']) > 500)) {
            $invalidProperties[] = "invalid value for 'link_purpose', the character length must be smaller than or equal to 500.";
        }

        if ($this->container['customer_details'] === null) {
            $invalidProperties[] = "'customer_details' can't be null";
        }
        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets link_id
     *
     * @return string
     */
    public function getLinkId()
    {
        return $this->container['link_id'];
    }

    /**
     * Sets link_id
     *
     * @param string $link_id Unique Identifier (provided by merchant) for the Link. Alphanumeric and only - and _ allowed (50 character limit). Use this for other link-related APIs.
     *
     * @return self
     */
    public function setLinkId($link_id)
    {
        if (is_null($link_id)) {
            throw new \InvalidArgumentException('non-nullable link_id cannot be null');
        }
        if ((mb_strlen($link_id) > 50)) {
            throw new \InvalidArgumentException('invalid length for $link_id when calling CreateLinkRequest., must be smaller than or equal to 50.');
        }

        $this->container['link_id'] = $link_id;

        return $this;
    }

    /**
     * Gets link_amount
     *
     * @return float
     */
    public function getLinkAmount()
    {
        return $this->container['link_amount'];
    }

    /**
     * Sets link_amount
     *
     * @param float $link_amount Amount to be collected using this link. Provide upto two decimals for paise.
     *
     * @return self
     */
    public function setLinkAmount($link_amount)
    {
        if (is_null($link_amount)) {
            throw new \InvalidArgumentException('non-nullable link_amount cannot be null');
        }
        $this->container['link_amount'] = $link_amount;

        return $this;
    }

    /**
     * Gets link_currency
     *
     * @return string
     */
    public function getLinkCurrency()
    {
        return $this->container['link_currency'];
    }

    /**
     * Sets link_currency
     *
     * @param string $link_currency Currency for the payment link. Default is INR. Contact <EMAIL> to enable new currencies.
     *
     * @return self
     */
    public function setLinkCurrency($link_currency)
    {
        if (is_null($link_currency)) {
            throw new \InvalidArgumentException('non-nullable link_currency cannot be null');
        }


        $this->container['link_currency'] = $link_currency;

        return $this;
    }

    /**
     * Gets link_purpose
     *
     * @return string
     */
    public function getLinkPurpose()
    {
        return $this->container['link_purpose'];
    }

    /**
     * Sets link_purpose
     *
     * @param string $link_purpose A brief description for which payment must be collected. This is shown to the customer.
     *
     * @return self
     */
    public function setLinkPurpose($link_purpose)
    {
        if (is_null($link_purpose)) {
            throw new \InvalidArgumentException('non-nullable link_purpose cannot be null');
        }
        if ((mb_strlen($link_purpose) > 500)) {
            throw new \InvalidArgumentException('invalid length for $link_purpose when calling CreateLinkRequest., must be smaller than or equal to 500.');
        }

        $this->container['link_purpose'] = $link_purpose;

        return $this;
    }

    /**
     * Gets customer_details
     *
     * @return \Cashfree\Model\LinkCustomerDetailsEntity
     */
    public function getCustomerDetails()
    {
        return $this->container['customer_details'];
    }

    /**
     * Sets customer_details
     *
     * @param \Cashfree\Model\LinkCustomerDetailsEntity $customer_details customer_details
     *
     * @return self
     */
    public function setCustomerDetails($customer_details)
    {
        if (is_null($customer_details)) {
            throw new \InvalidArgumentException('non-nullable customer_details cannot be null');
        }
        $this->container['customer_details'] = $customer_details;

        return $this;
    }

    /**
     * Gets link_partial_payments
     *
     * @return bool|null
     */
    public function getLinkPartialPayments()
    {
        return $this->container['link_partial_payments'];
    }

    /**
     * Sets link_partial_payments
     *
     * @param bool|null $link_partial_payments If \"true\", customer can make partial payments for the link.
     *
     * @return self
     */
    public function setLinkPartialPayments($link_partial_payments)
    {
        if (is_null($link_partial_payments)) {
            throw new \InvalidArgumentException('non-nullable link_partial_payments cannot be null');
        }
        $this->container['link_partial_payments'] = $link_partial_payments;

        return $this;
    }

    /**
     * Gets link_minimum_partial_amount
     *
     * @return float|null
     */
    public function getLinkMinimumPartialAmount()
    {
        return $this->container['link_minimum_partial_amount'];
    }

    /**
     * Sets link_minimum_partial_amount
     *
     * @param float|null $link_minimum_partial_amount Minimum amount in first installment that needs to be paid by the customer if partial payments are enabled. This should be less than the link_amount.
     *
     * @return self
     */
    public function setLinkMinimumPartialAmount($link_minimum_partial_amount)
    {
        if (is_null($link_minimum_partial_amount)) {
            throw new \InvalidArgumentException('non-nullable link_minimum_partial_amount cannot be null');
        }
        $this->container['link_minimum_partial_amount'] = $link_minimum_partial_amount;

        return $this;
    }

    /**
     * Gets link_expiry_time
     *
     * @return string|null
     */
    public function getLinkExpiryTime()
    {
        return $this->container['link_expiry_time'];
    }

    /**
     * Sets link_expiry_time
     *
     * @param string|null $link_expiry_time Time after which the link expires. Customers will not be able to make the payment beyond the time specified here. You can provide them in a valid ISO 8601 time format. Default is 30 days.
     *
     * @return self
     */
    public function setLinkExpiryTime($link_expiry_time)
    {
        if (is_null($link_expiry_time)) {
            throw new \InvalidArgumentException('non-nullable link_expiry_time cannot be null');
        }
        $this->container['link_expiry_time'] = $link_expiry_time;

        return $this;
    }

    /**
     * Gets link_notify
     *
     * @return \Cashfree\Model\LinkNotifyEntity|null
     */
    public function getLinkNotify()
    {
        return $this->container['link_notify'];
    }

    /**
     * Sets link_notify
     *
     * @param \Cashfree\Model\LinkNotifyEntity|null $link_notify link_notify
     *
     * @return self
     */
    public function setLinkNotify($link_notify)
    {
        if (is_null($link_notify)) {
            throw new \InvalidArgumentException('non-nullable link_notify cannot be null');
        }
        $this->container['link_notify'] = $link_notify;

        return $this;
    }

    /**
     * Gets link_auto_reminders
     *
     * @return bool|null
     */
    public function getLinkAutoReminders()
    {
        return $this->container['link_auto_reminders'];
    }

    /**
     * Sets link_auto_reminders
     *
     * @param bool|null $link_auto_reminders If \"true\", reminders will be sent to customers for collecting payments.
     *
     * @return self
     */
    public function setLinkAutoReminders($link_auto_reminders)
    {
        if (is_null($link_auto_reminders)) {
            throw new \InvalidArgumentException('non-nullable link_auto_reminders cannot be null');
        }
        $this->container['link_auto_reminders'] = $link_auto_reminders;

        return $this;
    }

    /**
     * Gets link_notes
     *
     * @return array<string,string>|null
     */
    public function getLinkNotes()
    {
        return $this->container['link_notes'];
    }

    /**
     * Sets link_notes
     *
     * @param array<string,string>|null $link_notes Key-value pair that can be used to store additional information about the entity. Maximum 5 key-value pairs
     *
     * @return self
     */
    public function setLinkNotes($link_notes)
    {
        if (is_null($link_notes)) {
            throw new \InvalidArgumentException('non-nullable link_notes cannot be null');
        }
        $this->container['link_notes'] = $link_notes;

        return $this;
    }

    /**
     * Gets link_meta
     *
     * @return \Cashfree\Model\LinkMetaResponseEntity|null
     */
    public function getLinkMeta()
    {
        return $this->container['link_meta'];
    }

    /**
     * Sets link_meta
     *
     * @param \Cashfree\Model\LinkMetaResponseEntity|null $link_meta link_meta
     *
     * @return self
     */
    public function setLinkMeta($link_meta)
    {
        if (is_null($link_meta)) {
            throw new \InvalidArgumentException('non-nullable link_meta cannot be null');
        }
        $this->container['link_meta'] = $link_meta;

        return $this;
    }

    /**
     * Gets order_splits
     *
     * @return \Cashfree\Model\VendorSplit[]|null
     */
    public function getOrderSplits()
    {
        return $this->container['order_splits'];
    }

    /**
     * Sets order_splits
     *
     * @param \Cashfree\Model\VendorSplit[]|null $order_splits If you have Easy split enabled in your Cashfree account then you can use this option to split the order amount.
     *
     * @return self
     */
    public function setOrderSplits($order_splits)
    {
        if (is_null($order_splits)) {
            throw new \InvalidArgumentException('non-nullable order_splits cannot be null');
        }
        $this->container['order_splits'] = $order_splits;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


