<?php
/**
 * CreateOrderRequest
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * Cashfree Payment Gateway APIs
 *
 * Cashfree's Payment Gateway APIs provide developers with a streamlined pathway to integrate advanced payment processing capabilities into their applications, platforms and websites.
 *
 * The version of the OpenAPI document: 2023-08-01
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 7.0.0
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Cashfree\Model;

use \ArrayAccess;
use \Cashfree\ObjectSerializer;

/**
 * CreateOrderRequest Class Doc Comment
 *
 * @category Class
 * @description Request body to create an order at cashfree
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<string, mixed>
 */
class CreateOrderRequest implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'CreateOrderRequest';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'order_id' => 'string',
        'order_amount' => 'float',
        'order_currency' => 'string',
        'customer_details' => '\Cashfree\Model\CustomerDetails',
        'terminal' => '\Cashfree\Model\TerminalDetails',
        'order_meta' => '\Cashfree\Model\OrderMeta',
        'order_expiry_time' => 'string',
        'order_note' => 'string',
        'order_tags' => 'array<string,string>',
        'order_splits' => '\Cashfree\Model\VendorSplit[]'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'order_id' => null,
        'order_amount' => 'double',
        'order_currency' => null,
        'customer_details' => null,
        'terminal' => null,
        'order_meta' => null,
        'order_expiry_time' => 'ISO8601',
        'order_note' => null,
        'order_tags' => null,
        'order_splits' => null
    ];

    /**
      * Array of nullable properties. Used for (de)serialization
      *
      * @var boolean[]
      */
    protected static $openAPINullables = [
        'order_id' => false,
		'order_amount' => false,
		'order_currency' => false,
		'customer_details' => false,
		'terminal' => false,
		'order_meta' => false,
		'order_expiry_time' => false,
		'order_note' => false,
		'order_tags' => false,
		'order_splits' => false
    ];

    /**
      * If a nullable field gets set to null, insert it here
      *
      * @var boolean[]
      */
    protected $openAPINullablesSetToNull = [];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of nullable properties
     *
     * @return array
     */
    protected static function openAPINullables(): array
    {
        return self::$openAPINullables;
    }

    /**
     * Array of nullable field names deliberately set to null
     *
     * @return boolean[]
     */
    private function getOpenAPINullablesSetToNull(): array
    {
        return $this->openAPINullablesSetToNull;
    }

    /**
     * Setter - Array of nullable field names deliberately set to null
     *
     * @param boolean[] $openAPINullablesSetToNull
     */
    private function setOpenAPINullablesSetToNull(array $openAPINullablesSetToNull): void
    {
        $this->openAPINullablesSetToNull = $openAPINullablesSetToNull;
    }

    /**
     * Checks if a property is nullable
     *
     * @param string $property
     * @return bool
     */
    public static function isNullable(string $property): bool
    {
        return self::openAPINullables()[$property] ?? false;
    }

    /**
     * Checks if a nullable property is set to null.
     *
     * @param string $property
     * @return bool
     */
    public function isNullableSetToNull(string $property): bool
    {
        return in_array($property, $this->getOpenAPINullablesSetToNull(), true);
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'order_id' => 'order_id',
        'order_amount' => 'order_amount',
        'order_currency' => 'order_currency',
        'customer_details' => 'customer_details',
        'terminal' => 'terminal',
        'order_meta' => 'order_meta',
        'order_expiry_time' => 'order_expiry_time',
        'order_note' => 'order_note',
        'order_tags' => 'order_tags',
        'order_splits' => 'order_splits'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'order_id' => 'setOrderId',
        'order_amount' => 'setOrderAmount',
        'order_currency' => 'setOrderCurrency',
        'customer_details' => 'setCustomerDetails',
        'terminal' => 'setTerminal',
        'order_meta' => 'setOrderMeta',
        'order_expiry_time' => 'setOrderExpiryTime',
        'order_note' => 'setOrderNote',
        'order_tags' => 'setOrderTags',
        'order_splits' => 'setOrderSplits'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'order_id' => 'getOrderId',
        'order_amount' => 'getOrderAmount',
        'order_currency' => 'getOrderCurrency',
        'customer_details' => 'getCustomerDetails',
        'terminal' => 'getTerminal',
        'order_meta' => 'getOrderMeta',
        'order_expiry_time' => 'getOrderExpiryTime',
        'order_note' => 'getOrderNote',
        'order_tags' => 'getOrderTags',
        'order_splits' => 'getOrderSplits'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->setIfExists('order_id', $data ?? [], null);
        $this->setIfExists('order_amount', $data ?? [], null);
        $this->setIfExists('order_currency', $data ?? [], null);
        $this->setIfExists('customer_details', $data ?? [], null);
        $this->setIfExists('terminal', $data ?? [], null);
        $this->setIfExists('order_meta', $data ?? [], null);
        $this->setIfExists('order_expiry_time', $data ?? [], null);
        $this->setIfExists('order_note', $data ?? [], null);
        $this->setIfExists('order_tags', $data ?? [], null);
        $this->setIfExists('order_splits', $data ?? [], null);
    }

    /**
    * Sets $this->container[$variableName] to the given data or to the given default Value; if $variableName
    * is nullable and its value is set to null in the $fields array, then mark it as "set to null" in the
    * $this->openAPINullablesSetToNull array
    *
    * @param string $variableName
    * @param array  $fields
    * @param mixed  $defaultValue
    */
    private function setIfExists(string $variableName, array $fields, $defaultValue): void
    {
        if (self::isNullable($variableName) && array_key_exists($variableName, $fields) && is_null($fields[$variableName])) {
            $this->openAPINullablesSetToNull[] = $variableName;
        }

        $this->container[$variableName] = $fields[$variableName] ?? $defaultValue;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if (!is_null($this->container['order_id']) && (mb_strlen($this->container['order_id']) > 45)) {
            $invalidProperties[] = "invalid value for 'order_id', the character length must be smaller than or equal to 45.";
        }

        if (!is_null($this->container['order_id']) && (mb_strlen($this->container['order_id']) < 3)) {
            $invalidProperties[] = "invalid value for 'order_id', the character length must be bigger than or equal to 3.";
        }

        if ($this->container['order_amount'] === null) {
            $invalidProperties[] = "'order_amount' can't be null";
        }
        if (($this->container['order_amount'] < 1)) {
            $invalidProperties[] = "invalid value for 'order_amount', must be bigger than or equal to 1.";
        }

        if ($this->container['order_currency'] === null) {
            $invalidProperties[] = "'order_currency' can't be null";
        }
        if ($this->container['customer_details'] === null) {
            $invalidProperties[] = "'customer_details' can't be null";
        }
        if (!is_null($this->container['order_note']) && (mb_strlen($this->container['order_note']) > 200)) {
            $invalidProperties[] = "invalid value for 'order_note', the character length must be smaller than or equal to 200.";
        }

        if (!is_null($this->container['order_note']) && (mb_strlen($this->container['order_note']) < 3)) {
            $invalidProperties[] = "invalid value for 'order_note', the character length must be bigger than or equal to 3.";
        }

        if (!is_null($this->container['order_tags']) && (count($this->container['order_tags']) > 15)) {
            $invalidProperties[] = "invalid value for 'order_tags', number of items must be less than or equal to 15.";
        }

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets order_id
     *
     * @return string|null
     */
    public function getOrderId()
    {
        return $this->container['order_id'];
    }

    /**
     * Sets order_id
     *
     * @param string|null $order_id Order identifier present in your system. Alphanumeric, '_' and '-' only
     *
     * @return self
     */
    public function setOrderId($order_id)
    {
        if (is_null($order_id)) {
            throw new \InvalidArgumentException('non-nullable order_id cannot be null');
        }
        if ((mb_strlen($order_id) > 45)) {
            throw new \InvalidArgumentException('invalid length for $order_id when calling CreateOrderRequest., must be smaller than or equal to 45.');
        }
        if ((mb_strlen($order_id) < 3)) {
            throw new \InvalidArgumentException('invalid length for $order_id when calling CreateOrderRequest., must be bigger than or equal to 3.');
        }

        $this->container['order_id'] = $order_id;

        return $this;
    }

    /**
     * Gets order_amount
     *
     * @return float
     */
    public function getOrderAmount()
    {
        return $this->container['order_amount'];
    }

    /**
     * Sets order_amount
     *
     * @param float $order_amount Bill amount for the order. Provide upto two decimals. 10.15 means Rs 10 and 15 paisa
     *
     * @return self
     */
    public function setOrderAmount($order_amount)
    {
        if (is_null($order_amount)) {
            throw new \InvalidArgumentException('non-nullable order_amount cannot be null');
        }

        if (($order_amount < 1)) {
            throw new \InvalidArgumentException('invalid value for $order_amount when calling CreateOrderRequest., must be bigger than or equal to 1.');
        }

        $this->container['order_amount'] = $order_amount;

        return $this;
    }

    /**
     * Gets order_currency
     *
     * @return string
     */
    public function getOrderCurrency()
    {
        return $this->container['order_currency'];
    }

    /**
     * Sets order_currency
     *
     * @param string $order_currency Currency for the order. INR if left empty. Contact <EMAIL> to enable new currencies.
     *
     * @return self
     */
    public function setOrderCurrency($order_currency)
    {
        if (is_null($order_currency)) {
            throw new \InvalidArgumentException('non-nullable order_currency cannot be null');
        }
        $this->container['order_currency'] = $order_currency;

        return $this;
    }

    /**
     * Gets customer_details
     *
     * @return \Cashfree\Model\CustomerDetails
     */
    public function getCustomerDetails()
    {
        return $this->container['customer_details'];
    }

    /**
     * Sets customer_details
     *
     * @param \Cashfree\Model\CustomerDetails $customer_details customer_details
     *
     * @return self
     */
    public function setCustomerDetails($customer_details)
    {
        if (is_null($customer_details)) {
            throw new \InvalidArgumentException('non-nullable customer_details cannot be null');
        }
        $this->container['customer_details'] = $customer_details;

        return $this;
    }

    /**
     * Gets terminal
     *
     * @return \Cashfree\Model\TerminalDetails|null
     */
    public function getTerminal()
    {
        return $this->container['terminal'];
    }

    /**
     * Sets terminal
     *
     * @param \Cashfree\Model\TerminalDetails|null $terminal terminal
     *
     * @return self
     */
    public function setTerminal($terminal)
    {
        if (is_null($terminal)) {
            throw new \InvalidArgumentException('non-nullable terminal cannot be null');
        }
        $this->container['terminal'] = $terminal;

        return $this;
    }

    /**
     * Gets order_meta
     *
     * @return \Cashfree\Model\OrderMeta|null
     */
    public function getOrderMeta()
    {
        return $this->container['order_meta'];
    }

    /**
     * Sets order_meta
     *
     * @param \Cashfree\Model\OrderMeta|null $order_meta order_meta
     *
     * @return self
     */
    public function setOrderMeta($order_meta)
    {
        if (is_null($order_meta)) {
            throw new \InvalidArgumentException('non-nullable order_meta cannot be null');
        }
        $this->container['order_meta'] = $order_meta;

        return $this;
    }

    /**
     * Gets order_expiry_time
     *
     * @return string|null
     */
    public function getOrderExpiryTime()
    {
        return $this->container['order_expiry_time'];
    }

    /**
     * Sets order_expiry_time
     *
     * @param string|null $order_expiry_time Time after which the order expires. Customers will not be able to make the payment beyond the time specified here. We store timestamps in IST, but you can provide them in a valid ISO 8601 time format. Example 2021-07-02T10:20:12+05:30 for IST, 2021-07-02T10:20:12Z for UTC
     *
     * @return self
     */
    public function setOrderExpiryTime($order_expiry_time)
    {
        if (is_null($order_expiry_time)) {
            throw new \InvalidArgumentException('non-nullable order_expiry_time cannot be null');
        }
        $this->container['order_expiry_time'] = $order_expiry_time;

        return $this;
    }

    /**
     * Gets order_note
     *
     * @return string|null
     */
    public function getOrderNote()
    {
        return $this->container['order_note'];
    }

    /**
     * Sets order_note
     *
     * @param string|null $order_note Order note for reference.
     *
     * @return self
     */
    public function setOrderNote($order_note)
    {
        if (is_null($order_note)) {
            throw new \InvalidArgumentException('non-nullable order_note cannot be null');
        }
        if ((mb_strlen($order_note) > 200)) {
            throw new \InvalidArgumentException('invalid length for $order_note when calling CreateOrderRequest., must be smaller than or equal to 200.');
        }
        if ((mb_strlen($order_note) < 3)) {
            throw new \InvalidArgumentException('invalid length for $order_note when calling CreateOrderRequest., must be bigger than or equal to 3.');
        }

        $this->container['order_note'] = $order_note;

        return $this;
    }

    /**
     * Gets order_tags
     *
     * @return array<string,string>|null
     */
    public function getOrderTags()
    {
        return $this->container['order_tags'];
    }

    /**
     * Sets order_tags
     *
     * @param array<string,string>|null $order_tags Custom Tags in thr form of {\"key\":\"value\"} which can be passed for an order. A maximum of 10 tags can be added
     *
     * @return self
     */
    public function setOrderTags($order_tags)
    {
        if (is_null($order_tags)) {
            throw new \InvalidArgumentException('non-nullable order_tags cannot be null');
        }

        if ((count($order_tags) > 15)) {
            throw new \InvalidArgumentException('invalid value for $order_tags when calling CreateOrderRequest., number of items must be less than or equal to 15.');
        }
        $this->container['order_tags'] = $order_tags;

        return $this;
    }

    /**
     * Gets order_splits
     *
     * @return \Cashfree\Model\VendorSplit[]|null
     */
    public function getOrderSplits()
    {
        return $this->container['order_splits'];
    }

    /**
     * Sets order_splits
     *
     * @param \Cashfree\Model\VendorSplit[]|null $order_splits If you have Easy split enabled in your Cashfree account then you can use this option to split the order amount.
     *
     * @return self
     */
    public function setOrderSplits($order_splits)
    {
        if (is_null($order_splits)) {
            throw new \InvalidArgumentException('non-nullable order_splits cannot be null');
        }
        $this->container['order_splits'] = $order_splits;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


