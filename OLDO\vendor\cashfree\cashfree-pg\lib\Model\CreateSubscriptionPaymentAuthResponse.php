<?php
/**
 * CreateSubscriptionPaymentAuthResponse
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * Cashfree Payment Gateway APIs
 *
 * Cashfree's Payment Gateway APIs provide developers with a streamlined pathway to integrate advanced payment processing capabilities into their applications, platforms and websites.
 *
 * The version of the OpenAPI document: 2023-08-01
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 7.0.0
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Cashfree\Model;

use \ArrayAccess;
use \Cashfree\ObjectSerializer;

/**
 * CreateSubscriptionPaymentAuthResponse Class Doc Comment
 *
 * @category Class
 * @description The response returned in Get, Create or Manage Subscription Payment APIs.
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<string, mixed>
 */
class CreateSubscriptionPaymentAuthResponse implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'CreateSubscriptionPaymentAuthResponse';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'cf_payment_id' => 'string',
        'cf_subscription_id' => 'string',
        'failure_details' => '\Cashfree\Model\CreateSubscriptionPaymentAuthResponseFailureDetails',
        'payment_amount' => 'float',
        'payment_id' => 'string',
        'payment_initiated_date' => 'string',
        'payment_status' => 'string',
        'payment_type' => 'string',
        'subscription_id' => 'string',
        'payment_method' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'cf_payment_id' => null,
        'cf_subscription_id' => null,
        'failure_details' => null,
        'payment_amount' => 'float64',
        'payment_id' => null,
        'payment_initiated_date' => null,
        'payment_status' => null,
        'payment_type' => null,
        'subscription_id' => null,
        'payment_method' => null
    ];

    /**
      * Array of nullable properties. Used for (de)serialization
      *
      * @var boolean[]
      */
    protected static $openAPINullables = [
        'cf_payment_id' => false,
		'cf_subscription_id' => false,
		'failure_details' => false,
		'payment_amount' => false,
		'payment_id' => false,
		'payment_initiated_date' => false,
		'payment_status' => false,
		'payment_type' => false,
		'subscription_id' => false,
		'payment_method' => false
    ];

    /**
      * If a nullable field gets set to null, insert it here
      *
      * @var boolean[]
      */
    protected $openAPINullablesSetToNull = [];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of nullable properties
     *
     * @return array
     */
    protected static function openAPINullables(): array
    {
        return self::$openAPINullables;
    }

    /**
     * Array of nullable field names deliberately set to null
     *
     * @return boolean[]
     */
    private function getOpenAPINullablesSetToNull(): array
    {
        return $this->openAPINullablesSetToNull;
    }

    /**
     * Setter - Array of nullable field names deliberately set to null
     *
     * @param boolean[] $openAPINullablesSetToNull
     */
    private function setOpenAPINullablesSetToNull(array $openAPINullablesSetToNull): void
    {
        $this->openAPINullablesSetToNull = $openAPINullablesSetToNull;
    }

    /**
     * Checks if a property is nullable
     *
     * @param string $property
     * @return bool
     */
    public static function isNullable(string $property): bool
    {
        return self::openAPINullables()[$property] ?? false;
    }

    /**
     * Checks if a nullable property is set to null.
     *
     * @param string $property
     * @return bool
     */
    public function isNullableSetToNull(string $property): bool
    {
        return in_array($property, $this->getOpenAPINullablesSetToNull(), true);
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'cf_payment_id' => 'cf_payment_id',
        'cf_subscription_id' => 'cf_subscription_id',
        'failure_details' => 'failure_details',
        'payment_amount' => 'payment_amount',
        'payment_id' => 'payment_id',
        'payment_initiated_date' => 'payment_initiated_date',
        'payment_status' => 'payment_status',
        'payment_type' => 'payment_type',
        'subscription_id' => 'subscription_id',
        'payment_method' => 'payment_method'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'cf_payment_id' => 'setCfPaymentId',
        'cf_subscription_id' => 'setCfSubscriptionId',
        'failure_details' => 'setFailureDetails',
        'payment_amount' => 'setPaymentAmount',
        'payment_id' => 'setPaymentId',
        'payment_initiated_date' => 'setPaymentInitiatedDate',
        'payment_status' => 'setPaymentStatus',
        'payment_type' => 'setPaymentType',
        'subscription_id' => 'setSubscriptionId',
        'payment_method' => 'setPaymentMethod'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'cf_payment_id' => 'getCfPaymentId',
        'cf_subscription_id' => 'getCfSubscriptionId',
        'failure_details' => 'getFailureDetails',
        'payment_amount' => 'getPaymentAmount',
        'payment_id' => 'getPaymentId',
        'payment_initiated_date' => 'getPaymentInitiatedDate',
        'payment_status' => 'getPaymentStatus',
        'payment_type' => 'getPaymentType',
        'subscription_id' => 'getSubscriptionId',
        'payment_method' => 'getPaymentMethod'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->setIfExists('cf_payment_id', $data ?? [], null);
        $this->setIfExists('cf_subscription_id', $data ?? [], null);
        $this->setIfExists('failure_details', $data ?? [], null);
        $this->setIfExists('payment_amount', $data ?? [], null);
        $this->setIfExists('payment_id', $data ?? [], null);
        $this->setIfExists('payment_initiated_date', $data ?? [], null);
        $this->setIfExists('payment_status', $data ?? [], null);
        $this->setIfExists('payment_type', $data ?? [], null);
        $this->setIfExists('subscription_id', $data ?? [], null);
        $this->setIfExists('payment_method', $data ?? [], null);
    }

    /**
    * Sets $this->container[$variableName] to the given data or to the given default Value; if $variableName
    * is nullable and its value is set to null in the $fields array, then mark it as "set to null" in the
    * $this->openAPINullablesSetToNull array
    *
    * @param string $variableName
    * @param array  $fields
    * @param mixed  $defaultValue
    */
    private function setIfExists(string $variableName, array $fields, $defaultValue): void
    {
        if (self::isNullable($variableName) && array_key_exists($variableName, $fields) && is_null($fields[$variableName])) {
            $this->openAPINullablesSetToNull[] = $variableName;
        }

        $this->container[$variableName] = $fields[$variableName] ?? $defaultValue;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets cf_payment_id
     *
     * @return string|null
     */
    public function getCfPaymentId()
    {
        return $this->container['cf_payment_id'];
    }

    /**
     * Sets cf_payment_id
     *
     * @param string|null $cf_payment_id Cashfree subscription payment reference number
     *
     * @return self
     */
    public function setCfPaymentId($cf_payment_id)
    {
        if (is_null($cf_payment_id)) {
            throw new \InvalidArgumentException('non-nullable cf_payment_id cannot be null');
        }
        $this->container['cf_payment_id'] = $cf_payment_id;

        return $this;
    }

    /**
     * Gets cf_subscription_id
     *
     * @return string|null
     */
    public function getCfSubscriptionId()
    {
        return $this->container['cf_subscription_id'];
    }

    /**
     * Sets cf_subscription_id
     *
     * @param string|null $cf_subscription_id Cashfree subscription reference number
     *
     * @return self
     */
    public function setCfSubscriptionId($cf_subscription_id)
    {
        if (is_null($cf_subscription_id)) {
            throw new \InvalidArgumentException('non-nullable cf_subscription_id cannot be null');
        }
        $this->container['cf_subscription_id'] = $cf_subscription_id;

        return $this;
    }

    /**
     * Gets failure_details
     *
     * @return \Cashfree\Model\CreateSubscriptionPaymentAuthResponseFailureDetails|null
     */
    public function getFailureDetails()
    {
        return $this->container['failure_details'];
    }

    /**
     * Sets failure_details
     *
     * @param \Cashfree\Model\CreateSubscriptionPaymentAuthResponseFailureDetails|null $failure_details failure_details
     *
     * @return self
     */
    public function setFailureDetails($failure_details)
    {
        if (is_null($failure_details)) {
            throw new \InvalidArgumentException('non-nullable failure_details cannot be null');
        }
        $this->container['failure_details'] = $failure_details;

        return $this;
    }

    /**
     * Gets payment_amount
     *
     * @return float|null
     */
    public function getPaymentAmount()
    {
        return $this->container['payment_amount'];
    }

    /**
     * Sets payment_amount
     *
     * @param float|null $payment_amount The charge amount of the payment.
     *
     * @return self
     */
    public function setPaymentAmount($payment_amount)
    {
        if (is_null($payment_amount)) {
            throw new \InvalidArgumentException('non-nullable payment_amount cannot be null');
        }
        $this->container['payment_amount'] = $payment_amount;

        return $this;
    }

    /**
     * Gets payment_id
     *
     * @return string|null
     */
    public function getPaymentId()
    {
        return $this->container['payment_id'];
    }

    /**
     * Sets payment_id
     *
     * @param string|null $payment_id A unique ID passed by merchant for identifying the transaction.
     *
     * @return self
     */
    public function setPaymentId($payment_id)
    {
        if (is_null($payment_id)) {
            throw new \InvalidArgumentException('non-nullable payment_id cannot be null');
        }
        $this->container['payment_id'] = $payment_id;

        return $this;
    }

    /**
     * Gets payment_initiated_date
     *
     * @return string|null
     */
    public function getPaymentInitiatedDate()
    {
        return $this->container['payment_initiated_date'];
    }

    /**
     * Sets payment_initiated_date
     *
     * @param string|null $payment_initiated_date The date on which the payment was initiated.
     *
     * @return self
     */
    public function setPaymentInitiatedDate($payment_initiated_date)
    {
        if (is_null($payment_initiated_date)) {
            throw new \InvalidArgumentException('non-nullable payment_initiated_date cannot be null');
        }
        $this->container['payment_initiated_date'] = $payment_initiated_date;

        return $this;
    }

    /**
     * Gets payment_status
     *
     * @return string|null
     */
    public function getPaymentStatus()
    {
        return $this->container['payment_status'];
    }

    /**
     * Sets payment_status
     *
     * @param string|null $payment_status Status of the payment.
     *
     * @return self
     */
    public function setPaymentStatus($payment_status)
    {
        if (is_null($payment_status)) {
            throw new \InvalidArgumentException('non-nullable payment_status cannot be null');
        }
        $this->container['payment_status'] = $payment_status;

        return $this;
    }

    /**
     * Gets payment_type
     *
     * @return string|null
     */
    public function getPaymentType()
    {
        return $this->container['payment_type'];
    }

    /**
     * Sets payment_type
     *
     * @param string|null $payment_type Payment type. Can be AUTH or CHARGE.
     *
     * @return self
     */
    public function setPaymentType($payment_type)
    {
        if (is_null($payment_type)) {
            throw new \InvalidArgumentException('non-nullable payment_type cannot be null');
        }
        $this->container['payment_type'] = $payment_type;

        return $this;
    }

    /**
     * Gets subscription_id
     *
     * @return string|null
     */
    public function getSubscriptionId()
    {
        return $this->container['subscription_id'];
    }

    /**
     * Sets subscription_id
     *
     * @param string|null $subscription_id A unique ID passed by merchant for identifying the subscription.
     *
     * @return self
     */
    public function setSubscriptionId($subscription_id)
    {
        if (is_null($subscription_id)) {
            throw new \InvalidArgumentException('non-nullable subscription_id cannot be null');
        }
        $this->container['subscription_id'] = $subscription_id;

        return $this;
    }

    /**
     * Gets payment_method
     *
     * @return string|null
     */
    public function getPaymentMethod()
    {
        return $this->container['payment_method'];
    }

    /**
     * Sets payment_method
     *
     * @param string|null $payment_method Payment method used for the authorization.
     *
     * @return self
     */
    public function setPaymentMethod($payment_method)
    {
        if (is_null($payment_method)) {
            throw new \InvalidArgumentException('non-nullable payment_method cannot be null');
        }
        $this->container['payment_method'] = $payment_method;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


