<?php
/**
 * CreateTerminalTransactionRequest
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * Cashfree Payment Gateway APIs
 *
 * Cashfree's Payment Gateway APIs provide developers with a streamlined pathway to integrate advanced payment processing capabilities into their applications, platforms and websites.
 *
 * The version of the OpenAPI document: 2023-08-01
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 7.0.0
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Cashfree\Model;

use \ArrayAccess;
use \Cashfree\ObjectSerializer;

/**
 * CreateTerminalTransactionRequest Class Doc Comment
 *
 * @category Class
 * @description Request body to create a terminal transaction
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<string, mixed>
 */
class CreateTerminalTransactionRequest implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'CreateTerminalTransactionRequest';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'cf_order_id' => 'string',
        'cf_terminal_id' => 'string',
        'payment_method' => 'string',
        'terminal_phone_no' => 'string',
        'add_invoice' => 'bool'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'cf_order_id' => null,
        'cf_terminal_id' => null,
        'payment_method' => null,
        'terminal_phone_no' => null,
        'add_invoice' => null
    ];

    /**
      * Array of nullable properties. Used for (de)serialization
      *
      * @var boolean[]
      */
    protected static $openAPINullables = [
        'cf_order_id' => false,
		'cf_terminal_id' => false,
		'payment_method' => false,
		'terminal_phone_no' => false,
		'add_invoice' => false
    ];

    /**
      * If a nullable field gets set to null, insert it here
      *
      * @var boolean[]
      */
    protected $openAPINullablesSetToNull = [];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of nullable properties
     *
     * @return array
     */
    protected static function openAPINullables(): array
    {
        return self::$openAPINullables;
    }

    /**
     * Array of nullable field names deliberately set to null
     *
     * @return boolean[]
     */
    private function getOpenAPINullablesSetToNull(): array
    {
        return $this->openAPINullablesSetToNull;
    }

    /**
     * Setter - Array of nullable field names deliberately set to null
     *
     * @param boolean[] $openAPINullablesSetToNull
     */
    private function setOpenAPINullablesSetToNull(array $openAPINullablesSetToNull): void
    {
        $this->openAPINullablesSetToNull = $openAPINullablesSetToNull;
    }

    /**
     * Checks if a property is nullable
     *
     * @param string $property
     * @return bool
     */
    public static function isNullable(string $property): bool
    {
        return self::openAPINullables()[$property] ?? false;
    }

    /**
     * Checks if a nullable property is set to null.
     *
     * @param string $property
     * @return bool
     */
    public function isNullableSetToNull(string $property): bool
    {
        return in_array($property, $this->getOpenAPINullablesSetToNull(), true);
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'cf_order_id' => 'cf_order_id',
        'cf_terminal_id' => 'cf_terminal_id',
        'payment_method' => 'payment_method',
        'terminal_phone_no' => 'terminal_phone_no',
        'add_invoice' => 'add_invoice'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'cf_order_id' => 'setCfOrderId',
        'cf_terminal_id' => 'setCfTerminalId',
        'payment_method' => 'setPaymentMethod',
        'terminal_phone_no' => 'setTerminalPhoneNo',
        'add_invoice' => 'setAddInvoice'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'cf_order_id' => 'getCfOrderId',
        'cf_terminal_id' => 'getCfTerminalId',
        'payment_method' => 'getPaymentMethod',
        'terminal_phone_no' => 'getTerminalPhoneNo',
        'add_invoice' => 'getAddInvoice'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->setIfExists('cf_order_id', $data ?? [], null);
        $this->setIfExists('cf_terminal_id', $data ?? [], null);
        $this->setIfExists('payment_method', $data ?? [], null);
        $this->setIfExists('terminal_phone_no', $data ?? [], null);
        $this->setIfExists('add_invoice', $data ?? [], null);
    }

    /**
    * Sets $this->container[$variableName] to the given data or to the given default Value; if $variableName
    * is nullable and its value is set to null in the $fields array, then mark it as "set to null" in the
    * $this->openAPINullablesSetToNull array
    *
    * @param string $variableName
    * @param array  $fields
    * @param mixed  $defaultValue
    */
    private function setIfExists(string $variableName, array $fields, $defaultValue): void
    {
        if (self::isNullable($variableName) && array_key_exists($variableName, $fields) && is_null($fields[$variableName])) {
            $this->openAPINullablesSetToNull[] = $variableName;
        }

        $this->container[$variableName] = $fields[$variableName] ?? $defaultValue;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if ($this->container['cf_order_id'] === null) {
            $invalidProperties[] = "'cf_order_id' can't be null";
        }
        if ($this->container['payment_method'] === null) {
            $invalidProperties[] = "'payment_method' can't be null";
        }
        if ((mb_strlen($this->container['payment_method']) > 100)) {
            $invalidProperties[] = "invalid value for 'payment_method', the character length must be smaller than or equal to 100.";
        }

        if ((mb_strlen($this->container['payment_method']) < 3)) {
            $invalidProperties[] = "invalid value for 'payment_method', the character length must be bigger than or equal to 3.";
        }

        if (!is_null($this->container['terminal_phone_no']) && (mb_strlen($this->container['terminal_phone_no']) > 10)) {
            $invalidProperties[] = "invalid value for 'terminal_phone_no', the character length must be smaller than or equal to 10.";
        }

        if (!is_null($this->container['terminal_phone_no']) && (mb_strlen($this->container['terminal_phone_no']) < 10)) {
            $invalidProperties[] = "invalid value for 'terminal_phone_no', the character length must be bigger than or equal to 10.";
        }

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets cf_order_id
     *
     * @return string
     */
    public function getCfOrderId()
    {
        return $this->container['cf_order_id'];
    }

    /**
     * Sets cf_order_id
     *
     * @param string $cf_order_id cashfree order ID that was returned while creating an order.
     *
     * @return self
     */
    public function setCfOrderId($cf_order_id)
    {
        if (is_null($cf_order_id)) {
            throw new \InvalidArgumentException('non-nullable cf_order_id cannot be null');
        }
        $this->container['cf_order_id'] = $cf_order_id;

        return $this;
    }

    /**
     * Gets cf_terminal_id
     *
     * @return string|null
     */
    public function getCfTerminalId()
    {
        return $this->container['cf_terminal_id'];
    }

    /**
     * Sets cf_terminal_id
     *
     * @param string|null $cf_terminal_id cashfree terminal id. this is a required parameter when you do not provide the terminal phone number.
     *
     * @return self
     */
    public function setCfTerminalId($cf_terminal_id)
    {
        if (is_null($cf_terminal_id)) {
            throw new \InvalidArgumentException('non-nullable cf_terminal_id cannot be null');
        }
        $this->container['cf_terminal_id'] = $cf_terminal_id;

        return $this;
    }

    /**
     * Gets payment_method
     *
     * @return string
     */
    public function getPaymentMethod()
    {
        return $this->container['payment_method'];
    }

    /**
     * Sets payment_method
     *
     * @param string $payment_method mention the payment method used for the transaction. possible values - QR_CODE, LINK.
     *
     * @return self
     */
    public function setPaymentMethod($payment_method)
    {
        if (is_null($payment_method)) {
            throw new \InvalidArgumentException('non-nullable payment_method cannot be null');
        }
        if ((mb_strlen($payment_method) > 100)) {
            throw new \InvalidArgumentException('invalid length for $payment_method when calling CreateTerminalTransactionRequest., must be smaller than or equal to 100.');
        }
        if ((mb_strlen($payment_method) < 3)) {
            throw new \InvalidArgumentException('invalid length for $payment_method when calling CreateTerminalTransactionRequest., must be bigger than or equal to 3.');
        }

        $this->container['payment_method'] = $payment_method;

        return $this;
    }

    /**
     * Gets terminal_phone_no
     *
     * @return string|null
     */
    public function getTerminalPhoneNo()
    {
        return $this->container['terminal_phone_no'];
    }

    /**
     * Sets terminal_phone_no
     *
     * @param string|null $terminal_phone_no agent mobile number assigned to the terminal. this is a required parameter when you do not provide the cf_terminal_id.
     *
     * @return self
     */
    public function setTerminalPhoneNo($terminal_phone_no)
    {
        if (is_null($terminal_phone_no)) {
            throw new \InvalidArgumentException('non-nullable terminal_phone_no cannot be null');
        }
        if ((mb_strlen($terminal_phone_no) > 10)) {
            throw new \InvalidArgumentException('invalid length for $terminal_phone_no when calling CreateTerminalTransactionRequest., must be smaller than or equal to 10.');
        }
        if ((mb_strlen($terminal_phone_no) < 10)) {
            throw new \InvalidArgumentException('invalid length for $terminal_phone_no when calling CreateTerminalTransactionRequest., must be bigger than or equal to 10.');
        }

        $this->container['terminal_phone_no'] = $terminal_phone_no;

        return $this;
    }

    /**
     * Gets add_invoice
     *
     * @return bool|null
     */
    public function getAddInvoice()
    {
        return $this->container['add_invoice'];
    }

    /**
     * Sets add_invoice
     *
     * @param bool|null $add_invoice make it true to have request be sent to create a Dynamic GST QR Code.
     *
     * @return self
     */
    public function setAddInvoice($add_invoice)
    {
        if (is_null($add_invoice)) {
            throw new \InvalidArgumentException('non-nullable add_invoice cannot be null');
        }
        $this->container['add_invoice'] = $add_invoice;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


