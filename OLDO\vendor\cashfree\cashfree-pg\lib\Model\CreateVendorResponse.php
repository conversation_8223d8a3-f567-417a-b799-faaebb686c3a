<?php
/**
 * CreateVendorResponse
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * Cashfree Payment Gateway APIs
 *
 * Cashfree's Payment Gateway APIs provide developers with a streamlined pathway to integrate advanced payment processing capabilities into their applications, platforms and websites.
 *
 * The version of the OpenAPI document: 2023-08-01
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 7.0.0
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Cashfree\Model;

use \ArrayAccess;
use \Cashfree\ObjectSerializer;

/**
 * CreateVendorResponse Class Doc Comment
 *
 * @category Class
 * @description Create Vendor Response
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<string, mixed>
 */
class CreateVendorResponse implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'CreateVendorResponse';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'email' => 'string',
        'status' => 'string',
        'bank' => '\Cashfree\Model\BankDetails[]',
        'upi' => 'string',
        'phone' => 'float',
        'name' => 'string',
        'vendor_id' => 'string',
        'schedule_option' => '\Cashfree\Model\ScheduleOption[]',
        'kyc_details' => '\Cashfree\Model\KycDetails[]',
        'dashboard_access' => 'bool',
        'bank_details' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'email' => null,
        'status' => null,
        'bank' => null,
        'upi' => null,
        'phone' => null,
        'name' => null,
        'vendor_id' => null,
        'schedule_option' => null,
        'kyc_details' => null,
        'dashboard_access' => null,
        'bank_details' => null
    ];

    /**
      * Array of nullable properties. Used for (de)serialization
      *
      * @var boolean[]
      */
    protected static $openAPINullables = [
        'email' => false,
		'status' => false,
		'bank' => false,
		'upi' => false,
		'phone' => false,
		'name' => false,
		'vendor_id' => false,
		'schedule_option' => false,
		'kyc_details' => false,
		'dashboard_access' => false,
		'bank_details' => false
    ];

    /**
      * If a nullable field gets set to null, insert it here
      *
      * @var boolean[]
      */
    protected $openAPINullablesSetToNull = [];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of nullable properties
     *
     * @return array
     */
    protected static function openAPINullables(): array
    {
        return self::$openAPINullables;
    }

    /**
     * Array of nullable field names deliberately set to null
     *
     * @return boolean[]
     */
    private function getOpenAPINullablesSetToNull(): array
    {
        return $this->openAPINullablesSetToNull;
    }

    /**
     * Setter - Array of nullable field names deliberately set to null
     *
     * @param boolean[] $openAPINullablesSetToNull
     */
    private function setOpenAPINullablesSetToNull(array $openAPINullablesSetToNull): void
    {
        $this->openAPINullablesSetToNull = $openAPINullablesSetToNull;
    }

    /**
     * Checks if a property is nullable
     *
     * @param string $property
     * @return bool
     */
    public static function isNullable(string $property): bool
    {
        return self::openAPINullables()[$property] ?? false;
    }

    /**
     * Checks if a nullable property is set to null.
     *
     * @param string $property
     * @return bool
     */
    public function isNullableSetToNull(string $property): bool
    {
        return in_array($property, $this->getOpenAPINullablesSetToNull(), true);
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'email' => 'email',
        'status' => 'status',
        'bank' => 'bank',
        'upi' => 'upi',
        'phone' => 'phone',
        'name' => 'name',
        'vendor_id' => 'vendor_id',
        'schedule_option' => 'schedule_option',
        'kyc_details' => 'kyc_details',
        'dashboard_access' => 'dashboard_access',
        'bank_details' => 'bank_details'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'email' => 'setEmail',
        'status' => 'setStatus',
        'bank' => 'setBank',
        'upi' => 'setUpi',
        'phone' => 'setPhone',
        'name' => 'setName',
        'vendor_id' => 'setVendorId',
        'schedule_option' => 'setScheduleOption',
        'kyc_details' => 'setKycDetails',
        'dashboard_access' => 'setDashboardAccess',
        'bank_details' => 'setBankDetails'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'email' => 'getEmail',
        'status' => 'getStatus',
        'bank' => 'getBank',
        'upi' => 'getUpi',
        'phone' => 'getPhone',
        'name' => 'getName',
        'vendor_id' => 'getVendorId',
        'schedule_option' => 'getScheduleOption',
        'kyc_details' => 'getKycDetails',
        'dashboard_access' => 'getDashboardAccess',
        'bank_details' => 'getBankDetails'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->setIfExists('email', $data ?? [], null);
        $this->setIfExists('status', $data ?? [], null);
        $this->setIfExists('bank', $data ?? [], null);
        $this->setIfExists('upi', $data ?? [], null);
        $this->setIfExists('phone', $data ?? [], null);
        $this->setIfExists('name', $data ?? [], null);
        $this->setIfExists('vendor_id', $data ?? [], null);
        $this->setIfExists('schedule_option', $data ?? [], null);
        $this->setIfExists('kyc_details', $data ?? [], null);
        $this->setIfExists('dashboard_access', $data ?? [], null);
        $this->setIfExists('bank_details', $data ?? [], null);
    }

    /**
    * Sets $this->container[$variableName] to the given data or to the given default Value; if $variableName
    * is nullable and its value is set to null in the $fields array, then mark it as "set to null" in the
    * $this->openAPINullablesSetToNull array
    *
    * @param string $variableName
    * @param array  $fields
    * @param mixed  $defaultValue
    */
    private function setIfExists(string $variableName, array $fields, $defaultValue): void
    {
        if (self::isNullable($variableName) && array_key_exists($variableName, $fields) && is_null($fields[$variableName])) {
            $this->openAPINullablesSetToNull[] = $variableName;
        }

        $this->container[$variableName] = $fields[$variableName] ?? $defaultValue;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets email
     *
     * @return string|null
     */
    public function getEmail()
    {
        return $this->container['email'];
    }

    /**
     * Sets email
     *
     * @param string|null $email email
     *
     * @return self
     */
    public function setEmail($email)
    {
        if (is_null($email)) {
            throw new \InvalidArgumentException('non-nullable email cannot be null');
        }
        $this->container['email'] = $email;

        return $this;
    }

    /**
     * Gets status
     *
     * @return string|null
     */
    public function getStatus()
    {
        return $this->container['status'];
    }

    /**
     * Sets status
     *
     * @param string|null $status status
     *
     * @return self
     */
    public function setStatus($status)
    {
        if (is_null($status)) {
            throw new \InvalidArgumentException('non-nullable status cannot be null');
        }
        $this->container['status'] = $status;

        return $this;
    }

    /**
     * Gets bank
     *
     * @return \Cashfree\Model\BankDetails[]|null
     */
    public function getBank()
    {
        return $this->container['bank'];
    }

    /**
     * Sets bank
     *
     * @param \Cashfree\Model\BankDetails[]|null $bank bank
     *
     * @return self
     */
    public function setBank($bank)
    {
        if (is_null($bank)) {
            throw new \InvalidArgumentException('non-nullable bank cannot be null');
        }
        $this->container['bank'] = $bank;

        return $this;
    }

    /**
     * Gets upi
     *
     * @return string|null
     */
    public function getUpi()
    {
        return $this->container['upi'];
    }

    /**
     * Sets upi
     *
     * @param string|null $upi upi
     *
     * @return self
     */
    public function setUpi($upi)
    {
        if (is_null($upi)) {
            throw new \InvalidArgumentException('non-nullable upi cannot be null');
        }
        $this->container['upi'] = $upi;

        return $this;
    }

    /**
     * Gets phone
     *
     * @return float|null
     */
    public function getPhone()
    {
        return $this->container['phone'];
    }

    /**
     * Sets phone
     *
     * @param float|null $phone phone
     *
     * @return self
     */
    public function setPhone($phone)
    {
        if (is_null($phone)) {
            throw new \InvalidArgumentException('non-nullable phone cannot be null');
        }
        $this->container['phone'] = $phone;

        return $this;
    }

    /**
     * Gets name
     *
     * @return string|null
     */
    public function getName()
    {
        return $this->container['name'];
    }

    /**
     * Sets name
     *
     * @param string|null $name name
     *
     * @return self
     */
    public function setName($name)
    {
        if (is_null($name)) {
            throw new \InvalidArgumentException('non-nullable name cannot be null');
        }
        $this->container['name'] = $name;

        return $this;
    }

    /**
     * Gets vendor_id
     *
     * @return string|null
     */
    public function getVendorId()
    {
        return $this->container['vendor_id'];
    }

    /**
     * Sets vendor_id
     *
     * @param string|null $vendor_id vendor_id
     *
     * @return self
     */
    public function setVendorId($vendor_id)
    {
        if (is_null($vendor_id)) {
            throw new \InvalidArgumentException('non-nullable vendor_id cannot be null');
        }
        $this->container['vendor_id'] = $vendor_id;

        return $this;
    }

    /**
     * Gets schedule_option
     *
     * @return \Cashfree\Model\ScheduleOption[]|null
     */
    public function getScheduleOption()
    {
        return $this->container['schedule_option'];
    }

    /**
     * Sets schedule_option
     *
     * @param \Cashfree\Model\ScheduleOption[]|null $schedule_option schedule_option
     *
     * @return self
     */
    public function setScheduleOption($schedule_option)
    {
        if (is_null($schedule_option)) {
            throw new \InvalidArgumentException('non-nullable schedule_option cannot be null');
        }
        $this->container['schedule_option'] = $schedule_option;

        return $this;
    }

    /**
     * Gets kyc_details
     *
     * @return \Cashfree\Model\KycDetails[]|null
     */
    public function getKycDetails()
    {
        return $this->container['kyc_details'];
    }

    /**
     * Sets kyc_details
     *
     * @param \Cashfree\Model\KycDetails[]|null $kyc_details kyc_details
     *
     * @return self
     */
    public function setKycDetails($kyc_details)
    {
        if (is_null($kyc_details)) {
            throw new \InvalidArgumentException('non-nullable kyc_details cannot be null');
        }
        $this->container['kyc_details'] = $kyc_details;

        return $this;
    }

    /**
     * Gets dashboard_access
     *
     * @return bool|null
     */
    public function getDashboardAccess()
    {
        return $this->container['dashboard_access'];
    }

    /**
     * Sets dashboard_access
     *
     * @param bool|null $dashboard_access dashboard_access
     *
     * @return self
     */
    public function setDashboardAccess($dashboard_access)
    {
        if (is_null($dashboard_access)) {
            throw new \InvalidArgumentException('non-nullable dashboard_access cannot be null');
        }
        $this->container['dashboard_access'] = $dashboard_access;

        return $this;
    }

    /**
     * Gets bank_details
     *
     * @return string|null
     */
    public function getBankDetails()
    {
        return $this->container['bank_details'];
    }

    /**
     * Sets bank_details
     *
     * @param string|null $bank_details bank_details
     *
     * @return self
     */
    public function setBankDetails($bank_details)
    {
        if (is_null($bank_details)) {
            throw new \InvalidArgumentException('non-nullable bank_details cannot be null');
        }
        $this->container['bank_details'] = $bank_details;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


