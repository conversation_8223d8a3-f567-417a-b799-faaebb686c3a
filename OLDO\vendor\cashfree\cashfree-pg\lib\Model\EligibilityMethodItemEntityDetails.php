<?php
/**
 * EligibilityMethodItemEntityDetails
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * Cashfree Payment Gateway APIs
 *
 * Cashfree's Payment Gateway APIs provide developers with a streamlined pathway to integrate advanced payment processing capabilities into their applications, platforms and websites.
 *
 * The version of the OpenAPI document: 2023-08-01
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 7.0.0
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Cashfree\Model;

use \ArrayAccess;
use \Cashfree\ObjectSerializer;

/**
 * EligibilityMethodItemEntityDetails Class Doc Comment
 *
 * @category Class
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<string, mixed>
 */
class EligibilityMethodItemEntityDetails implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'EligibilityMethodItem_entity_details';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'account_types' => 'string[]',
        'frequent_bank_details' => '\Cashfree\Model\SubscriptionBankDetails[]',
        'all_bank_details' => '\Cashfree\Model\SubscriptionBankDetails[]',
        'available_handles' => '\Cashfree\Model\EligibilityMethodItemEntityDetailsAvailableHandlesInner[]',
        'allowed_card_types' => 'string[]'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'account_types' => null,
        'frequent_bank_details' => null,
        'all_bank_details' => null,
        'available_handles' => null,
        'allowed_card_types' => null
    ];

    /**
      * Array of nullable properties. Used for (de)serialization
      *
      * @var boolean[]
      */
    protected static $openAPINullables = [
        'account_types' => false,
		'frequent_bank_details' => false,
		'all_bank_details' => false,
		'available_handles' => false,
		'allowed_card_types' => false
    ];

    /**
      * If a nullable field gets set to null, insert it here
      *
      * @var boolean[]
      */
    protected $openAPINullablesSetToNull = [];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of nullable properties
     *
     * @return array
     */
    protected static function openAPINullables(): array
    {
        return self::$openAPINullables;
    }

    /**
     * Array of nullable field names deliberately set to null
     *
     * @return boolean[]
     */
    private function getOpenAPINullablesSetToNull(): array
    {
        return $this->openAPINullablesSetToNull;
    }

    /**
     * Setter - Array of nullable field names deliberately set to null
     *
     * @param boolean[] $openAPINullablesSetToNull
     */
    private function setOpenAPINullablesSetToNull(array $openAPINullablesSetToNull): void
    {
        $this->openAPINullablesSetToNull = $openAPINullablesSetToNull;
    }

    /**
     * Checks if a property is nullable
     *
     * @param string $property
     * @return bool
     */
    public static function isNullable(string $property): bool
    {
        return self::openAPINullables()[$property] ?? false;
    }

    /**
     * Checks if a nullable property is set to null.
     *
     * @param string $property
     * @return bool
     */
    public function isNullableSetToNull(string $property): bool
    {
        return in_array($property, $this->getOpenAPINullablesSetToNull(), true);
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'account_types' => 'account_types',
        'frequent_bank_details' => 'frequent_bank_details',
        'all_bank_details' => 'all_bank_details',
        'available_handles' => 'available_handles',
        'allowed_card_types' => 'allowed_card_types'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'account_types' => 'setAccountTypes',
        'frequent_bank_details' => 'setFrequentBankDetails',
        'all_bank_details' => 'setAllBankDetails',
        'available_handles' => 'setAvailableHandles',
        'allowed_card_types' => 'setAllowedCardTypes'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'account_types' => 'getAccountTypes',
        'frequent_bank_details' => 'getFrequentBankDetails',
        'all_bank_details' => 'getAllBankDetails',
        'available_handles' => 'getAvailableHandles',
        'allowed_card_types' => 'getAllowedCardTypes'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->setIfExists('account_types', $data ?? [], null);
        $this->setIfExists('frequent_bank_details', $data ?? [], null);
        $this->setIfExists('all_bank_details', $data ?? [], null);
        $this->setIfExists('available_handles', $data ?? [], null);
        $this->setIfExists('allowed_card_types', $data ?? [], null);
    }

    /**
    * Sets $this->container[$variableName] to the given data or to the given default Value; if $variableName
    * is nullable and its value is set to null in the $fields array, then mark it as "set to null" in the
    * $this->openAPINullablesSetToNull array
    *
    * @param string $variableName
    * @param array  $fields
    * @param mixed  $defaultValue
    */
    private function setIfExists(string $variableName, array $fields, $defaultValue): void
    {
        if (self::isNullable($variableName) && array_key_exists($variableName, $fields) && is_null($fields[$variableName])) {
            $this->openAPINullablesSetToNull[] = $variableName;
        }

        $this->container[$variableName] = $fields[$variableName] ?? $defaultValue;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets account_types
     *
     * @return string[]|null
     */
    public function getAccountTypes()
    {
        return $this->container['account_types'];
    }

    /**
     * Sets account_types
     *
     * @param string[]|null $account_types List of account types associated with the payment method. (e.g. SAVINGS or CURRENT)
     *
     * @return self
     */
    public function setAccountTypes($account_types)
    {
        if (is_null($account_types)) {
            throw new \InvalidArgumentException('non-nullable account_types cannot be null');
        }
        $this->container['account_types'] = $account_types;

        return $this;
    }

    /**
     * Gets frequent_bank_details
     *
     * @return \Cashfree\Model\SubscriptionBankDetails[]|null
     */
    public function getFrequentBankDetails()
    {
        return $this->container['frequent_bank_details'];
    }

    /**
     * Sets frequent_bank_details
     *
     * @param \Cashfree\Model\SubscriptionBankDetails[]|null $frequent_bank_details List of the most frequently used banks.
     *
     * @return self
     */
    public function setFrequentBankDetails($frequent_bank_details)
    {
        if (is_null($frequent_bank_details)) {
            throw new \InvalidArgumentException('non-nullable frequent_bank_details cannot be null');
        }
        $this->container['frequent_bank_details'] = $frequent_bank_details;

        return $this;
    }

    /**
     * Gets all_bank_details
     *
     * @return \Cashfree\Model\SubscriptionBankDetails[]|null
     */
    public function getAllBankDetails()
    {
        return $this->container['all_bank_details'];
    }

    /**
     * Sets all_bank_details
     *
     * @param \Cashfree\Model\SubscriptionBankDetails[]|null $all_bank_details Details about all banks associated with the payment method.
     *
     * @return self
     */
    public function setAllBankDetails($all_bank_details)
    {
        if (is_null($all_bank_details)) {
            throw new \InvalidArgumentException('non-nullable all_bank_details cannot be null');
        }
        $this->container['all_bank_details'] = $all_bank_details;

        return $this;
    }

    /**
     * Gets available_handles
     *
     * @return \Cashfree\Model\EligibilityMethodItemEntityDetailsAvailableHandlesInner[]|null
     */
    public function getAvailableHandles()
    {
        return $this->container['available_handles'];
    }

    /**
     * Sets available_handles
     *
     * @param \Cashfree\Model\EligibilityMethodItemEntityDetailsAvailableHandlesInner[]|null $available_handles List of supported VPA handles.
     *
     * @return self
     */
    public function setAvailableHandles($available_handles)
    {
        if (is_null($available_handles)) {
            throw new \InvalidArgumentException('non-nullable available_handles cannot be null');
        }
        $this->container['available_handles'] = $available_handles;

        return $this;
    }

    /**
     * Gets allowed_card_types
     *
     * @return string[]|null
     */
    public function getAllowedCardTypes()
    {
        return $this->container['allowed_card_types'];
    }

    /**
     * Sets allowed_card_types
     *
     * @param string[]|null $allowed_card_types List of allowed card types. (e.g. DEBIT_CARD, CREDIT_CARD)
     *
     * @return self
     */
    public function setAllowedCardTypes($allowed_card_types)
    {
        if (is_null($allowed_card_types)) {
            throw new \InvalidArgumentException('non-nullable allowed_card_types cannot be null');
        }
        $this->container['allowed_card_types'] = $allowed_card_types;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


