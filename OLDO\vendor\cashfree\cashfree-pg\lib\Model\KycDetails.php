<?php
/**
 * KycDetails
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * Cashfree Payment Gateway APIs
 *
 * Cashfree's Payment Gateway APIs provide developers with a streamlined pathway to integrate advanced payment processing capabilities into their applications, platforms and websites.
 *
 * The version of the OpenAPI document: 2023-08-01
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 7.0.0
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Cashfree\Model;

use \ArrayAccess;
use \Cashfree\ObjectSerializer;

/**
 * KycDetails Class Doc Comment
 *
 * @category Class
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<string, mixed>
 */
class KycDetails implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'KycDetails';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'account_type' => 'string',
        'business_type' => 'string',
        'uidai' => 'float',
        'gst' => 'string',
        'cin' => 'string',
        'pan' => 'string',
        'passport_number' => 'string',
        'driving_license' => 'string',
        'voter_id' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'account_type' => null,
        'business_type' => null,
        'uidai' => null,
        'gst' => null,
        'cin' => null,
        'pan' => null,
        'passport_number' => null,
        'driving_license' => null,
        'voter_id' => null
    ];

    /**
      * Array of nullable properties. Used for (de)serialization
      *
      * @var boolean[]
      */
    protected static $openAPINullables = [
        'account_type' => false,
		'business_type' => false,
		'uidai' => false,
		'gst' => false,
		'cin' => false,
		'pan' => false,
		'passport_number' => false,
		'driving_license' => false,
		'voter_id' => false
    ];

    /**
      * If a nullable field gets set to null, insert it here
      *
      * @var boolean[]
      */
    protected $openAPINullablesSetToNull = [];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of nullable properties
     *
     * @return array
     */
    protected static function openAPINullables(): array
    {
        return self::$openAPINullables;
    }

    /**
     * Array of nullable field names deliberately set to null
     *
     * @return boolean[]
     */
    private function getOpenAPINullablesSetToNull(): array
    {
        return $this->openAPINullablesSetToNull;
    }

    /**
     * Setter - Array of nullable field names deliberately set to null
     *
     * @param boolean[] $openAPINullablesSetToNull
     */
    private function setOpenAPINullablesSetToNull(array $openAPINullablesSetToNull): void
    {
        $this->openAPINullablesSetToNull = $openAPINullablesSetToNull;
    }

    /**
     * Checks if a property is nullable
     *
     * @param string $property
     * @return bool
     */
    public static function isNullable(string $property): bool
    {
        return self::openAPINullables()[$property] ?? false;
    }

    /**
     * Checks if a nullable property is set to null.
     *
     * @param string $property
     * @return bool
     */
    public function isNullableSetToNull(string $property): bool
    {
        return in_array($property, $this->getOpenAPINullablesSetToNull(), true);
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'account_type' => 'account_type',
        'business_type' => 'business_type',
        'uidai' => 'uidai',
        'gst' => 'gst',
        'cin' => 'cin',
        'pan' => 'pan',
        'passport_number' => 'passport_number',
        'driving_license' => 'driving_license',
        'voter_id' => 'voter_id'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'account_type' => 'setAccountType',
        'business_type' => 'setBusinessType',
        'uidai' => 'setUidai',
        'gst' => 'setGst',
        'cin' => 'setCin',
        'pan' => 'setPan',
        'passport_number' => 'setPassportNumber',
        'driving_license' => 'setDrivingLicense',
        'voter_id' => 'setVoterId'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'account_type' => 'getAccountType',
        'business_type' => 'getBusinessType',
        'uidai' => 'getUidai',
        'gst' => 'getGst',
        'cin' => 'getCin',
        'pan' => 'getPan',
        'passport_number' => 'getPassportNumber',
        'driving_license' => 'getDrivingLicense',
        'voter_id' => 'getVoterId'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->setIfExists('account_type', $data ?? [], null);
        $this->setIfExists('business_type', $data ?? [], null);
        $this->setIfExists('uidai', $data ?? [], null);
        $this->setIfExists('gst', $data ?? [], null);
        $this->setIfExists('cin', $data ?? [], null);
        $this->setIfExists('pan', $data ?? [], null);
        $this->setIfExists('passport_number', $data ?? [], null);
        $this->setIfExists('driving_license', $data ?? [], null);
        $this->setIfExists('voter_id', $data ?? [], null);
    }

    /**
    * Sets $this->container[$variableName] to the given data or to the given default Value; if $variableName
    * is nullable and its value is set to null in the $fields array, then mark it as "set to null" in the
    * $this->openAPINullablesSetToNull array
    *
    * @param string $variableName
    * @param array  $fields
    * @param mixed  $defaultValue
    */
    private function setIfExists(string $variableName, array $fields, $defaultValue): void
    {
        if (self::isNullable($variableName) && array_key_exists($variableName, $fields) && is_null($fields[$variableName])) {
            $this->openAPINullablesSetToNull[] = $variableName;
        }

        $this->container[$variableName] = $fields[$variableName] ?? $defaultValue;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets account_type
     *
     * @return string|null
     */
    public function getAccountType()
    {
        return $this->container['account_type'];
    }

    /**
     * Sets account_type
     *
     * @param string|null $account_type account_type
     *
     * @return self
     */
    public function setAccountType($account_type)
    {
        if (is_null($account_type)) {
            throw new \InvalidArgumentException('non-nullable account_type cannot be null');
        }
        $this->container['account_type'] = $account_type;

        return $this;
    }

    /**
     * Gets business_type
     *
     * @return string|null
     */
    public function getBusinessType()
    {
        return $this->container['business_type'];
    }

    /**
     * Sets business_type
     *
     * @param string|null $business_type business_type
     *
     * @return self
     */
    public function setBusinessType($business_type)
    {
        if (is_null($business_type)) {
            throw new \InvalidArgumentException('non-nullable business_type cannot be null');
        }
        $this->container['business_type'] = $business_type;

        return $this;
    }

    /**
     * Gets uidai
     *
     * @return float|null
     */
    public function getUidai()
    {
        return $this->container['uidai'];
    }

    /**
     * Sets uidai
     *
     * @param float|null $uidai uidai
     *
     * @return self
     */
    public function setUidai($uidai)
    {
        if (is_null($uidai)) {
            throw new \InvalidArgumentException('non-nullable uidai cannot be null');
        }
        $this->container['uidai'] = $uidai;

        return $this;
    }

    /**
     * Gets gst
     *
     * @return string|null
     */
    public function getGst()
    {
        return $this->container['gst'];
    }

    /**
     * Sets gst
     *
     * @param string|null $gst gst
     *
     * @return self
     */
    public function setGst($gst)
    {
        if (is_null($gst)) {
            throw new \InvalidArgumentException('non-nullable gst cannot be null');
        }
        $this->container['gst'] = $gst;

        return $this;
    }

    /**
     * Gets cin
     *
     * @return string|null
     */
    public function getCin()
    {
        return $this->container['cin'];
    }

    /**
     * Sets cin
     *
     * @param string|null $cin cin
     *
     * @return self
     */
    public function setCin($cin)
    {
        if (is_null($cin)) {
            throw new \InvalidArgumentException('non-nullable cin cannot be null');
        }
        $this->container['cin'] = $cin;

        return $this;
    }

    /**
     * Gets pan
     *
     * @return string|null
     */
    public function getPan()
    {
        return $this->container['pan'];
    }

    /**
     * Sets pan
     *
     * @param string|null $pan pan
     *
     * @return self
     */
    public function setPan($pan)
    {
        if (is_null($pan)) {
            throw new \InvalidArgumentException('non-nullable pan cannot be null');
        }
        $this->container['pan'] = $pan;

        return $this;
    }

    /**
     * Gets passport_number
     *
     * @return string|null
     */
    public function getPassportNumber()
    {
        return $this->container['passport_number'];
    }

    /**
     * Sets passport_number
     *
     * @param string|null $passport_number passport_number
     *
     * @return self
     */
    public function setPassportNumber($passport_number)
    {
        if (is_null($passport_number)) {
            throw new \InvalidArgumentException('non-nullable passport_number cannot be null');
        }
        $this->container['passport_number'] = $passport_number;

        return $this;
    }

    /**
     * Gets driving_license
     *
     * @return string|null
     */
    public function getDrivingLicense()
    {
        return $this->container['driving_license'];
    }

    /**
     * Sets driving_license
     *
     * @param string|null $driving_license driving_license
     *
     * @return self
     */
    public function setDrivingLicense($driving_license)
    {
        if (is_null($driving_license)) {
            throw new \InvalidArgumentException('non-nullable driving_license cannot be null');
        }
        $this->container['driving_license'] = $driving_license;

        return $this;
    }

    /**
     * Gets voter_id
     *
     * @return string|null
     */
    public function getVoterId()
    {
        return $this->container['voter_id'];
    }

    /**
     * Sets voter_id
     *
     * @param string|null $voter_id voter_id
     *
     * @return self
     */
    public function setVoterId($voter_id)
    {
        if (is_null($voter_id)) {
            throw new \InvalidArgumentException('non-nullable voter_id cannot be null');
        }
        $this->container['voter_id'] = $voter_id;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


