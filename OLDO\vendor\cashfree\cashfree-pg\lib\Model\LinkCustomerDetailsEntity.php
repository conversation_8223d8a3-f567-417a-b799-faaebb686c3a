<?php
/**
 * LinkCustomerDetailsEntity
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * Cashfree Payment Gateway APIs
 *
 * Cashfree's Payment Gateway APIs provide developers with a streamlined pathway to integrate advanced payment processing capabilities into their applications, platforms and websites.
 *
 * The version of the OpenAPI document: 2023-08-01
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 7.0.0
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Cashfree\Model;

use \ArrayAccess;
use \Cashfree\ObjectSerializer;

/**
 * LinkCustomerDetailsEntity Class Doc Comment
 *
 * @category Class
 * @description Payment link customer entity
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<string, mixed>
 */
class LinkCustomerDetailsEntity implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'LinkCustomerDetailsEntity';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'customer_phone' => 'string',
        'customer_email' => 'string',
        'customer_name' => 'string',
        'customer_bank_account_number' => 'string',
        'customer_bank_ifsc' => 'string',
        'customer_bank_code' => 'int'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'customer_phone' => null,
        'customer_email' => null,
        'customer_name' => null,
        'customer_bank_account_number' => null,
        'customer_bank_ifsc' => null,
        'customer_bank_code' => null
    ];

    /**
      * Array of nullable properties. Used for (de)serialization
      *
      * @var boolean[]
      */
    protected static $openAPINullables = [
        'customer_phone' => false,
		'customer_email' => false,
		'customer_name' => false,
		'customer_bank_account_number' => false,
		'customer_bank_ifsc' => false,
		'customer_bank_code' => false
    ];

    /**
      * If a nullable field gets set to null, insert it here
      *
      * @var boolean[]
      */
    protected $openAPINullablesSetToNull = [];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of nullable properties
     *
     * @return array
     */
    protected static function openAPINullables(): array
    {
        return self::$openAPINullables;
    }

    /**
     * Array of nullable field names deliberately set to null
     *
     * @return boolean[]
     */
    private function getOpenAPINullablesSetToNull(): array
    {
        return $this->openAPINullablesSetToNull;
    }

    /**
     * Setter - Array of nullable field names deliberately set to null
     *
     * @param boolean[] $openAPINullablesSetToNull
     */
    private function setOpenAPINullablesSetToNull(array $openAPINullablesSetToNull): void
    {
        $this->openAPINullablesSetToNull = $openAPINullablesSetToNull;
    }

    /**
     * Checks if a property is nullable
     *
     * @param string $property
     * @return bool
     */
    public static function isNullable(string $property): bool
    {
        return self::openAPINullables()[$property] ?? false;
    }

    /**
     * Checks if a nullable property is set to null.
     *
     * @param string $property
     * @return bool
     */
    public function isNullableSetToNull(string $property): bool
    {
        return in_array($property, $this->getOpenAPINullablesSetToNull(), true);
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'customer_phone' => 'customer_phone',
        'customer_email' => 'customer_email',
        'customer_name' => 'customer_name',
        'customer_bank_account_number' => 'customer_bank_account_number',
        'customer_bank_ifsc' => 'customer_bank_ifsc',
        'customer_bank_code' => 'customer_bank_code'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'customer_phone' => 'setCustomerPhone',
        'customer_email' => 'setCustomerEmail',
        'customer_name' => 'setCustomerName',
        'customer_bank_account_number' => 'setCustomerBankAccountNumber',
        'customer_bank_ifsc' => 'setCustomerBankIfsc',
        'customer_bank_code' => 'setCustomerBankCode'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'customer_phone' => 'getCustomerPhone',
        'customer_email' => 'getCustomerEmail',
        'customer_name' => 'getCustomerName',
        'customer_bank_account_number' => 'getCustomerBankAccountNumber',
        'customer_bank_ifsc' => 'getCustomerBankIfsc',
        'customer_bank_code' => 'getCustomerBankCode'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }

    public const CUSTOMER_BANK_CODE_3003 = 3003;
    public const CUSTOMER_BANK_CODE_3005 = 3005;
    public const CUSTOMER_BANK_CODE_3006 = 3006;
    public const CUSTOMER_BANK_CODE_3010 = 3010;
    public const CUSTOMER_BANK_CODE_3012 = 3012;
    public const CUSTOMER_BANK_CODE_3016 = 3016;
    public const CUSTOMER_BANK_CODE_3019 = 3019;
    public const CUSTOMER_BANK_CODE_3020 = 3020;
    public const CUSTOMER_BANK_CODE_3021 = 3021;
    public const CUSTOMER_BANK_CODE_3022 = 3022;
    public const CUSTOMER_BANK_CODE_3023 = 3023;
    public const CUSTOMER_BANK_CODE_3024 = 3024;
    public const CUSTOMER_BANK_CODE_3026 = 3026;
    public const CUSTOMER_BANK_CODE_3027 = 3027;
    public const CUSTOMER_BANK_CODE_3028 = 3028;
    public const CUSTOMER_BANK_CODE_3029 = 3029;
    public const CUSTOMER_BANK_CODE_3030 = 3030;
    public const CUSTOMER_BANK_CODE_3031 = 3031;
    public const CUSTOMER_BANK_CODE_3032 = 3032;
    public const CUSTOMER_BANK_CODE_3033 = 3033;
    public const CUSTOMER_BANK_CODE_3038 = 3038;
    public const CUSTOMER_BANK_CODE_3039 = 3039;
    public const CUSTOMER_BANK_CODE_3040 = 3040;
    public const CUSTOMER_BANK_CODE_3042 = 3042;
    public const CUSTOMER_BANK_CODE_3044 = 3044;
    public const CUSTOMER_BANK_CODE_3054 = 3054;
    public const CUSTOMER_BANK_CODE_3055 = 3055;
    public const CUSTOMER_BANK_CODE_3058 = 3058;
    public const CUSTOMER_BANK_CODE_3086 = 3086;
    public const CUSTOMER_BANK_CODE_3087 = 3087;
    public const CUSTOMER_BANK_CODE_3088 = 3088;
    public const CUSTOMER_BANK_CODE_3089 = 3089;
    public const CUSTOMER_BANK_CODE_3090 = 3090;
    public const CUSTOMER_BANK_CODE_3091 = 3091;
    public const CUSTOMER_BANK_CODE_3092 = 3092;
    public const CUSTOMER_BANK_CODE_3098 = 3098;
    public const CUSTOMER_BANK_CODE_3115 = 3115;
    public const CUSTOMER_BANK_CODE_3117 = 3117;
    public const CUSTOMER_BANK_CODE_7001 = 7001;
    public const CUSTOMER_BANK_CODE_unknown_default_open_api = ********;

    /**
     * Gets allowable values of the enum
     *
     * @return string[]
     */
    public function getCustomerBankCodeAllowableValues()
    {
        return [
            self::CUSTOMER_BANK_CODE_3003,
            self::CUSTOMER_BANK_CODE_3005,
            self::CUSTOMER_BANK_CODE_3006,
            self::CUSTOMER_BANK_CODE_3010,
            self::CUSTOMER_BANK_CODE_3012,
            self::CUSTOMER_BANK_CODE_3016,
            self::CUSTOMER_BANK_CODE_3019,
            self::CUSTOMER_BANK_CODE_3020,
            self::CUSTOMER_BANK_CODE_3021,
            self::CUSTOMER_BANK_CODE_3022,
            self::CUSTOMER_BANK_CODE_3023,
            self::CUSTOMER_BANK_CODE_3024,
            self::CUSTOMER_BANK_CODE_3026,
            self::CUSTOMER_BANK_CODE_3027,
            self::CUSTOMER_BANK_CODE_3028,
            self::CUSTOMER_BANK_CODE_3029,
            self::CUSTOMER_BANK_CODE_3030,
            self::CUSTOMER_BANK_CODE_3031,
            self::CUSTOMER_BANK_CODE_3032,
            self::CUSTOMER_BANK_CODE_3033,
            self::CUSTOMER_BANK_CODE_3038,
            self::CUSTOMER_BANK_CODE_3039,
            self::CUSTOMER_BANK_CODE_3040,
            self::CUSTOMER_BANK_CODE_3042,
            self::CUSTOMER_BANK_CODE_3044,
            self::CUSTOMER_BANK_CODE_3054,
            self::CUSTOMER_BANK_CODE_3055,
            self::CUSTOMER_BANK_CODE_3058,
            self::CUSTOMER_BANK_CODE_3086,
            self::CUSTOMER_BANK_CODE_3087,
            self::CUSTOMER_BANK_CODE_3088,
            self::CUSTOMER_BANK_CODE_3089,
            self::CUSTOMER_BANK_CODE_3090,
            self::CUSTOMER_BANK_CODE_3091,
            self::CUSTOMER_BANK_CODE_3092,
            self::CUSTOMER_BANK_CODE_3098,
            self::CUSTOMER_BANK_CODE_3115,
            self::CUSTOMER_BANK_CODE_3117,
            self::CUSTOMER_BANK_CODE_7001,
            self::CUSTOMER_BANK_CODE_unknown_default_open_api,
        ];
    }

    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->setIfExists('customer_phone', $data ?? [], null);
        $this->setIfExists('customer_email', $data ?? [], null);
        $this->setIfExists('customer_name', $data ?? [], null);
        $this->setIfExists('customer_bank_account_number', $data ?? [], null);
        $this->setIfExists('customer_bank_ifsc', $data ?? [], null);
        $this->setIfExists('customer_bank_code', $data ?? [], null);
    }

    /**
    * Sets $this->container[$variableName] to the given data or to the given default Value; if $variableName
    * is nullable and its value is set to null in the $fields array, then mark it as "set to null" in the
    * $this->openAPINullablesSetToNull array
    *
    * @param string $variableName
    * @param array  $fields
    * @param mixed  $defaultValue
    */
    private function setIfExists(string $variableName, array $fields, $defaultValue): void
    {
        if (self::isNullable($variableName) && array_key_exists($variableName, $fields) && is_null($fields[$variableName])) {
            $this->openAPINullablesSetToNull[] = $variableName;
        }

        $this->container[$variableName] = $fields[$variableName] ?? $defaultValue;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if ($this->container['customer_phone'] === null) {
            $invalidProperties[] = "'customer_phone' can't be null";
        }
        $allowedValues = $this->getCustomerBankCodeAllowableValues();
        if (!is_null($this->container['customer_bank_code']) && !in_array($this->container['customer_bank_code'], $allowedValues, true)) {
            $invalidProperties[] = sprintf(
                "invalid value '%s' for 'customer_bank_code', must be one of '%s'",
                $this->container['customer_bank_code'],
                implode("', '", $allowedValues)
            );
        }

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets customer_phone
     *
     * @return string
     */
    public function getCustomerPhone()
    {
        return $this->container['customer_phone'];
    }

    /**
     * Sets customer_phone
     *
     * @param string $customer_phone Customer phone number
     *
     * @return self
     */
    public function setCustomerPhone($customer_phone)
    {
        if (is_null($customer_phone)) {
            throw new \InvalidArgumentException('non-nullable customer_phone cannot be null');
        }
        $this->container['customer_phone'] = $customer_phone;

        return $this;
    }

    /**
     * Gets customer_email
     *
     * @return string|null
     */
    public function getCustomerEmail()
    {
        return $this->container['customer_email'];
    }

    /**
     * Sets customer_email
     *
     * @param string|null $customer_email Customer email address
     *
     * @return self
     */
    public function setCustomerEmail($customer_email)
    {
        if (is_null($customer_email)) {
            throw new \InvalidArgumentException('non-nullable customer_email cannot be null');
        }
        $this->container['customer_email'] = $customer_email;

        return $this;
    }

    /**
     * Gets customer_name
     *
     * @return string|null
     */
    public function getCustomerName()
    {
        return $this->container['customer_name'];
    }

    /**
     * Sets customer_name
     *
     * @param string|null $customer_name Customer name
     *
     * @return self
     */
    public function setCustomerName($customer_name)
    {
        if (is_null($customer_name)) {
            throw new \InvalidArgumentException('non-nullable customer_name cannot be null');
        }
        $this->container['customer_name'] = $customer_name;

        return $this;
    }

    /**
     * Gets customer_bank_account_number
     *
     * @return string|null
     */
    public function getCustomerBankAccountNumber()
    {
        return $this->container['customer_bank_account_number'];
    }

    /**
     * Sets customer_bank_account_number
     *
     * @param string|null $customer_bank_account_number Customer Bank Account Number
     *
     * @return self
     */
    public function setCustomerBankAccountNumber($customer_bank_account_number)
    {
        if (is_null($customer_bank_account_number)) {
            throw new \InvalidArgumentException('non-nullable customer_bank_account_number cannot be null');
        }
        $this->container['customer_bank_account_number'] = $customer_bank_account_number;

        return $this;
    }

    /**
     * Gets customer_bank_ifsc
     *
     * @return string|null
     */
    public function getCustomerBankIfsc()
    {
        return $this->container['customer_bank_ifsc'];
    }

    /**
     * Sets customer_bank_ifsc
     *
     * @param string|null $customer_bank_ifsc Customer Bank Ifsc
     *
     * @return self
     */
    public function setCustomerBankIfsc($customer_bank_ifsc)
    {
        if (is_null($customer_bank_ifsc)) {
            throw new \InvalidArgumentException('non-nullable customer_bank_ifsc cannot be null');
        }
        $this->container['customer_bank_ifsc'] = $customer_bank_ifsc;

        return $this;
    }

    /**
     * Gets customer_bank_code
     *
     * @return int|null
     */
    public function getCustomerBankCode()
    {
        return $this->container['customer_bank_code'];
    }

    /**
     * Sets customer_bank_code
     *
     * @param int|null $customer_bank_code Customer Bank Code
     *
     * @return self
     */
    public function setCustomerBankCode($customer_bank_code)
    {
        if (is_null($customer_bank_code)) {
            throw new \InvalidArgumentException('non-nullable customer_bank_code cannot be null');
        }
        $allowedValues = $this->getCustomerBankCodeAllowableValues();
        if (!in_array($customer_bank_code, $allowedValues, true)) {
            throw new \InvalidArgumentException(
                sprintf(
                    "Invalid value '%s' for 'customer_bank_code', must be one of '%s'",
                    $customer_bank_code,
                    implode("', '", $allowedValues)
                )
            );
        }
        $this->container['customer_bank_code'] = $customer_bank_code;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


