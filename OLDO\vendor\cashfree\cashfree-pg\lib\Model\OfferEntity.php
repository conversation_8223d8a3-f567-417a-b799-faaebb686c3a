<?php
/**
 * OfferEntity
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * Cashfree Payment Gateway APIs
 *
 * Cashfree's Payment Gateway APIs provide developers with a streamlined pathway to integrate advanced payment processing capabilities into their applications, platforms and websites.
 *
 * The version of the OpenAPI document: 2023-08-01
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 7.0.0
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Cashfree\Model;

use \ArrayAccess;
use \Cashfree\ObjectSerializer;

/**
 * OfferEntity Class Doc Comment
 *
 * @category Class
 * @description Offer entity object
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<string, mixed>
 */
class OfferEntity implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'OfferEntity';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'offer_id' => 'string',
        'offer_status' => 'string',
        'offer_meta' => '\Cashfree\Model\OfferMeta',
        'offer_tnc' => '\Cashfree\Model\OfferTnc',
        'offer_details' => '\Cashfree\Model\OfferDetails',
        'offer_validations' => '\Cashfree\Model\OfferValidations'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'offer_id' => null,
        'offer_status' => null,
        'offer_meta' => null,
        'offer_tnc' => null,
        'offer_details' => null,
        'offer_validations' => null
    ];

    /**
      * Array of nullable properties. Used for (de)serialization
      *
      * @var boolean[]
      */
    protected static $openAPINullables = [
        'offer_id' => false,
		'offer_status' => false,
		'offer_meta' => false,
		'offer_tnc' => false,
		'offer_details' => false,
		'offer_validations' => false
    ];

    /**
      * If a nullable field gets set to null, insert it here
      *
      * @var boolean[]
      */
    protected $openAPINullablesSetToNull = [];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of nullable properties
     *
     * @return array
     */
    protected static function openAPINullables(): array
    {
        return self::$openAPINullables;
    }

    /**
     * Array of nullable field names deliberately set to null
     *
     * @return boolean[]
     */
    private function getOpenAPINullablesSetToNull(): array
    {
        return $this->openAPINullablesSetToNull;
    }

    /**
     * Setter - Array of nullable field names deliberately set to null
     *
     * @param boolean[] $openAPINullablesSetToNull
     */
    private function setOpenAPINullablesSetToNull(array $openAPINullablesSetToNull): void
    {
        $this->openAPINullablesSetToNull = $openAPINullablesSetToNull;
    }

    /**
     * Checks if a property is nullable
     *
     * @param string $property
     * @return bool
     */
    public static function isNullable(string $property): bool
    {
        return self::openAPINullables()[$property] ?? false;
    }

    /**
     * Checks if a nullable property is set to null.
     *
     * @param string $property
     * @return bool
     */
    public function isNullableSetToNull(string $property): bool
    {
        return in_array($property, $this->getOpenAPINullablesSetToNull(), true);
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'offer_id' => 'offer_id',
        'offer_status' => 'offer_status',
        'offer_meta' => 'offer_meta',
        'offer_tnc' => 'offer_tnc',
        'offer_details' => 'offer_details',
        'offer_validations' => 'offer_validations'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'offer_id' => 'setOfferId',
        'offer_status' => 'setOfferStatus',
        'offer_meta' => 'setOfferMeta',
        'offer_tnc' => 'setOfferTnc',
        'offer_details' => 'setOfferDetails',
        'offer_validations' => 'setOfferValidations'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'offer_id' => 'getOfferId',
        'offer_status' => 'getOfferStatus',
        'offer_meta' => 'getOfferMeta',
        'offer_tnc' => 'getOfferTnc',
        'offer_details' => 'getOfferDetails',
        'offer_validations' => 'getOfferValidations'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->setIfExists('offer_id', $data ?? [], null);
        $this->setIfExists('offer_status', $data ?? [], null);
        $this->setIfExists('offer_meta', $data ?? [], null);
        $this->setIfExists('offer_tnc', $data ?? [], null);
        $this->setIfExists('offer_details', $data ?? [], null);
        $this->setIfExists('offer_validations', $data ?? [], null);
    }

    /**
    * Sets $this->container[$variableName] to the given data or to the given default Value; if $variableName
    * is nullable and its value is set to null in the $fields array, then mark it as "set to null" in the
    * $this->openAPINullablesSetToNull array
    *
    * @param string $variableName
    * @param array  $fields
    * @param mixed  $defaultValue
    */
    private function setIfExists(string $variableName, array $fields, $defaultValue): void
    {
        if (self::isNullable($variableName) && array_key_exists($variableName, $fields) && is_null($fields[$variableName])) {
            $this->openAPINullablesSetToNull[] = $variableName;
        }

        $this->container[$variableName] = $fields[$variableName] ?? $defaultValue;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets offer_id
     *
     * @return string|null
     */
    public function getOfferId()
    {
        return $this->container['offer_id'];
    }

    /**
     * Sets offer_id
     *
     * @param string|null $offer_id offer_id
     *
     * @return self
     */
    public function setOfferId($offer_id)
    {
        if (is_null($offer_id)) {
            throw new \InvalidArgumentException('non-nullable offer_id cannot be null');
        }
        $this->container['offer_id'] = $offer_id;

        return $this;
    }

    /**
     * Gets offer_status
     *
     * @return string|null
     */
    public function getOfferStatus()
    {
        return $this->container['offer_status'];
    }

    /**
     * Sets offer_status
     *
     * @param string|null $offer_status offer_status
     *
     * @return self
     */
    public function setOfferStatus($offer_status)
    {
        if (is_null($offer_status)) {
            throw new \InvalidArgumentException('non-nullable offer_status cannot be null');
        }
        $this->container['offer_status'] = $offer_status;

        return $this;
    }

    /**
     * Gets offer_meta
     *
     * @return \Cashfree\Model\OfferMeta|null
     */
    public function getOfferMeta()
    {
        return $this->container['offer_meta'];
    }

    /**
     * Sets offer_meta
     *
     * @param \Cashfree\Model\OfferMeta|null $offer_meta offer_meta
     *
     * @return self
     */
    public function setOfferMeta($offer_meta)
    {
        if (is_null($offer_meta)) {
            throw new \InvalidArgumentException('non-nullable offer_meta cannot be null');
        }
        $this->container['offer_meta'] = $offer_meta;

        return $this;
    }

    /**
     * Gets offer_tnc
     *
     * @return \Cashfree\Model\OfferTnc|null
     */
    public function getOfferTnc()
    {
        return $this->container['offer_tnc'];
    }

    /**
     * Sets offer_tnc
     *
     * @param \Cashfree\Model\OfferTnc|null $offer_tnc offer_tnc
     *
     * @return self
     */
    public function setOfferTnc($offer_tnc)
    {
        if (is_null($offer_tnc)) {
            throw new \InvalidArgumentException('non-nullable offer_tnc cannot be null');
        }
        $this->container['offer_tnc'] = $offer_tnc;

        return $this;
    }

    /**
     * Gets offer_details
     *
     * @return \Cashfree\Model\OfferDetails|null
     */
    public function getOfferDetails()
    {
        return $this->container['offer_details'];
    }

    /**
     * Sets offer_details
     *
     * @param \Cashfree\Model\OfferDetails|null $offer_details offer_details
     *
     * @return self
     */
    public function setOfferDetails($offer_details)
    {
        if (is_null($offer_details)) {
            throw new \InvalidArgumentException('non-nullable offer_details cannot be null');
        }
        $this->container['offer_details'] = $offer_details;

        return $this;
    }

    /**
     * Gets offer_validations
     *
     * @return \Cashfree\Model\OfferValidations|null
     */
    public function getOfferValidations()
    {
        return $this->container['offer_validations'];
    }

    /**
     * Sets offer_validations
     *
     * @param \Cashfree\Model\OfferValidations|null $offer_validations offer_validations
     *
     * @return self
     */
    public function setOfferValidations($offer_validations)
    {
        if (is_null($offer_validations)) {
            throw new \InvalidArgumentException('non-nullable offer_validations cannot be null');
        }
        $this->container['offer_validations'] = $offer_validations;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


