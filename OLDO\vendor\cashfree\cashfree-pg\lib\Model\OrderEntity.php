<?php
/**
 * OrderEntity
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * Cashfree Payment Gateway APIs
 *
 * Cashfree's Payment Gateway APIs provide developers with a streamlined pathway to integrate advanced payment processing capabilities into their applications, platforms and websites.
 *
 * The version of the OpenAPI document: 2023-08-01
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 7.0.0
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Cashfree\Model;

use \ArrayAccess;
use \Cashfree\ObjectSerializer;

/**
 * OrderEntity Class Doc Comment
 *
 * @category Class
 * @description The complete order entity
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<string, mixed>
 */
class OrderEntity implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'OrderEntity';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'cf_order_id' => 'string',
        'order_id' => 'string',
        'entity' => 'string',
        'order_currency' => 'string',
        'order_amount' => 'float',
        'order_status' => 'string',
        'payment_session_id' => 'string',
        'order_expiry_time' => '\DateTime',
        'order_note' => 'string',
        'created_at' => '\DateTime',
        'order_splits' => '\Cashfree\Model\VendorSplit[]',
        'customer_details' => '\Cashfree\Model\CustomerDetailsResponse',
        'order_meta' => '\Cashfree\Model\OrderMeta',
        'order_tags' => 'array<string,string>'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'cf_order_id' => null,
        'order_id' => null,
        'entity' => null,
        'order_currency' => null,
        'order_amount' => null,
        'order_status' => null,
        'payment_session_id' => null,
        'order_expiry_time' => 'date-time',
        'order_note' => null,
        'created_at' => 'date-time',
        'order_splits' => null,
        'customer_details' => null,
        'order_meta' => null,
        'order_tags' => null
    ];

    /**
      * Array of nullable properties. Used for (de)serialization
      *
      * @var boolean[]
      */
    protected static $openAPINullables = [
        'cf_order_id' => false,
		'order_id' => false,
		'entity' => false,
		'order_currency' => false,
		'order_amount' => false,
		'order_status' => false,
		'payment_session_id' => false,
		'order_expiry_time' => false,
		'order_note' => false,
		'created_at' => false,
		'order_splits' => false,
		'customer_details' => false,
		'order_meta' => false,
		'order_tags' => false
    ];

    /**
      * If a nullable field gets set to null, insert it here
      *
      * @var boolean[]
      */
    protected $openAPINullablesSetToNull = [];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of nullable properties
     *
     * @return array
     */
    protected static function openAPINullables(): array
    {
        return self::$openAPINullables;
    }

    /**
     * Array of nullable field names deliberately set to null
     *
     * @return boolean[]
     */
    private function getOpenAPINullablesSetToNull(): array
    {
        return $this->openAPINullablesSetToNull;
    }

    /**
     * Setter - Array of nullable field names deliberately set to null
     *
     * @param boolean[] $openAPINullablesSetToNull
     */
    private function setOpenAPINullablesSetToNull(array $openAPINullablesSetToNull): void
    {
        $this->openAPINullablesSetToNull = $openAPINullablesSetToNull;
    }

    /**
     * Checks if a property is nullable
     *
     * @param string $property
     * @return bool
     */
    public static function isNullable(string $property): bool
    {
        return self::openAPINullables()[$property] ?? false;
    }

    /**
     * Checks if a nullable property is set to null.
     *
     * @param string $property
     * @return bool
     */
    public function isNullableSetToNull(string $property): bool
    {
        return in_array($property, $this->getOpenAPINullablesSetToNull(), true);
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'cf_order_id' => 'cf_order_id',
        'order_id' => 'order_id',
        'entity' => 'entity',
        'order_currency' => 'order_currency',
        'order_amount' => 'order_amount',
        'order_status' => 'order_status',
        'payment_session_id' => 'payment_session_id',
        'order_expiry_time' => 'order_expiry_time',
        'order_note' => 'order_note',
        'created_at' => 'created_at',
        'order_splits' => 'order_splits',
        'customer_details' => 'customer_details',
        'order_meta' => 'order_meta',
        'order_tags' => 'order_tags'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'cf_order_id' => 'setCfOrderId',
        'order_id' => 'setOrderId',
        'entity' => 'setEntity',
        'order_currency' => 'setOrderCurrency',
        'order_amount' => 'setOrderAmount',
        'order_status' => 'setOrderStatus',
        'payment_session_id' => 'setPaymentSessionId',
        'order_expiry_time' => 'setOrderExpiryTime',
        'order_note' => 'setOrderNote',
        'created_at' => 'setCreatedAt',
        'order_splits' => 'setOrderSplits',
        'customer_details' => 'setCustomerDetails',
        'order_meta' => 'setOrderMeta',
        'order_tags' => 'setOrderTags'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'cf_order_id' => 'getCfOrderId',
        'order_id' => 'getOrderId',
        'entity' => 'getEntity',
        'order_currency' => 'getOrderCurrency',
        'order_amount' => 'getOrderAmount',
        'order_status' => 'getOrderStatus',
        'payment_session_id' => 'getPaymentSessionId',
        'order_expiry_time' => 'getOrderExpiryTime',
        'order_note' => 'getOrderNote',
        'created_at' => 'getCreatedAt',
        'order_splits' => 'getOrderSplits',
        'customer_details' => 'getCustomerDetails',
        'order_meta' => 'getOrderMeta',
        'order_tags' => 'getOrderTags'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->setIfExists('cf_order_id', $data ?? [], null);
        $this->setIfExists('order_id', $data ?? [], null);
        $this->setIfExists('entity', $data ?? [], null);
        $this->setIfExists('order_currency', $data ?? [], null);
        $this->setIfExists('order_amount', $data ?? [], null);
        $this->setIfExists('order_status', $data ?? [], null);
        $this->setIfExists('payment_session_id', $data ?? [], null);
        $this->setIfExists('order_expiry_time', $data ?? [], null);
        $this->setIfExists('order_note', $data ?? [], null);
        $this->setIfExists('created_at', $data ?? [], null);
        $this->setIfExists('order_splits', $data ?? [], null);
        $this->setIfExists('customer_details', $data ?? [], null);
        $this->setIfExists('order_meta', $data ?? [], null);
        $this->setIfExists('order_tags', $data ?? [], null);
    }

    /**
    * Sets $this->container[$variableName] to the given data or to the given default Value; if $variableName
    * is nullable and its value is set to null in the $fields array, then mark it as "set to null" in the
    * $this->openAPINullablesSetToNull array
    *
    * @param string $variableName
    * @param array  $fields
    * @param mixed  $defaultValue
    */
    private function setIfExists(string $variableName, array $fields, $defaultValue): void
    {
        if (self::isNullable($variableName) && array_key_exists($variableName, $fields) && is_null($fields[$variableName])) {
            $this->openAPINullablesSetToNull[] = $variableName;
        }

        $this->container[$variableName] = $fields[$variableName] ?? $defaultValue;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if (!is_null($this->container['order_tags']) && (count($this->container['order_tags']) > 15)) {
            $invalidProperties[] = "invalid value for 'order_tags', number of items must be less than or equal to 15.";
        }

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets cf_order_id
     *
     * @return string|null
     */
    public function getCfOrderId()
    {
        return $this->container['cf_order_id'];
    }

    /**
     * Sets cf_order_id
     *
     * @param string|null $cf_order_id unique id generated by cashfree for your order
     *
     * @return self
     */
    public function setCfOrderId($cf_order_id)
    {
        if (is_null($cf_order_id)) {
            throw new \InvalidArgumentException('non-nullable cf_order_id cannot be null');
        }
        $this->container['cf_order_id'] = $cf_order_id;

        return $this;
    }

    /**
     * Gets order_id
     *
     * @return string|null
     */
    public function getOrderId()
    {
        return $this->container['order_id'];
    }

    /**
     * Sets order_id
     *
     * @param string|null $order_id order_id sent during the api request
     *
     * @return self
     */
    public function setOrderId($order_id)
    {
        if (is_null($order_id)) {
            throw new \InvalidArgumentException('non-nullable order_id cannot be null');
        }
        $this->container['order_id'] = $order_id;

        return $this;
    }

    /**
     * Gets entity
     *
     * @return string|null
     */
    public function getEntity()
    {
        return $this->container['entity'];
    }

    /**
     * Sets entity
     *
     * @param string|null $entity Type of the entity.
     *
     * @return self
     */
    public function setEntity($entity)
    {
        if (is_null($entity)) {
            throw new \InvalidArgumentException('non-nullable entity cannot be null');
        }
        $this->container['entity'] = $entity;

        return $this;
    }

    /**
     * Gets order_currency
     *
     * @return string|null
     */
    public function getOrderCurrency()
    {
        return $this->container['order_currency'];
    }

    /**
     * Sets order_currency
     *
     * @param string|null $order_currency Currency of the order. Example INR
     *
     * @return self
     */
    public function setOrderCurrency($order_currency)
    {
        if (is_null($order_currency)) {
            throw new \InvalidArgumentException('non-nullable order_currency cannot be null');
        }
        $this->container['order_currency'] = $order_currency;

        return $this;
    }

    /**
     * Gets order_amount
     *
     * @return float|null
     */
    public function getOrderAmount()
    {
        return $this->container['order_amount'];
    }

    /**
     * Sets order_amount
     *
     * @param float|null $order_amount order_amount
     *
     * @return self
     */
    public function setOrderAmount($order_amount)
    {
        if (is_null($order_amount)) {
            throw new \InvalidArgumentException('non-nullable order_amount cannot be null');
        }
        $this->container['order_amount'] = $order_amount;

        return $this;
    }

    /**
     * Gets order_status
     *
     * @return string|null
     */
    public function getOrderStatus()
    {
        return $this->container['order_status'];
    }

    /**
     * Sets order_status
     *
     * @param string|null $order_status Possible values are  - `ACTIVE`: Order does not have a sucessful transaction yet - `PAID`: Order is PAID with one successful transaction - `EXPIRED`: Order was not PAID and not it has expired. No transaction can be initiated for an EXPIRED order. `TERMINATED`: Order terminated `TERMINATION_REQUESTED`: Order termination requested
     *
     * @return self
     */
    public function setOrderStatus($order_status)
    {
        if (is_null($order_status)) {
            throw new \InvalidArgumentException('non-nullable order_status cannot be null');
        }
        $this->container['order_status'] = $order_status;

        return $this;
    }

    /**
     * Gets payment_session_id
     *
     * @return string|null
     */
    public function getPaymentSessionId()
    {
        return $this->container['payment_session_id'];
    }

    /**
     * Sets payment_session_id
     *
     * @param string|null $payment_session_id payment_session_id
     *
     * @return self
     */
    public function setPaymentSessionId($payment_session_id)
    {
        if (is_null($payment_session_id)) {
            throw new \InvalidArgumentException('non-nullable payment_session_id cannot be null');
        }
        $this->container['payment_session_id'] = $payment_session_id;

        return $this;
    }

    /**
     * Gets order_expiry_time
     *
     * @return \DateTime|null
     */
    public function getOrderExpiryTime()
    {
        return $this->container['order_expiry_time'];
    }

    /**
     * Sets order_expiry_time
     *
     * @param \DateTime|null $order_expiry_time order_expiry_time
     *
     * @return self
     */
    public function setOrderExpiryTime($order_expiry_time)
    {
        if (is_null($order_expiry_time)) {
            throw new \InvalidArgumentException('non-nullable order_expiry_time cannot be null');
        }
        $this->container['order_expiry_time'] = $order_expiry_time;

        return $this;
    }

    /**
     * Gets order_note
     *
     * @return string|null
     */
    public function getOrderNote()
    {
        return $this->container['order_note'];
    }

    /**
     * Sets order_note
     *
     * @param string|null $order_note Additional note for order
     *
     * @return self
     */
    public function setOrderNote($order_note)
    {
        if (is_null($order_note)) {
            throw new \InvalidArgumentException('non-nullable order_note cannot be null');
        }
        $this->container['order_note'] = $order_note;

        return $this;
    }

    /**
     * Gets created_at
     *
     * @return \DateTime|null
     */
    public function getCreatedAt()
    {
        return $this->container['created_at'];
    }

    /**
     * Sets created_at
     *
     * @param \DateTime|null $created_at When the order was created at cashfree's server
     *
     * @return self
     */
    public function setCreatedAt($created_at)
    {
        if (is_null($created_at)) {
            throw new \InvalidArgumentException('non-nullable created_at cannot be null');
        }
        $this->container['created_at'] = $created_at;

        return $this;
    }

    /**
     * Gets order_splits
     *
     * @return \Cashfree\Model\VendorSplit[]|null
     */
    public function getOrderSplits()
    {
        return $this->container['order_splits'];
    }

    /**
     * Sets order_splits
     *
     * @param \Cashfree\Model\VendorSplit[]|null $order_splits order_splits
     *
     * @return self
     */
    public function setOrderSplits($order_splits)
    {
        if (is_null($order_splits)) {
            throw new \InvalidArgumentException('non-nullable order_splits cannot be null');
        }
        $this->container['order_splits'] = $order_splits;

        return $this;
    }

    /**
     * Gets customer_details
     *
     * @return \Cashfree\Model\CustomerDetailsResponse|null
     */
    public function getCustomerDetails()
    {
        return $this->container['customer_details'];
    }

    /**
     * Sets customer_details
     *
     * @param \Cashfree\Model\CustomerDetailsResponse|null $customer_details customer_details
     *
     * @return self
     */
    public function setCustomerDetails($customer_details)
    {
        if (is_null($customer_details)) {
            throw new \InvalidArgumentException('non-nullable customer_details cannot be null');
        }
        $this->container['customer_details'] = $customer_details;

        return $this;
    }

    /**
     * Gets order_meta
     *
     * @return \Cashfree\Model\OrderMeta|null
     */
    public function getOrderMeta()
    {
        return $this->container['order_meta'];
    }

    /**
     * Sets order_meta
     *
     * @param \Cashfree\Model\OrderMeta|null $order_meta order_meta
     *
     * @return self
     */
    public function setOrderMeta($order_meta)
    {
        if (is_null($order_meta)) {
            throw new \InvalidArgumentException('non-nullable order_meta cannot be null');
        }
        $this->container['order_meta'] = $order_meta;

        return $this;
    }

    /**
     * Gets order_tags
     *
     * @return array<string,string>|null
     */
    public function getOrderTags()
    {
        return $this->container['order_tags'];
    }

    /**
     * Sets order_tags
     *
     * @param array<string,string>|null $order_tags Custom Tags in thr form of {\"key\":\"value\"} which can be passed for an order. A maximum of 10 tags can be added
     *
     * @return self
     */
    public function setOrderTags($order_tags)
    {
        if (is_null($order_tags)) {
            throw new \InvalidArgumentException('non-nullable order_tags cannot be null');
        }

        if ((count($order_tags) > 15)) {
            throw new \InvalidArgumentException('invalid value for $order_tags when calling OrderEntity., number of items must be less than or equal to 15.');
        }
        $this->container['order_tags'] = $order_tags;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


