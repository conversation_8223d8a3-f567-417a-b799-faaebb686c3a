<?php
/**
 * PaymentEntity
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * Cashfree Payment Gateway APIs
 *
 * Cashfree's Payment Gateway APIs provide developers with a streamlined pathway to integrate advanced payment processing capabilities into their applications, platforms and websites.
 *
 * The version of the OpenAPI document: 2023-08-01
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 7.0.0
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Cashfree\Model;

use \ArrayAccess;
use \Cashfree\ObjectSerializer;

/**
 * PaymentEntity Class Doc Comment
 *
 * @category Class
 * @description payment entity full object
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<string, mixed>
 */
class PaymentEntity implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'PaymentEntity';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'cf_payment_id' => 'string',
        'order_id' => 'string',
        'entity' => 'string',
        'error_details' => '\Cashfree\Model\ErrorDetailsInPaymentsEntity',
        'is_captured' => 'bool',
        'order_amount' => 'float',
        'payment_group' => 'string',
        'payment_currency' => 'string',
        'payment_amount' => 'float',
        'payment_time' => 'string',
        'payment_completion_time' => 'string',
        'payment_status' => 'string',
        'payment_message' => 'string',
        'bank_reference' => 'string',
        'auth_id' => 'string',
        'authorization' => '\Cashfree\Model\AuthorizationInPaymentsEntity',
        'payment_method' => '\Cashfree\Model\PaymentEntityPaymentMethod'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'cf_payment_id' => null,
        'order_id' => null,
        'entity' => null,
        'error_details' => null,
        'is_captured' => null,
        'order_amount' => null,
        'payment_group' => null,
        'payment_currency' => null,
        'payment_amount' => null,
        'payment_time' => null,
        'payment_completion_time' => null,
        'payment_status' => null,
        'payment_message' => null,
        'bank_reference' => null,
        'auth_id' => null,
        'authorization' => null,
        'payment_method' => null
    ];

    /**
      * Array of nullable properties. Used for (de)serialization
      *
      * @var boolean[]
      */
    protected static $openAPINullables = [
        'cf_payment_id' => false,
		'order_id' => false,
		'entity' => false,
		'error_details' => false,
		'is_captured' => false,
		'order_amount' => false,
		'payment_group' => false,
		'payment_currency' => false,
		'payment_amount' => false,
		'payment_time' => false,
		'payment_completion_time' => false,
		'payment_status' => false,
		'payment_message' => false,
		'bank_reference' => false,
		'auth_id' => false,
		'authorization' => false,
		'payment_method' => false
    ];

    /**
      * If a nullable field gets set to null, insert it here
      *
      * @var boolean[]
      */
    protected $openAPINullablesSetToNull = [];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of nullable properties
     *
     * @return array
     */
    protected static function openAPINullables(): array
    {
        return self::$openAPINullables;
    }

    /**
     * Array of nullable field names deliberately set to null
     *
     * @return boolean[]
     */
    private function getOpenAPINullablesSetToNull(): array
    {
        return $this->openAPINullablesSetToNull;
    }

    /**
     * Setter - Array of nullable field names deliberately set to null
     *
     * @param boolean[] $openAPINullablesSetToNull
     */
    private function setOpenAPINullablesSetToNull(array $openAPINullablesSetToNull): void
    {
        $this->openAPINullablesSetToNull = $openAPINullablesSetToNull;
    }

    /**
     * Checks if a property is nullable
     *
     * @param string $property
     * @return bool
     */
    public static function isNullable(string $property): bool
    {
        return self::openAPINullables()[$property] ?? false;
    }

    /**
     * Checks if a nullable property is set to null.
     *
     * @param string $property
     * @return bool
     */
    public function isNullableSetToNull(string $property): bool
    {
        return in_array($property, $this->getOpenAPINullablesSetToNull(), true);
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'cf_payment_id' => 'cf_payment_id',
        'order_id' => 'order_id',
        'entity' => 'entity',
        'error_details' => 'error_details',
        'is_captured' => 'is_captured',
        'order_amount' => 'order_amount',
        'payment_group' => 'payment_group',
        'payment_currency' => 'payment_currency',
        'payment_amount' => 'payment_amount',
        'payment_time' => 'payment_time',
        'payment_completion_time' => 'payment_completion_time',
        'payment_status' => 'payment_status',
        'payment_message' => 'payment_message',
        'bank_reference' => 'bank_reference',
        'auth_id' => 'auth_id',
        'authorization' => 'authorization',
        'payment_method' => 'payment_method'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'cf_payment_id' => 'setCfPaymentId',
        'order_id' => 'setOrderId',
        'entity' => 'setEntity',
        'error_details' => 'setErrorDetails',
        'is_captured' => 'setIsCaptured',
        'order_amount' => 'setOrderAmount',
        'payment_group' => 'setPaymentGroup',
        'payment_currency' => 'setPaymentCurrency',
        'payment_amount' => 'setPaymentAmount',
        'payment_time' => 'setPaymentTime',
        'payment_completion_time' => 'setPaymentCompletionTime',
        'payment_status' => 'setPaymentStatus',
        'payment_message' => 'setPaymentMessage',
        'bank_reference' => 'setBankReference',
        'auth_id' => 'setAuthId',
        'authorization' => 'setAuthorization',
        'payment_method' => 'setPaymentMethod'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'cf_payment_id' => 'getCfPaymentId',
        'order_id' => 'getOrderId',
        'entity' => 'getEntity',
        'error_details' => 'getErrorDetails',
        'is_captured' => 'getIsCaptured',
        'order_amount' => 'getOrderAmount',
        'payment_group' => 'getPaymentGroup',
        'payment_currency' => 'getPaymentCurrency',
        'payment_amount' => 'getPaymentAmount',
        'payment_time' => 'getPaymentTime',
        'payment_completion_time' => 'getPaymentCompletionTime',
        'payment_status' => 'getPaymentStatus',
        'payment_message' => 'getPaymentMessage',
        'bank_reference' => 'getBankReference',
        'auth_id' => 'getAuthId',
        'authorization' => 'getAuthorization',
        'payment_method' => 'getPaymentMethod'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }

    public const PAYMENT_STATUS_SUCCESS = 'SUCCESS';
    public const PAYMENT_STATUS_NOT_ATTEMPTED = 'NOT_ATTEMPTED';
    public const PAYMENT_STATUS_FAILED = 'FAILED';
    public const PAYMENT_STATUS_USER_DROPPED = 'USER_DROPPED';
    public const PAYMENT_STATUS_VOID = 'VOID';
    public const PAYMENT_STATUS_CANCELLED = 'CANCELLED';
    public const PAYMENT_STATUS_PENDING = 'PENDING';
    public const PAYMENT_STATUS_UNKNOWN_DEFAULT_OPEN_API = 'unknown_default_open_api';

    /**
     * Gets allowable values of the enum
     *
     * @return string[]
     */
    public function getPaymentStatusAllowableValues()
    {
        return [
            self::PAYMENT_STATUS_SUCCESS,
            self::PAYMENT_STATUS_NOT_ATTEMPTED,
            self::PAYMENT_STATUS_FAILED,
            self::PAYMENT_STATUS_USER_DROPPED,
            self::PAYMENT_STATUS_VOID,
            self::PAYMENT_STATUS_CANCELLED,
            self::PAYMENT_STATUS_PENDING,
            self::PAYMENT_STATUS_UNKNOWN_DEFAULT_OPEN_API,
        ];
    }

    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->setIfExists('cf_payment_id', $data ?? [], null);
        $this->setIfExists('order_id', $data ?? [], null);
        $this->setIfExists('entity', $data ?? [], null);
        $this->setIfExists('error_details', $data ?? [], null);
        $this->setIfExists('is_captured', $data ?? [], null);
        $this->setIfExists('order_amount', $data ?? [], null);
        $this->setIfExists('payment_group', $data ?? [], null);
        $this->setIfExists('payment_currency', $data ?? [], null);
        $this->setIfExists('payment_amount', $data ?? [], null);
        $this->setIfExists('payment_time', $data ?? [], null);
        $this->setIfExists('payment_completion_time', $data ?? [], null);
        $this->setIfExists('payment_status', $data ?? [], null);
        $this->setIfExists('payment_message', $data ?? [], null);
        $this->setIfExists('bank_reference', $data ?? [], null);
        $this->setIfExists('auth_id', $data ?? [], null);
        $this->setIfExists('authorization', $data ?? [], null);
        $this->setIfExists('payment_method', $data ?? [], null);
    }

    /**
    * Sets $this->container[$variableName] to the given data or to the given default Value; if $variableName
    * is nullable and its value is set to null in the $fields array, then mark it as "set to null" in the
    * $this->openAPINullablesSetToNull array
    *
    * @param string $variableName
    * @param array  $fields
    * @param mixed  $defaultValue
    */
    private function setIfExists(string $variableName, array $fields, $defaultValue): void
    {
        if (self::isNullable($variableName) && array_key_exists($variableName, $fields) && is_null($fields[$variableName])) {
            $this->openAPINullablesSetToNull[] = $variableName;
        }

        $this->container[$variableName] = $fields[$variableName] ?? $defaultValue;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        $allowedValues = $this->getPaymentStatusAllowableValues();
        if (!is_null($this->container['payment_status']) && !in_array($this->container['payment_status'], $allowedValues, true)) {
            $invalidProperties[] = sprintf(
                "invalid value '%s' for 'payment_status', must be one of '%s'",
                $this->container['payment_status'],
                implode("', '", $allowedValues)
            );
        }

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets cf_payment_id
     *
     * @return string|null
     */
    public function getCfPaymentId()
    {
        return $this->container['cf_payment_id'];
    }

    /**
     * Sets cf_payment_id
     *
     * @param string|null $cf_payment_id cf_payment_id
     *
     * @return self
     */
    public function setCfPaymentId($cf_payment_id)
    {
        if (is_null($cf_payment_id)) {
            throw new \InvalidArgumentException('non-nullable cf_payment_id cannot be null');
        }
        $this->container['cf_payment_id'] = $cf_payment_id;

        return $this;
    }

    /**
     * Gets order_id
     *
     * @return string|null
     */
    public function getOrderId()
    {
        return $this->container['order_id'];
    }

    /**
     * Sets order_id
     *
     * @param string|null $order_id order_id
     *
     * @return self
     */
    public function setOrderId($order_id)
    {
        if (is_null($order_id)) {
            throw new \InvalidArgumentException('non-nullable order_id cannot be null');
        }
        $this->container['order_id'] = $order_id;

        return $this;
    }

    /**
     * Gets entity
     *
     * @return string|null
     */
    public function getEntity()
    {
        return $this->container['entity'];
    }

    /**
     * Sets entity
     *
     * @param string|null $entity entity
     *
     * @return self
     */
    public function setEntity($entity)
    {
        if (is_null($entity)) {
            throw new \InvalidArgumentException('non-nullable entity cannot be null');
        }
        $this->container['entity'] = $entity;

        return $this;
    }

    /**
     * Gets error_details
     *
     * @return \Cashfree\Model\ErrorDetailsInPaymentsEntity|null
     */
    public function getErrorDetails()
    {
        return $this->container['error_details'];
    }

    /**
     * Sets error_details
     *
     * @param \Cashfree\Model\ErrorDetailsInPaymentsEntity|null $error_details error_details
     *
     * @return self
     */
    public function setErrorDetails($error_details)
    {
        if (is_null($error_details)) {
            throw new \InvalidArgumentException('non-nullable error_details cannot be null');
        }
        $this->container['error_details'] = $error_details;

        return $this;
    }

    /**
     * Gets is_captured
     *
     * @return bool|null
     */
    public function getIsCaptured()
    {
        return $this->container['is_captured'];
    }

    /**
     * Sets is_captured
     *
     * @param bool|null $is_captured is_captured
     *
     * @return self
     */
    public function setIsCaptured($is_captured)
    {
        if (is_null($is_captured)) {
            throw new \InvalidArgumentException('non-nullable is_captured cannot be null');
        }
        $this->container['is_captured'] = $is_captured;

        return $this;
    }

    /**
     * Gets order_amount
     *
     * @return float|null
     */
    public function getOrderAmount()
    {
        return $this->container['order_amount'];
    }

    /**
     * Sets order_amount
     *
     * @param float|null $order_amount Order amount can be different from payment amount if you collect service fee from the customer
     *
     * @return self
     */
    public function setOrderAmount($order_amount)
    {
        if (is_null($order_amount)) {
            throw new \InvalidArgumentException('non-nullable order_amount cannot be null');
        }
        $this->container['order_amount'] = $order_amount;

        return $this;
    }

    /**
     * Gets payment_group
     *
     * @return string|null
     */
    public function getPaymentGroup()
    {
        return $this->container['payment_group'];
    }

    /**
     * Sets payment_group
     *
     * @param string|null $payment_group Type of payment group. One of ['prepaid_card', 'upi_ppi_offline', 'cash', 'upi_credit_card', 'paypal', 'net_banking', 'cardless_emi', 'credit_card', 'bank_transfer', 'pay_later', 'debit_card_emi', 'debit_card', 'wallet', 'upi_ppi', 'upi', 'credit_card_emi']
     *
     * @return self
     */
    public function setPaymentGroup($payment_group)
    {
        if (is_null($payment_group)) {
            throw new \InvalidArgumentException('non-nullable payment_group cannot be null');
        }
        $this->container['payment_group'] = $payment_group;

        return $this;
    }

    /**
     * Gets payment_currency
     *
     * @return string|null
     */
    public function getPaymentCurrency()
    {
        return $this->container['payment_currency'];
    }

    /**
     * Sets payment_currency
     *
     * @param string|null $payment_currency payment_currency
     *
     * @return self
     */
    public function setPaymentCurrency($payment_currency)
    {
        if (is_null($payment_currency)) {
            throw new \InvalidArgumentException('non-nullable payment_currency cannot be null');
        }
        $this->container['payment_currency'] = $payment_currency;

        return $this;
    }

    /**
     * Gets payment_amount
     *
     * @return float|null
     */
    public function getPaymentAmount()
    {
        return $this->container['payment_amount'];
    }

    /**
     * Sets payment_amount
     *
     * @param float|null $payment_amount payment_amount
     *
     * @return self
     */
    public function setPaymentAmount($payment_amount)
    {
        if (is_null($payment_amount)) {
            throw new \InvalidArgumentException('non-nullable payment_amount cannot be null');
        }
        $this->container['payment_amount'] = $payment_amount;

        return $this;
    }

    /**
     * Gets payment_time
     *
     * @return string|null
     */
    public function getPaymentTime()
    {
        return $this->container['payment_time'];
    }

    /**
     * Sets payment_time
     *
     * @param string|null $payment_time This is the time when the payment was initiated
     *
     * @return self
     */
    public function setPaymentTime($payment_time)
    {
        if (is_null($payment_time)) {
            throw new \InvalidArgumentException('non-nullable payment_time cannot be null');
        }
        $this->container['payment_time'] = $payment_time;

        return $this;
    }

    /**
     * Gets payment_completion_time
     *
     * @return string|null
     */
    public function getPaymentCompletionTime()
    {
        return $this->container['payment_completion_time'];
    }

    /**
     * Sets payment_completion_time
     *
     * @param string|null $payment_completion_time This is the time when the payment reaches its terminal state
     *
     * @return self
     */
    public function setPaymentCompletionTime($payment_completion_time)
    {
        if (is_null($payment_completion_time)) {
            throw new \InvalidArgumentException('non-nullable payment_completion_time cannot be null');
        }
        $this->container['payment_completion_time'] = $payment_completion_time;

        return $this;
    }

    /**
     * Gets payment_status
     *
     * @return string|null
     */
    public function getPaymentStatus()
    {
        return $this->container['payment_status'];
    }

    /**
     * Sets payment_status
     *
     * @param string|null $payment_status The transaction status can be one of  [\"SUCCESS\", \"NOT_ATTEMPTED\", \"FAILED\", \"USER_DROPPED\", \"VOID\", \"CANCELLED\", \"PENDING\"]
     *
     * @return self
     */
    public function setPaymentStatus($payment_status)
    {
        if (is_null($payment_status)) {
            throw new \InvalidArgumentException('non-nullable payment_status cannot be null');
        }
        $allowedValues = $this->getPaymentStatusAllowableValues();
        if (!in_array($payment_status, $allowedValues, true)) {
            throw new \InvalidArgumentException(
                sprintf(
                    "Invalid value '%s' for 'payment_status', must be one of '%s'",
                    $payment_status,
                    implode("', '", $allowedValues)
                )
            );
        }
        $this->container['payment_status'] = $payment_status;

        return $this;
    }

    /**
     * Gets payment_message
     *
     * @return string|null
     */
    public function getPaymentMessage()
    {
        return $this->container['payment_message'];
    }

    /**
     * Sets payment_message
     *
     * @param string|null $payment_message payment_message
     *
     * @return self
     */
    public function setPaymentMessage($payment_message)
    {
        if (is_null($payment_message)) {
            throw new \InvalidArgumentException('non-nullable payment_message cannot be null');
        }
        $this->container['payment_message'] = $payment_message;

        return $this;
    }

    /**
     * Gets bank_reference
     *
     * @return string|null
     */
    public function getBankReference()
    {
        return $this->container['bank_reference'];
    }

    /**
     * Sets bank_reference
     *
     * @param string|null $bank_reference bank_reference
     *
     * @return self
     */
    public function setBankReference($bank_reference)
    {
        if (is_null($bank_reference)) {
            throw new \InvalidArgumentException('non-nullable bank_reference cannot be null');
        }
        $this->container['bank_reference'] = $bank_reference;

        return $this;
    }

    /**
     * Gets auth_id
     *
     * @return string|null
     */
    public function getAuthId()
    {
        return $this->container['auth_id'];
    }

    /**
     * Sets auth_id
     *
     * @param string|null $auth_id auth_id
     *
     * @return self
     */
    public function setAuthId($auth_id)
    {
        if (is_null($auth_id)) {
            throw new \InvalidArgumentException('non-nullable auth_id cannot be null');
        }
        $this->container['auth_id'] = $auth_id;

        return $this;
    }

    /**
     * Gets authorization
     *
     * @return \Cashfree\Model\AuthorizationInPaymentsEntity|null
     */
    public function getAuthorization()
    {
        return $this->container['authorization'];
    }

    /**
     * Sets authorization
     *
     * @param \Cashfree\Model\AuthorizationInPaymentsEntity|null $authorization authorization
     *
     * @return self
     */
    public function setAuthorization($authorization)
    {
        if (is_null($authorization)) {
            throw new \InvalidArgumentException('non-nullable authorization cannot be null');
        }
        $this->container['authorization'] = $authorization;

        return $this;
    }

    /**
     * Gets payment_method
     *
     * @return \Cashfree\Model\PaymentEntityPaymentMethod|null
     */
    public function getPaymentMethod()
    {
        return $this->container['payment_method'];
    }

    /**
     * Sets payment_method
     *
     * @param \Cashfree\Model\PaymentEntityPaymentMethod|null $payment_method payment_method
     *
     * @return self
     */
    public function setPaymentMethod($payment_method)
    {
        if (is_null($payment_method)) {
            throw new \InvalidArgumentException('non-nullable payment_method cannot be null');
        }
        $this->container['payment_method'] = $payment_method;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


