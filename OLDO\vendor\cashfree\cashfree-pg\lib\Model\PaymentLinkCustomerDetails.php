<?php
/**
 * PaymentLinkCustomerDetails
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * Cashfree Payment Gateway APIs
 *
 * Cashfree's Payment Gateway APIs provide developers with a streamlined pathway to integrate advanced payment processing capabilities into their applications, platforms and websites.
 *
 * The version of the OpenAPI document: 2023-08-01
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 7.0.0
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Cashfree\Model;

use \ArrayAccess;
use \Cashfree\ObjectSerializer;

/**
 * PaymentLinkCustomerDetails Class Doc Comment
 *
 * @category Class
 * @description The customer details that are necessary. Note that you can pass dummy details if your use case does not require the customer details.
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<string, mixed>
 */
class PaymentLinkCustomerDetails implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'PaymentLinkCustomerDetails';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'customer_id' => 'string',
        'customer_email' => 'string',
        'customer_phone' => 'string',
        'customer_name' => 'string',
        'customer_bank_account_number' => 'string',
        'customer_bank_ifsc' => 'string',
        'customer_bank_code' => 'float'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'customer_id' => null,
        'customer_email' => null,
        'customer_phone' => null,
        'customer_name' => null,
        'customer_bank_account_number' => null,
        'customer_bank_ifsc' => null,
        'customer_bank_code' => null
    ];

    /**
      * Array of nullable properties. Used for (de)serialization
      *
      * @var boolean[]
      */
    protected static $openAPINullables = [
        'customer_id' => false,
		'customer_email' => false,
		'customer_phone' => false,
		'customer_name' => false,
		'customer_bank_account_number' => false,
		'customer_bank_ifsc' => false,
		'customer_bank_code' => false
    ];

    /**
      * If a nullable field gets set to null, insert it here
      *
      * @var boolean[]
      */
    protected $openAPINullablesSetToNull = [];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of nullable properties
     *
     * @return array
     */
    protected static function openAPINullables(): array
    {
        return self::$openAPINullables;
    }

    /**
     * Array of nullable field names deliberately set to null
     *
     * @return boolean[]
     */
    private function getOpenAPINullablesSetToNull(): array
    {
        return $this->openAPINullablesSetToNull;
    }

    /**
     * Setter - Array of nullable field names deliberately set to null
     *
     * @param boolean[] $openAPINullablesSetToNull
     */
    private function setOpenAPINullablesSetToNull(array $openAPINullablesSetToNull): void
    {
        $this->openAPINullablesSetToNull = $openAPINullablesSetToNull;
    }

    /**
     * Checks if a property is nullable
     *
     * @param string $property
     * @return bool
     */
    public static function isNullable(string $property): bool
    {
        return self::openAPINullables()[$property] ?? false;
    }

    /**
     * Checks if a nullable property is set to null.
     *
     * @param string $property
     * @return bool
     */
    public function isNullableSetToNull(string $property): bool
    {
        return in_array($property, $this->getOpenAPINullablesSetToNull(), true);
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'customer_id' => 'customer_id',
        'customer_email' => 'customer_email',
        'customer_phone' => 'customer_phone',
        'customer_name' => 'customer_name',
        'customer_bank_account_number' => 'customer_bank_account_number',
        'customer_bank_ifsc' => 'customer_bank_ifsc',
        'customer_bank_code' => 'customer_bank_code'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'customer_id' => 'setCustomerId',
        'customer_email' => 'setCustomerEmail',
        'customer_phone' => 'setCustomerPhone',
        'customer_name' => 'setCustomerName',
        'customer_bank_account_number' => 'setCustomerBankAccountNumber',
        'customer_bank_ifsc' => 'setCustomerBankIfsc',
        'customer_bank_code' => 'setCustomerBankCode'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'customer_id' => 'getCustomerId',
        'customer_email' => 'getCustomerEmail',
        'customer_phone' => 'getCustomerPhone',
        'customer_name' => 'getCustomerName',
        'customer_bank_account_number' => 'getCustomerBankAccountNumber',
        'customer_bank_ifsc' => 'getCustomerBankIfsc',
        'customer_bank_code' => 'getCustomerBankCode'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->setIfExists('customer_id', $data ?? [], null);
        $this->setIfExists('customer_email', $data ?? [], null);
        $this->setIfExists('customer_phone', $data ?? [], null);
        $this->setIfExists('customer_name', $data ?? [], null);
        $this->setIfExists('customer_bank_account_number', $data ?? [], null);
        $this->setIfExists('customer_bank_ifsc', $data ?? [], null);
        $this->setIfExists('customer_bank_code', $data ?? [], null);
    }

    /**
    * Sets $this->container[$variableName] to the given data or to the given default Value; if $variableName
    * is nullable and its value is set to null in the $fields array, then mark it as "set to null" in the
    * $this->openAPINullablesSetToNull array
    *
    * @param string $variableName
    * @param array  $fields
    * @param mixed  $defaultValue
    */
    private function setIfExists(string $variableName, array $fields, $defaultValue): void
    {
        if (self::isNullable($variableName) && array_key_exists($variableName, $fields) && is_null($fields[$variableName])) {
            $this->openAPINullablesSetToNull[] = $variableName;
        }

        $this->container[$variableName] = $fields[$variableName] ?? $defaultValue;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if (!is_null($this->container['customer_id']) && (mb_strlen($this->container['customer_id']) > 50)) {
            $invalidProperties[] = "invalid value for 'customer_id', the character length must be smaller than or equal to 50.";
        }

        if (!is_null($this->container['customer_id']) && (mb_strlen($this->container['customer_id']) < 3)) {
            $invalidProperties[] = "invalid value for 'customer_id', the character length must be bigger than or equal to 3.";
        }

        if (!is_null($this->container['customer_email']) && (mb_strlen($this->container['customer_email']) > 100)) {
            $invalidProperties[] = "invalid value for 'customer_email', the character length must be smaller than or equal to 100.";
        }

        if (!is_null($this->container['customer_email']) && (mb_strlen($this->container['customer_email']) < 3)) {
            $invalidProperties[] = "invalid value for 'customer_email', the character length must be bigger than or equal to 3.";
        }

        if ($this->container['customer_phone'] === null) {
            $invalidProperties[] = "'customer_phone' can't be null";
        }
        if ((mb_strlen($this->container['customer_phone']) > 10)) {
            $invalidProperties[] = "invalid value for 'customer_phone', the character length must be smaller than or equal to 10.";
        }

        if ((mb_strlen($this->container['customer_phone']) < 10)) {
            $invalidProperties[] = "invalid value for 'customer_phone', the character length must be bigger than or equal to 10.";
        }

        if (!is_null($this->container['customer_name']) && (mb_strlen($this->container['customer_name']) > 100)) {
            $invalidProperties[] = "invalid value for 'customer_name', the character length must be smaller than or equal to 100.";
        }

        if (!is_null($this->container['customer_name']) && (mb_strlen($this->container['customer_name']) < 3)) {
            $invalidProperties[] = "invalid value for 'customer_name', the character length must be bigger than or equal to 3.";
        }

        if (!is_null($this->container['customer_bank_account_number']) && (mb_strlen($this->container['customer_bank_account_number']) > 20)) {
            $invalidProperties[] = "invalid value for 'customer_bank_account_number', the character length must be smaller than or equal to 20.";
        }

        if (!is_null($this->container['customer_bank_account_number']) && (mb_strlen($this->container['customer_bank_account_number']) < 3)) {
            $invalidProperties[] = "invalid value for 'customer_bank_account_number', the character length must be bigger than or equal to 3.";
        }

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets customer_id
     *
     * @return string|null
     */
    public function getCustomerId()
    {
        return $this->container['customer_id'];
    }

    /**
     * Sets customer_id
     *
     * @param string|null $customer_id A unique identifier for the customer. Use alphanumeric values only.
     *
     * @return self
     */
    public function setCustomerId($customer_id)
    {
        if (is_null($customer_id)) {
            throw new \InvalidArgumentException('non-nullable customer_id cannot be null');
        }
        if ((mb_strlen($customer_id) > 50)) {
            throw new \InvalidArgumentException('invalid length for $customer_id when calling PaymentLinkCustomerDetails., must be smaller than or equal to 50.');
        }
        if ((mb_strlen($customer_id) < 3)) {
            throw new \InvalidArgumentException('invalid length for $customer_id when calling PaymentLinkCustomerDetails., must be bigger than or equal to 3.');
        }

        $this->container['customer_id'] = $customer_id;

        return $this;
    }

    /**
     * Gets customer_email
     *
     * @return string|null
     */
    public function getCustomerEmail()
    {
        return $this->container['customer_email'];
    }

    /**
     * Sets customer_email
     *
     * @param string|null $customer_email Customer email address.
     *
     * @return self
     */
    public function setCustomerEmail($customer_email)
    {
        if (is_null($customer_email)) {
            throw new \InvalidArgumentException('non-nullable customer_email cannot be null');
        }
        if ((mb_strlen($customer_email) > 100)) {
            throw new \InvalidArgumentException('invalid length for $customer_email when calling PaymentLinkCustomerDetails., must be smaller than or equal to 100.');
        }
        if ((mb_strlen($customer_email) < 3)) {
            throw new \InvalidArgumentException('invalid length for $customer_email when calling PaymentLinkCustomerDetails., must be bigger than or equal to 3.');
        }

        $this->container['customer_email'] = $customer_email;

        return $this;
    }

    /**
     * Gets customer_phone
     *
     * @return string
     */
    public function getCustomerPhone()
    {
        return $this->container['customer_phone'];
    }

    /**
     * Sets customer_phone
     *
     * @param string $customer_phone Customer phone number.
     *
     * @return self
     */
    public function setCustomerPhone($customer_phone)
    {
        if (is_null($customer_phone)) {
            throw new \InvalidArgumentException('non-nullable customer_phone cannot be null');
        }
        if ((mb_strlen($customer_phone) > 10)) {
            throw new \InvalidArgumentException('invalid length for $customer_phone when calling PaymentLinkCustomerDetails., must be smaller than or equal to 10.');
        }
        if ((mb_strlen($customer_phone) < 10)) {
            throw new \InvalidArgumentException('invalid length for $customer_phone when calling PaymentLinkCustomerDetails., must be bigger than or equal to 10.');
        }

        $this->container['customer_phone'] = $customer_phone;

        return $this;
    }

    /**
     * Gets customer_name
     *
     * @return string|null
     */
    public function getCustomerName()
    {
        return $this->container['customer_name'];
    }

    /**
     * Sets customer_name
     *
     * @param string|null $customer_name Name of the customer.
     *
     * @return self
     */
    public function setCustomerName($customer_name)
    {
        if (is_null($customer_name)) {
            throw new \InvalidArgumentException('non-nullable customer_name cannot be null');
        }
        if ((mb_strlen($customer_name) > 100)) {
            throw new \InvalidArgumentException('invalid length for $customer_name when calling PaymentLinkCustomerDetails., must be smaller than or equal to 100.');
        }
        if ((mb_strlen($customer_name) < 3)) {
            throw new \InvalidArgumentException('invalid length for $customer_name when calling PaymentLinkCustomerDetails., must be bigger than or equal to 3.');
        }

        $this->container['customer_name'] = $customer_name;

        return $this;
    }

    /**
     * Gets customer_bank_account_number
     *
     * @return string|null
     */
    public function getCustomerBankAccountNumber()
    {
        return $this->container['customer_bank_account_number'];
    }

    /**
     * Sets customer_bank_account_number
     *
     * @param string|null $customer_bank_account_number Customer bank account. Required if you want to do a bank account check (TPV)
     *
     * @return self
     */
    public function setCustomerBankAccountNumber($customer_bank_account_number)
    {
        if (is_null($customer_bank_account_number)) {
            throw new \InvalidArgumentException('non-nullable customer_bank_account_number cannot be null');
        }
        if ((mb_strlen($customer_bank_account_number) > 20)) {
            throw new \InvalidArgumentException('invalid length for $customer_bank_account_number when calling PaymentLinkCustomerDetails., must be smaller than or equal to 20.');
        }
        if ((mb_strlen($customer_bank_account_number) < 3)) {
            throw new \InvalidArgumentException('invalid length for $customer_bank_account_number when calling PaymentLinkCustomerDetails., must be bigger than or equal to 3.');
        }

        $this->container['customer_bank_account_number'] = $customer_bank_account_number;

        return $this;
    }

    /**
     * Gets customer_bank_ifsc
     *
     * @return string|null
     */
    public function getCustomerBankIfsc()
    {
        return $this->container['customer_bank_ifsc'];
    }

    /**
     * Sets customer_bank_ifsc
     *
     * @param string|null $customer_bank_ifsc Customer bank IFSC. Required if you want to do a bank account check (TPV)
     *
     * @return self
     */
    public function setCustomerBankIfsc($customer_bank_ifsc)
    {
        if (is_null($customer_bank_ifsc)) {
            throw new \InvalidArgumentException('non-nullable customer_bank_ifsc cannot be null');
        }
        $this->container['customer_bank_ifsc'] = $customer_bank_ifsc;

        return $this;
    }

    /**
     * Gets customer_bank_code
     *
     * @return float|null
     */
    public function getCustomerBankCode()
    {
        return $this->container['customer_bank_code'];
    }

    /**
     * Sets customer_bank_code
     *
     * @param float|null $customer_bank_code Customer bank code. Required for net banking payments, if you want to do a bank account check (TPV)
     *
     * @return self
     */
    public function setCustomerBankCode($customer_bank_code)
    {
        if (is_null($customer_bank_code)) {
            throw new \InvalidArgumentException('non-nullable customer_bank_code cannot be null');
        }
        $this->container['customer_bank_code'] = $customer_bank_code;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


