<?php
/**
 * EasySplitApiTest
 * PHP version 7.4
 *
 * @category Class
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * Cashfree Payment Gateway APIs
 *
 * Cashfree's Payment Gateway APIs provide developers with a streamlined pathway to integrate advanced payment processing capabilities into their applications, platforms and websites.
 *
 * The version of the OpenAPI document: 2023-08-01
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 7.0.0
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Please update the test case below to test the endpoint.
 */

namespace Cashfree\Test\Api;

use \Cashfree\Configuration;
use \Cashfree\ApiException;
use \Cashfree\ObjectSerializer;
use PHPUnit\Framework\TestCase;

/**
 * EasySplitApiTest Class Doc Comment
 *
 * @category Class
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */
class EasySplitApiTest extends TestCase
{

    /**
     * Setup before running any test cases
     */
    public static function setUpBeforeClass(): void
    {
    }

    /**
     * Setup before running each test case
     */
    public function setUp(): void
    {
    }

    /**
     * Clean up after running each test case
     */
    public function tearDown(): void
    {
    }

    /**
     * Clean up after running all test cases
     */
    public static function tearDownAfterClass(): void
    {
    }

    /**
     * Test case for pGESCreateOnDemandTransfer
     *
     * Create On Demand Transfer.
     *
     */
    public function testPGESCreateOnDemandTransfer()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for pGESCreateVendors
     *
     * Create vendor.
     *
     */
    public function testPGESCreateVendors()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for pGESDownloadVendorsDocs
     *
     * Download Vendor Documents.
     *
     */
    public function testPGESDownloadVendorsDocs()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for pGESFetchVendors
     *
     * Get Vendor All Details.
     *
     */
    public function testPGESFetchVendors()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for pGESGetVendorBalance
     *
     * Get On Demand Balance.
     *
     */
    public function testPGESGetVendorBalance()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for pGESGetVendorBalanceTransferCharges
     *
     * Get Vendor Balance Transfer Charges.
     *
     */
    public function testPGESGetVendorBalanceTransferCharges()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for pGESGetVendorsDocs
     *
     * Get Vendor All Documents Status.
     *
     */
    public function testPGESGetVendorsDocs()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for pGESOrderRecon
     *
     * Get Split and Settlement Details by OrderID v2.0.
     *
     */
    public function testPGESOrderRecon()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for pGESUpdateVendors
     *
     * Update vendor Details.
     *
     */
    public function testPGESUpdateVendors()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for pGESUploadVendorsDocs
     *
     * Upload Vendor Docs.
     *
     */
    public function testPGESUploadVendorsDocs()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for pGOrderSplitAfterPayment
     *
     * Split After Payment.
     *
     */
    public function testPGOrderSplitAfterPayment()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for pGOrderStaticSplit
     *
     * Create Static Split Configuration.
     *
     */
    public function testPGOrderStaticSplit()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }
}
