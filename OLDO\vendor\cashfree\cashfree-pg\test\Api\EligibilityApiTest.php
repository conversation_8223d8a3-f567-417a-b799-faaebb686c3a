<?php
/**
 * EligibilityApiTest
 * PHP version 7.4
 *
 * @category Class
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * Cashfree Payment Gateway APIs
 *
 * Cashfree's Payment Gateway APIs provide developers with a streamlined pathway to integrate advanced payment processing capabilities into their applications, platforms and websites.
 *
 * The version of the OpenAPI document: 2023-08-01
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 7.0.0
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Please update the test case below to test the endpoint.
 */

namespace Cashfree\Test\Api;

use \Cashfree\Configuration;
use \Cashfree\ApiException;
use \Cashfree\ObjectSerializer;
use PHPUnit\Framework\TestCase;

/**
 * EligibilityApiTest Class Doc Comment
 *
 * @category Class
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */
class EligibilityApiTest extends TestCase
{

    /**
     * Setup before running any test cases
     */
    public static function setUpBeforeClass(): void
    {
    }

    /**
     * Setup before running each test case
     */
    public function setUp(): void
    {
    }

    /**
     * Clean up after running each test case
     */
    public function tearDown(): void
    {
    }

    /**
     * Clean up after running all test cases
     */
    public static function tearDownAfterClass(): void
    {
    }

    /**
     * Test case for pGEligibilityFetchCardlessEMI
     *
     * Get Eligible Cardless EMI Payment Methods for a customer on an order.
     *
     */
    public function testPGEligibilityFetchCardlessEMI()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for pGEligibilityFetchOffers
     *
     * Get Eligible Offers for an Order.
     *
     */
    public function testPGEligibilityFetchOffers()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for pGEligibilityFetchPaylater
     *
     * Get Eligible Paylater for a customer on an order.
     *
     */
    public function testPGEligibilityFetchPaylater()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for pGEligibilityFetchPaymentMethods
     *
     * Get eligible Payment Methods.
     *
     */
    public function testPGEligibilityFetchPaymentMethods()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }
}
