<?php
/**
 * PaymentsApiTest
 * PHP version 7.4
 *
 * @category Class
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * Cashfree Payment Gateway APIs
 *
 * Cashfree's Payment Gateway APIs provide developers with a streamlined pathway to integrate advanced payment processing capabilities into their applications, platforms and websites.
 *
 * The version of the OpenAPI document: 2023-08-01
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 7.0.0
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Please update the test case below to test the endpoint.
 */

namespace Cashfree\Test\Api;

use \Cashfree\Configuration;
use \Cashfree\ApiException;
use \Cashfree\ObjectSerializer;
use PHPUnit\Framework\TestCase;

/**
 * PaymentsApiTest Class Doc Comment
 *
 * @category Class
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */
class PaymentsApiTest extends TestCase
{

    /**
     * Setup before running any test cases
     */
    public static function setUpBeforeClass(): void
    {
    }

    /**
     * Setup before running each test case
     */
    public function setUp(): void
    {
    }

    /**
     * Clean up after running each test case
     */
    public function tearDown(): void
    {
    }

    /**
     * Clean up after running all test cases
     */
    public static function tearDownAfterClass(): void
    {
    }

    /**
     * Test case for pGAuthorizeOrder
     *
     * Preauthorization.
     *
     */
    public function testPGAuthorizeOrder()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for pGOrderAuthenticatePayment
     *
     * Submit or Resend OTP.
     *
     */
    public function testPGOrderAuthenticatePayment()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for pGOrderFetchPayment
     *
     * Get Payment by ID.
     *
     */
    public function testPGOrderFetchPayment()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for pGOrderFetchPayments
     *
     * Get Payments for an Order.
     *
     */
    public function testPGOrderFetchPayments()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for pGPayOrder
     *
     * Order Pay.
     *
     */
    public function testPGPayOrder()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }
}
