<?php
/**
 * SoftPOSApiTest
 * PHP version 7.4
 *
 * @category Class
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * Cashfree Payment Gateway APIs
 *
 * Cashfree's Payment Gateway APIs provide developers with a streamlined pathway to integrate advanced payment processing capabilities into their applications, platforms and websites.
 *
 * The version of the OpenAPI document: 2023-08-01
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 7.0.0
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Please update the test case below to test the endpoint.
 */

namespace Cashfree\Test\Api;

use \Cashfree\Configuration;
use \Cashfree\ApiException;
use \Cashfree\ObjectSerializer;
use PHPUnit\Framework\TestCase;

/**
 * SoftPOSApiTest Class Doc Comment
 *
 * @category Class
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */
class SoftPOSApiTest extends TestCase
{

    /**
     * Setup before running any test cases
     */
    public static function setUpBeforeClass(): void
    {
    }

    /**
     * Setup before running each test case
     */
    public function setUp(): void
    {
    }

    /**
     * Clean up after running each test case
     */
    public function tearDown(): void
    {
    }

    /**
     * Clean up after running all test cases
     */
    public static function tearDownAfterClass(): void
    {
    }

    /**
     * Test case for sposCreateTerminal
     *
     * Create Terminal.
     *
     */
    public function testSposCreateTerminal()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for sposCreateTerminalTransaction
     *
     * Create Terminal Transaction.
     *
     */
    public function testSposCreateTerminalTransaction()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for sposFetchTerminal
     *
     * Get Terminal Status using Phone Number.
     *
     */
    public function testSposFetchTerminal()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for sposFetchTerminalQRCodes
     *
     * Fetch Terminal QR Codes.
     *
     */
    public function testSposFetchTerminalQRCodes()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for sposFetchTerminalTransaction
     *
     * Get Terminal Transaction.
     *
     */
    public function testSposFetchTerminalTransaction()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for sposUpdateTerminal
     *
     * Update Terminal.
     *
     */
    public function testSposUpdateTerminal()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for sposUpdateTerminalStatus
     *
     * Update Terminal Status.
     *
     */
    public function testSposUpdateTerminalStatus()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for sposUploadTerminalDocs
     *
     * Upload Terminal Docs.
     *
     */
    public function testSposUploadTerminalDocs()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }
}
