<?php
/**
 * SubscriptionApiTest
 * PHP version 7.4
 *
 * @category Class
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * Cashfree Payment Gateway APIs
 *
 * Cashfree's Payment Gateway APIs provide developers with a streamlined pathway to integrate advanced payment processing capabilities into their applications, platforms and websites.
 *
 * The version of the OpenAPI document: 2023-08-01
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 7.0.0
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Please update the test case below to test the endpoint.
 */

namespace Cashfree\Test\Api;

use \Cashfree\Configuration;
use \Cashfree\ApiException;
use \Cashfree\ObjectSerializer;
use PHPUnit\Framework\TestCase;

/**
 * SubscriptionApiTest Class Doc Comment
 *
 * @category Class
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */
class SubscriptionApiTest extends TestCase
{

    /**
     * Setup before running any test cases
     */
    public static function setUpBeforeClass(): void
    {
    }

    /**
     * Setup before running each test case
     */
    public function setUp(): void
    {
    }

    /**
     * Clean up after running each test case
     */
    public function tearDown(): void
    {
    }

    /**
     * Clean up after running all test cases
     */
    public static function tearDownAfterClass(): void
    {
    }

    /**
     * Test case for subsCreatePayment
     *
     * Raise a charge or create an auth..
     *
     */
    public function testSubsCreatePayment()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for subsCreatePlan
     *
     * Create a plan..
     *
     */
    public function testSubsCreatePlan()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for subsCreateRefund
     *
     * Create a refund..
     *
     */
    public function testSubsCreateRefund()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for subsCreateSubscription
     *
     * Create Subscription.
     *
     */
    public function testSubsCreateSubscription()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for subsFetchPlan
     *
     * Fetch Plan.
     *
     */
    public function testSubsFetchPlan()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for subsFetchSubscription
     *
     * Fetch Subscription.
     *
     */
    public function testSubsFetchSubscription()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for subsFetchSubscriptionPayment
     *
     * Fetch details of a single payment..
     *
     */
    public function testSubsFetchSubscriptionPayment()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for subsFetchSubscriptionPayments
     *
     * Fetch details of all payments of a subscription..
     *
     */
    public function testSubsFetchSubscriptionPayments()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for subsFetchSubscriptionRefund
     *
     * Fetch details of a refund..
     *
     */
    public function testSubsFetchSubscriptionRefund()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for subsManageSubscription
     *
     * Manage a subscription..
     *
     */
    public function testSubsManageSubscription()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for subsManageSubscriptionPayment
     *
     * Manage a single payment..
     *
     */
    public function testSubsManageSubscriptionPayment()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for subscriptionDocumentUpload
     *
     * Upload Physical Nach for Physical Nach Authorization..
     *
     */
    public function testSubscriptionDocumentUpload()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test case for subscriptionEligibility
     *
     * Get Eligible payment methods available for a subscription.
     *
     */
    public function testSubscriptionEligibility()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }
}
