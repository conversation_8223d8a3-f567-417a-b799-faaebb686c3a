<?php
/**
 * ESOrderReconResponseDataInnerTest
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * Cashfree Payment Gateway APIs
 *
 * Cashfree's Payment Gateway APIs provide developers with a streamlined pathway to integrate advanced payment processing capabilities into their applications, platforms and websites.
 *
 * The version of the OpenAPI document: 2023-08-01
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 7.0.0
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Please update the test case below to test the model.
 */

namespace Cashfree\Test\Model;

use PHPUnit\Framework\TestCase;

/**
 * ESOrderReconResponseDataInnerTest Class Doc Comment
 *
 * @category    Class
 * @description ESOrderReconResponseDataInner
 * @package     Cashfree
 * <AUTHOR> Generator team
 * @link        https://openapi-generator.tech
 */
class ESOrderReconResponseDataInnerTest extends TestCase
{

    /**
     * Setup before running any test case
     */
    public static function setUpBeforeClass(): void
    {
    }

    /**
     * Setup before running each test case
     */
    public function setUp(): void
    {
    }

    /**
     * Clean up after running each test case
     */
    public function tearDown(): void
    {
    }

    /**
     * Clean up after running all test cases
     */
    public static function tearDownAfterClass(): void
    {
    }

    /**
     * Test "ESOrderReconResponseDataInner"
     */
    public function testESOrderReconResponseDataInner()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "amount"
     */
    public function testPropertyAmount()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "settlement_eligibility_time"
     */
    public function testPropertySettlementEligibilityTime()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "merchant_order_id"
     */
    public function testPropertyMerchantOrderId()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "tx_time"
     */
    public function testPropertyTxTime()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "settled"
     */
    public function testPropertySettled()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "entity_id"
     */
    public function testPropertyEntityId()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "merchant_settlement_utr"
     */
    public function testPropertyMerchantSettlementUtr()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "currency"
     */
    public function testPropertyCurrency()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "sale_type"
     */
    public function testPropertySaleType()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "customer_name"
     */
    public function testPropertyCustomerName()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "customer_email"
     */
    public function testPropertyCustomerEmail()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "customer_phone"
     */
    public function testPropertyCustomerPhone()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "merchant_vendor_commission"
     */
    public function testPropertyMerchantVendorCommission()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "split_service_charge"
     */
    public function testPropertySplitServiceCharge()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "split_service_tax"
     */
    public function testPropertySplitServiceTax()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "pg_service_tax"
     */
    public function testPropertyPgServiceTax()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "pg_service_charge"
     */
    public function testPropertyPgServiceCharge()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "pg_charge_postpaid"
     */
    public function testPropertyPgChargePostpaid()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "merchant_settlement_id"
     */
    public function testPropertyMerchantSettlementId()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "added_on"
     */
    public function testPropertyAddedOn()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "tags"
     */
    public function testPropertyTags()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "entity_type"
     */
    public function testPropertyEntityType()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "settlement_initiated_on"
     */
    public function testPropertySettlementInitiatedOn()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "settlement_time"
     */
    public function testPropertySettlementTime()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "order_splits"
     */
    public function testPropertyOrderSplits()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "eligible_split_balance"
     */
    public function testPropertyEligibleSplitBalance()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }
}
