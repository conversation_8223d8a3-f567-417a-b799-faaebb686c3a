<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\AccessContextManager;

class AuthorizedOrgsDesc extends \Google\Collection
{
  protected $collection_key = 'orgs';
  /**
   * @var string
   */
  public $assetType;
  /**
   * @var string
   */
  public $authorizationDirection;
  /**
   * @var string
   */
  public $authorizationType;
  /**
   * @var string
   */
  public $name;
  /**
   * @var string[]
   */
  public $orgs;

  /**
   * @param string
   */
  public function setAssetType($assetType)
  {
    $this->assetType = $assetType;
  }
  /**
   * @return string
   */
  public function getAssetType()
  {
    return $this->assetType;
  }
  /**
   * @param string
   */
  public function setAuthorizationDirection($authorizationDirection)
  {
    $this->authorizationDirection = $authorizationDirection;
  }
  /**
   * @return string
   */
  public function getAuthorizationDirection()
  {
    return $this->authorizationDirection;
  }
  /**
   * @param string
   */
  public function setAuthorizationType($authorizationType)
  {
    $this->authorizationType = $authorizationType;
  }
  /**
   * @return string
   */
  public function getAuthorizationType()
  {
    return $this->authorizationType;
  }
  /**
   * @param string
   */
  public function setName($name)
  {
    $this->name = $name;
  }
  /**
   * @return string
   */
  public function getName()
  {
    return $this->name;
  }
  /**
   * @param string[]
   */
  public function setOrgs($orgs)
  {
    $this->orgs = $orgs;
  }
  /**
   * @return string[]
   */
  public function getOrgs()
  {
    return $this->orgs;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(AuthorizedOrgsDesc::class, 'Google_Service_AccessContextManager_AuthorizedOrgsDesc');
