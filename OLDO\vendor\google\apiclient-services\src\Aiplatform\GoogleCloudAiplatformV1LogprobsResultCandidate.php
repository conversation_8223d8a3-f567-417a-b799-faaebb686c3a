<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Aiplatform;

class GoogleCloudAiplatformV1LogprobsResultCandidate extends \Google\Model
{
  /**
   * @var float
   */
  public $logProbability;
  /**
   * @var string
   */
  public $token;
  /**
   * @var int
   */
  public $tokenId;

  /**
   * @param float
   */
  public function setLogProbability($logProbability)
  {
    $this->logProbability = $logProbability;
  }
  /**
   * @return float
   */
  public function getLogProbability()
  {
    return $this->logProbability;
  }
  /**
   * @param string
   */
  public function setToken($token)
  {
    $this->token = $token;
  }
  /**
   * @return string
   */
  public function getToken()
  {
    return $this->token;
  }
  /**
   * @param int
   */
  public function setTokenId($tokenId)
  {
    $this->tokenId = $tokenId;
  }
  /**
   * @return int
   */
  public function getTokenId()
  {
    return $this->tokenId;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudAiplatformV1LogprobsResultCandidate::class, 'Google_Service_Aiplatform_GoogleCloudAiplatformV1LogprobsResultCandidate');
