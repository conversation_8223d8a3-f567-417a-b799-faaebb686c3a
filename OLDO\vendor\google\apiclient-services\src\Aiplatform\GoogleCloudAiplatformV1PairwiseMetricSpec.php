<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Aiplatform;

class GoogleCloudAiplatformV1PairwiseMetricSpec extends \Google\Model
{
  /**
   * @var string
   */
  public $baselineResponseFieldName;
  /**
   * @var string
   */
  public $candidateResponseFieldName;
  /**
   * @var string
   */
  public $metricPromptTemplate;
  /**
   * @var string
   */
  public $systemInstruction;

  /**
   * @param string
   */
  public function setBaselineResponseFieldName($baselineResponseFieldName)
  {
    $this->baselineResponseFieldName = $baselineResponseFieldName;
  }
  /**
   * @return string
   */
  public function getBaselineResponseFieldName()
  {
    return $this->baselineResponseFieldName;
  }
  /**
   * @param string
   */
  public function setCandidateResponseFieldName($candidateResponseFieldName)
  {
    $this->candidateResponseFieldName = $candidateResponseFieldName;
  }
  /**
   * @return string
   */
  public function getCandidateResponseFieldName()
  {
    return $this->candidateResponseFieldName;
  }
  /**
   * @param string
   */
  public function setMetricPromptTemplate($metricPromptTemplate)
  {
    $this->metricPromptTemplate = $metricPromptTemplate;
  }
  /**
   * @return string
   */
  public function getMetricPromptTemplate()
  {
    return $this->metricPromptTemplate;
  }
  /**
   * @param string
   */
  public function setSystemInstruction($systemInstruction)
  {
    $this->systemInstruction = $systemInstruction;
  }
  /**
   * @return string
   */
  public function getSystemInstruction()
  {
    return $this->systemInstruction;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudAiplatformV1PairwiseMetricSpec::class, 'Google_Service_Aiplatform_GoogleCloudAiplatformV1PairwiseMetricSpec');
