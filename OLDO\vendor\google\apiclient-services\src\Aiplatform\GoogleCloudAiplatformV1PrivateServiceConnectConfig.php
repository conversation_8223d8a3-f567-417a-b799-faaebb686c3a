<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Aiplatform;

class GoogleCloudAiplatformV1PrivateServiceConnectConfig extends \Google\Collection
{
  protected $collection_key = 'projectAllowlist';
  /**
   * @var bool
   */
  public $enablePrivateServiceConnect;
  /**
   * @var string[]
   */
  public $projectAllowlist;
  /**
   * @var string
   */
  public $serviceAttachment;

  /**
   * @param bool
   */
  public function setEnablePrivateServiceConnect($enablePrivateServiceConnect)
  {
    $this->enablePrivateServiceConnect = $enablePrivateServiceConnect;
  }
  /**
   * @return bool
   */
  public function getEnablePrivateServiceConnect()
  {
    return $this->enablePrivateServiceConnect;
  }
  /**
   * @param string[]
   */
  public function setProjectAllowlist($projectAllowlist)
  {
    $this->projectAllowlist = $projectAllowlist;
  }
  /**
   * @return string[]
   */
  public function getProjectAllowlist()
  {
    return $this->projectAllowlist;
  }
  /**
   * @param string
   */
  public function setServiceAttachment($serviceAttachment)
  {
    $this->serviceAttachment = $serviceAttachment;
  }
  /**
   * @return string
   */
  public function getServiceAttachment()
  {
    return $this->serviceAttachment;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudAiplatformV1PrivateServiceConnectConfig::class, 'Google_Service_Aiplatform_GoogleCloudAiplatformV1PrivateServiceConnectConfig');
