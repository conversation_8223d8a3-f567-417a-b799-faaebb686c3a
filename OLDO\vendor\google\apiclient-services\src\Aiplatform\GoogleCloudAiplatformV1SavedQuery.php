<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Aiplatform;

class GoogleCloudAiplatformV1SavedQuery extends \Google\Model
{
  /**
   * @var string
   */
  public $annotationFilter;
  /**
   * @var int
   */
  public $annotationSpecCount;
  /**
   * @var string
   */
  public $createTime;
  /**
   * @var string
   */
  public $displayName;
  /**
   * @var string
   */
  public $etag;
  /**
   * @var array
   */
  public $metadata;
  /**
   * @var string
   */
  public $name;
  /**
   * @var string
   */
  public $problemType;
  /**
   * @var bool
   */
  public $supportAutomlTraining;
  /**
   * @var string
   */
  public $updateTime;

  /**
   * @param string
   */
  public function setAnnotationFilter($annotationFilter)
  {
    $this->annotationFilter = $annotationFilter;
  }
  /**
   * @return string
   */
  public function getAnnotationFilter()
  {
    return $this->annotationFilter;
  }
  /**
   * @param int
   */
  public function setAnnotationSpecCount($annotationSpecCount)
  {
    $this->annotationSpecCount = $annotationSpecCount;
  }
  /**
   * @return int
   */
  public function getAnnotationSpecCount()
  {
    return $this->annotationSpecCount;
  }
  /**
   * @param string
   */
  public function setCreateTime($createTime)
  {
    $this->createTime = $createTime;
  }
  /**
   * @return string
   */
  public function getCreateTime()
  {
    return $this->createTime;
  }
  /**
   * @param string
   */
  public function setDisplayName($displayName)
  {
    $this->displayName = $displayName;
  }
  /**
   * @return string
   */
  public function getDisplayName()
  {
    return $this->displayName;
  }
  /**
   * @param string
   */
  public function setEtag($etag)
  {
    $this->etag = $etag;
  }
  /**
   * @return string
   */
  public function getEtag()
  {
    return $this->etag;
  }
  /**
   * @param array
   */
  public function setMetadata($metadata)
  {
    $this->metadata = $metadata;
  }
  /**
   * @return array
   */
  public function getMetadata()
  {
    return $this->metadata;
  }
  /**
   * @param string
   */
  public function setName($name)
  {
    $this->name = $name;
  }
  /**
   * @return string
   */
  public function getName()
  {
    return $this->name;
  }
  /**
   * @param string
   */
  public function setProblemType($problemType)
  {
    $this->problemType = $problemType;
  }
  /**
   * @return string
   */
  public function getProblemType()
  {
    return $this->problemType;
  }
  /**
   * @param bool
   */
  public function setSupportAutomlTraining($supportAutomlTraining)
  {
    $this->supportAutomlTraining = $supportAutomlTraining;
  }
  /**
   * @return bool
   */
  public function getSupportAutomlTraining()
  {
    return $this->supportAutomlTraining;
  }
  /**
   * @param string
   */
  public function setUpdateTime($updateTime)
  {
    $this->updateTime = $updateTime;
  }
  /**
   * @return string
   */
  public function getUpdateTime()
  {
    return $this->updateTime;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudAiplatformV1SavedQuery::class, 'Google_Service_Aiplatform_GoogleCloudAiplatformV1SavedQuery');
