<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Aiplatform\Resource;

use Google\Service\Aiplatform\GoogleCloudAiplatformV1Artifact;
use Google\Service\Aiplatform\GoogleCloudAiplatformV1LineageSubgraph;
use Google\Service\Aiplatform\GoogleCloudAiplatformV1ListArtifactsResponse;
use Google\Service\Aiplatform\GoogleCloudAiplatformV1PurgeArtifactsRequest;
use Google\Service\Aiplatform\GoogleLongrunningOperation;

/**
 * The "artifacts" collection of methods.
 * Typical usage is:
 *  <code>
 *   $aiplatformService = new Google\Service\Aiplatform(...);
 *   $artifacts = $aiplatformService->projects_locations_metadataStores_artifacts;
 *  </code>
 */
class ProjectsLocationsMetadataStoresArtifacts extends \Google\Service\Resource
{
  /**
   * Creates an Artifact associated with a MetadataStore. (artifacts.create)
   *
   * @param string $parent Required. The resource name of the MetadataStore where
   * the Artifact should be created. Format:
   * `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
   * @param GoogleCloudAiplatformV1Artifact $postBody
   * @param array $optParams Optional parameters.
   *
   * @opt_param string artifactId The {artifact} portion of the resource name with
   * the format: `projects/{project}/locations/{location}/metadataStores/{metadata
   * store}/artifacts/{artifact}` If not provided, the Artifact's ID will be a
   * UUID generated by the service. Must be 4-128 characters in length. Valid
   * characters are `/a-z-/`. Must be unique across all Artifacts in the parent
   * MetadataStore. (Otherwise the request will fail with ALREADY_EXISTS, or
   * PERMISSION_DENIED if the caller can't view the preexisting Artifact.)
   * @return GoogleCloudAiplatformV1Artifact
   * @throws \Google\Service\Exception
   */
  public function create($parent, GoogleCloudAiplatformV1Artifact $postBody, $optParams = [])
  {
    $params = ['parent' => $parent, 'postBody' => $postBody];
    $params = array_merge($params, $optParams);
    return $this->call('create', [$params], GoogleCloudAiplatformV1Artifact::class);
  }
  /**
   * Deletes an Artifact. (artifacts.delete)
   *
   * @param string $name Required. The resource name of the Artifact to delete.
   * Format: `projects/{project}/locations/{location}/metadataStores/{metadatastor
   * e}/artifacts/{artifact}`
   * @param array $optParams Optional parameters.
   *
   * @opt_param string etag Optional. The etag of the Artifact to delete. If this
   * is provided, it must match the server's etag. Otherwise, the request will
   * fail with a FAILED_PRECONDITION.
   * @return GoogleLongrunningOperation
   * @throws \Google\Service\Exception
   */
  public function delete($name, $optParams = [])
  {
    $params = ['name' => $name];
    $params = array_merge($params, $optParams);
    return $this->call('delete', [$params], GoogleLongrunningOperation::class);
  }
  /**
   * Retrieves a specific Artifact. (artifacts.get)
   *
   * @param string $name Required. The resource name of the Artifact to retrieve.
   * Format: `projects/{project}/locations/{location}/metadataStores/{metadatastor
   * e}/artifacts/{artifact}`
   * @param array $optParams Optional parameters.
   * @return GoogleCloudAiplatformV1Artifact
   * @throws \Google\Service\Exception
   */
  public function get($name, $optParams = [])
  {
    $params = ['name' => $name];
    $params = array_merge($params, $optParams);
    return $this->call('get', [$params], GoogleCloudAiplatformV1Artifact::class);
  }
  /**
   * Lists Artifacts in the MetadataStore.
   * (artifacts.listProjectsLocationsMetadataStoresArtifacts)
   *
   * @param string $parent Required. The MetadataStore whose Artifacts should be
   * listed. Format:
   * `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
   * @param array $optParams Optional parameters.
   *
   * @opt_param string filter Filter specifying the boolean condition for the
   * Artifacts to satisfy in order to be part of the result set. The syntax to
   * define filter query is based on https://google.aip.dev/160. The supported set
   * of filters include the following: * **Attribute filtering**: For example:
   * `display_name = "test"`. Supported fields include: `name`, `display_name`,
   * `uri`, `state`, `schema_title`, `create_time`, and `update_time`. Time
   * fields, such as `create_time` and `update_time`, require values specified in
   * RFC-3339 format. For example: `create_time = "2020-11-19T11:30:00-04:00"` *
   * **Metadata field**: To filter on metadata fields use traversal operation as
   * follows: `metadata..`. For example: `metadata.field_1.number_value = 10.0` In
   * case the field name contains special characters (such as colon), one can
   * embed it inside double quote. For example: `metadata."field:1".number_value =
   * 10.0` * **Context based filtering**: To filter Artifacts based on the
   * contexts to which they belong, use the function operator with the full
   * resource name `in_context()`. For example:
   * `in_context("projects//locations//metadataStores//contexts/")` Each of the
   * above supported filter types can be combined together using logical operators
   * (`AND` & `OR`). Maximum nested expression depth allowed is 5. For example:
   * `display_name = "test" AND metadata.field1.bool_value = true`.
   * @opt_param string orderBy How the list of messages is ordered. Specify the
   * values to order by and an ordering operation. The default sorting order is
   * ascending. To specify descending order for a field, users append a " desc"
   * suffix; for example: "foo desc, bar". Subfields are specified with a `.`
   * character, such as foo.bar. see https://google.aip.dev/132#ordering for more
   * details.
   * @opt_param int pageSize The maximum number of Artifacts to return. The
   * service may return fewer. Must be in range 1-1000, inclusive. Defaults to
   * 100.
   * @opt_param string pageToken A page token, received from a previous
   * MetadataService.ListArtifacts call. Provide this to retrieve the subsequent
   * page. When paginating, all other provided parameters must match the call that
   * provided the page token. (Otherwise the request will fail with
   * INVALID_ARGUMENT error.)
   * @return GoogleCloudAiplatformV1ListArtifactsResponse
   * @throws \Google\Service\Exception
   */
  public function listProjectsLocationsMetadataStoresArtifacts($parent, $optParams = [])
  {
    $params = ['parent' => $parent];
    $params = array_merge($params, $optParams);
    return $this->call('list', [$params], GoogleCloudAiplatformV1ListArtifactsResponse::class);
  }
  /**
   * Updates a stored Artifact. (artifacts.patch)
   *
   * @param string $name Output only. The resource name of the Artifact.
   * @param GoogleCloudAiplatformV1Artifact $postBody
   * @param array $optParams Optional parameters.
   *
   * @opt_param bool allowMissing If set to true, and the Artifact is not found, a
   * new Artifact is created.
   * @opt_param string updateMask Optional. A FieldMask indicating which fields
   * should be updated.
   * @return GoogleCloudAiplatformV1Artifact
   * @throws \Google\Service\Exception
   */
  public function patch($name, GoogleCloudAiplatformV1Artifact $postBody, $optParams = [])
  {
    $params = ['name' => $name, 'postBody' => $postBody];
    $params = array_merge($params, $optParams);
    return $this->call('patch', [$params], GoogleCloudAiplatformV1Artifact::class);
  }
  /**
   * Purges Artifacts. (artifacts.purge)
   *
   * @param string $parent Required. The metadata store to purge Artifacts from.
   * Format:
   * `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
   * @param GoogleCloudAiplatformV1PurgeArtifactsRequest $postBody
   * @param array $optParams Optional parameters.
   * @return GoogleLongrunningOperation
   * @throws \Google\Service\Exception
   */
  public function purge($parent, GoogleCloudAiplatformV1PurgeArtifactsRequest $postBody, $optParams = [])
  {
    $params = ['parent' => $parent, 'postBody' => $postBody];
    $params = array_merge($params, $optParams);
    return $this->call('purge', [$params], GoogleLongrunningOperation::class);
  }
  /**
   * Retrieves lineage of an Artifact represented through Artifacts and Executions
   * connected by Event edges and returned as a LineageSubgraph.
   * (artifacts.queryArtifactLineageSubgraph)
   *
   * @param string $artifact Required. The resource name of the Artifact whose
   * Lineage needs to be retrieved as a LineageSubgraph. Format: `projects/{projec
   * t}/locations/{location}/metadataStores/{metadatastore}/artifacts/{artifact}`
   * The request may error with FAILED_PRECONDITION if the number of Artifacts,
   * the number of Executions, or the number of Events that would be returned for
   * the Context exceeds 1000.
   * @param array $optParams Optional parameters.
   *
   * @opt_param string filter Filter specifying the boolean condition for the
   * Artifacts to satisfy in order to be part of the Lineage Subgraph. The syntax
   * to define filter query is based on https://google.aip.dev/160. The supported
   * set of filters include the following: * **Attribute filtering**: For example:
   * `display_name = "test"` Supported fields include: `name`, `display_name`,
   * `uri`, `state`, `schema_title`, `create_time`, and `update_time`. Time
   * fields, such as `create_time` and `update_time`, require values specified in
   * RFC-3339 format. For example: `create_time = "2020-11-19T11:30:00-04:00"` *
   * **Metadata field**: To filter on metadata fields use traversal operation as
   * follows: `metadata..`. For example: `metadata.field_1.number_value = 10.0` In
   * case the field name contains special characters (such as colon), one can
   * embed it inside double quote. For example: `metadata."field:1".number_value =
   * 10.0` Each of the above supported filter types can be combined together using
   * logical operators (`AND` & `OR`). Maximum nested expression depth allowed is
   * 5. For example: `display_name = "test" AND metadata.field1.bool_value =
   * true`.
   * @opt_param int maxHops Specifies the size of the lineage graph in terms of
   * number of hops from the specified artifact. Negative Value: INVALID_ARGUMENT
   * error is returned 0: Only input artifact is returned. No value: Transitive
   * closure is performed to return the complete graph.
   * @return GoogleCloudAiplatformV1LineageSubgraph
   * @throws \Google\Service\Exception
   */
  public function queryArtifactLineageSubgraph($artifact, $optParams = [])
  {
    $params = ['artifact' => $artifact];
    $params = array_merge($params, $optParams);
    return $this->call('queryArtifactLineageSubgraph', [$params], GoogleCloudAiplatformV1LineageSubgraph::class);
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(ProjectsLocationsMetadataStoresArtifacts::class, 'Google_Service_Aiplatform_Resource_ProjectsLocationsMetadataStoresArtifacts');
