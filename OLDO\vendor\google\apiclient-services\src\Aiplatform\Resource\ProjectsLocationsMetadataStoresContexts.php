<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Aiplatform\Resource;

use Google\Service\Aiplatform\GoogleCloudAiplatformV1AddContextArtifactsAndExecutionsRequest;
use Google\Service\Aiplatform\GoogleCloudAiplatformV1AddContextArtifactsAndExecutionsResponse;
use Google\Service\Aiplatform\GoogleCloudAiplatformV1AddContextChildrenRequest;
use Google\Service\Aiplatform\GoogleCloudAiplatformV1AddContextChildrenResponse;
use Google\Service\Aiplatform\GoogleCloudAiplatformV1Context;
use Google\Service\Aiplatform\GoogleCloudAiplatformV1LineageSubgraph;
use Google\Service\Aiplatform\GoogleCloudAiplatformV1ListContextsResponse;
use Google\Service\Aiplatform\GoogleCloudAiplatformV1PurgeContextsRequest;
use Google\Service\Aiplatform\GoogleCloudAiplatformV1RemoveContextChildrenRequest;
use Google\Service\Aiplatform\GoogleCloudAiplatformV1RemoveContextChildrenResponse;
use Google\Service\Aiplatform\GoogleLongrunningOperation;

/**
 * The "contexts" collection of methods.
 * Typical usage is:
 *  <code>
 *   $aiplatformService = new Google\Service\Aiplatform(...);
 *   $contexts = $aiplatformService->projects_locations_metadataStores_contexts;
 *  </code>
 */
class ProjectsLocationsMetadataStoresContexts extends \Google\Service\Resource
{
  /**
   * Adds a set of Artifacts and Executions to a Context. If any of the Artifacts
   * or Executions have already been added to a Context, they are simply skipped.
   * (contexts.addContextArtifactsAndExecutions)
   *
   * @param string $context Required. The resource name of the Context that the
   * Artifacts and Executions belong to. Format: `projects/{project}/locations/{lo
   * cation}/metadataStores/{metadatastore}/contexts/{context}`
   * @param GoogleCloudAiplatformV1AddContextArtifactsAndExecutionsRequest $postBody
   * @param array $optParams Optional parameters.
   * @return GoogleCloudAiplatformV1AddContextArtifactsAndExecutionsResponse
   * @throws \Google\Service\Exception
   */
  public function addContextArtifactsAndExecutions($context, GoogleCloudAiplatformV1AddContextArtifactsAndExecutionsRequest $postBody, $optParams = [])
  {
    $params = ['context' => $context, 'postBody' => $postBody];
    $params = array_merge($params, $optParams);
    return $this->call('addContextArtifactsAndExecutions', [$params], GoogleCloudAiplatformV1AddContextArtifactsAndExecutionsResponse::class);
  }
  /**
   * Adds a set of Contexts as children to a parent Context. If any of the child
   * Contexts have already been added to the parent Context, they are simply
   * skipped. If this call would create a cycle or cause any Context to have more
   * than 10 parents, the request will fail with an INVALID_ARGUMENT error.
   * (contexts.addContextChildren)
   *
   * @param string $context Required. The resource name of the parent Context.
   * Format: `projects/{project}/locations/{location}/metadataStores/{metadatastor
   * e}/contexts/{context}`
   * @param GoogleCloudAiplatformV1AddContextChildrenRequest $postBody
   * @param array $optParams Optional parameters.
   * @return GoogleCloudAiplatformV1AddContextChildrenResponse
   * @throws \Google\Service\Exception
   */
  public function addContextChildren($context, GoogleCloudAiplatformV1AddContextChildrenRequest $postBody, $optParams = [])
  {
    $params = ['context' => $context, 'postBody' => $postBody];
    $params = array_merge($params, $optParams);
    return $this->call('addContextChildren', [$params], GoogleCloudAiplatformV1AddContextChildrenResponse::class);
  }
  /**
   * Creates a Context associated with a MetadataStore. (contexts.create)
   *
   * @param string $parent Required. The resource name of the MetadataStore where
   * the Context should be created. Format:
   * `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
   * @param GoogleCloudAiplatformV1Context $postBody
   * @param array $optParams Optional parameters.
   *
   * @opt_param string contextId The {context} portion of the resource name with
   * the format: `projects/{project}/locations/{location}/metadataStores/{metadata
   * store}/contexts/{context}`. If not provided, the Context's ID will be a UUID
   * generated by the service. Must be 4-128 characters in length. Valid
   * characters are `/a-z-/`. Must be unique across all Contexts in the parent
   * MetadataStore. (Otherwise the request will fail with ALREADY_EXISTS, or
   * PERMISSION_DENIED if the caller can't view the preexisting Context.)
   * @return GoogleCloudAiplatformV1Context
   * @throws \Google\Service\Exception
   */
  public function create($parent, GoogleCloudAiplatformV1Context $postBody, $optParams = [])
  {
    $params = ['parent' => $parent, 'postBody' => $postBody];
    $params = array_merge($params, $optParams);
    return $this->call('create', [$params], GoogleCloudAiplatformV1Context::class);
  }
  /**
   * Deletes a stored Context. (contexts.delete)
   *
   * @param string $name Required. The resource name of the Context to delete.
   * Format: `projects/{project}/locations/{location}/metadataStores/{metadatastor
   * e}/contexts/{context}`
   * @param array $optParams Optional parameters.
   *
   * @opt_param string etag Optional. The etag of the Context to delete. If this
   * is provided, it must match the server's etag. Otherwise, the request will
   * fail with a FAILED_PRECONDITION.
   * @opt_param bool force The force deletion semantics is still undefined. Users
   * should not use this field.
   * @return GoogleLongrunningOperation
   * @throws \Google\Service\Exception
   */
  public function delete($name, $optParams = [])
  {
    $params = ['name' => $name];
    $params = array_merge($params, $optParams);
    return $this->call('delete', [$params], GoogleLongrunningOperation::class);
  }
  /**
   * Retrieves a specific Context. (contexts.get)
   *
   * @param string $name Required. The resource name of the Context to retrieve.
   * Format: `projects/{project}/locations/{location}/metadataStores/{metadatastor
   * e}/contexts/{context}`
   * @param array $optParams Optional parameters.
   * @return GoogleCloudAiplatformV1Context
   * @throws \Google\Service\Exception
   */
  public function get($name, $optParams = [])
  {
    $params = ['name' => $name];
    $params = array_merge($params, $optParams);
    return $this->call('get', [$params], GoogleCloudAiplatformV1Context::class);
  }
  /**
   * Lists Contexts on the MetadataStore.
   * (contexts.listProjectsLocationsMetadataStoresContexts)
   *
   * @param string $parent Required. The MetadataStore whose Contexts should be
   * listed. Format:
   * `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
   * @param array $optParams Optional parameters.
   *
   * @opt_param string filter Filter specifying the boolean condition for the
   * Contexts to satisfy in order to be part of the result set. The syntax to
   * define filter query is based on https://google.aip.dev/160. Following are the
   * supported set of filters: * **Attribute filtering**: For example:
   * `display_name = "test"`. Supported fields include: `name`, `display_name`,
   * `schema_title`, `create_time`, and `update_time`. Time fields, such as
   * `create_time` and `update_time`, require values specified in RFC-3339 format.
   * For example: `create_time = "2020-11-19T11:30:00-04:00"`. * **Metadata
   * field**: To filter on metadata fields use traversal operation as follows:
   * `metadata..`. For example: `metadata.field_1.number_value = 10.0`. In case
   * the field name contains special characters (such as colon), one can embed it
   * inside double quote. For example: `metadata."field:1".number_value = 10.0` *
   * **Parent Child filtering**: To filter Contexts based on parent-child
   * relationship use the HAS operator as follows: ``` parent_contexts:
   * "projects//locations//metadataStores//contexts/" child_contexts:
   * "projects//locations//metadataStores//contexts/" ``` Each of the above
   * supported filters can be combined together using logical operators (`AND` &
   * `OR`). Maximum nested expression depth allowed is 5. For example:
   * `display_name = "test" AND metadata.field1.bool_value = true`.
   * @opt_param string orderBy How the list of messages is ordered. Specify the
   * values to order by and an ordering operation. The default sorting order is
   * ascending. To specify descending order for a field, users append a " desc"
   * suffix; for example: "foo desc, bar". Subfields are specified with a `.`
   * character, such as foo.bar. see https://google.aip.dev/132#ordering for more
   * details.
   * @opt_param int pageSize The maximum number of Contexts to return. The service
   * may return fewer. Must be in range 1-1000, inclusive. Defaults to 100.
   * @opt_param string pageToken A page token, received from a previous
   * MetadataService.ListContexts call. Provide this to retrieve the subsequent
   * page. When paginating, all other provided parameters must match the call that
   * provided the page token. (Otherwise the request will fail with
   * INVALID_ARGUMENT error.)
   * @return GoogleCloudAiplatformV1ListContextsResponse
   * @throws \Google\Service\Exception
   */
  public function listProjectsLocationsMetadataStoresContexts($parent, $optParams = [])
  {
    $params = ['parent' => $parent];
    $params = array_merge($params, $optParams);
    return $this->call('list', [$params], GoogleCloudAiplatformV1ListContextsResponse::class);
  }
  /**
   * Updates a stored Context. (contexts.patch)
   *
   * @param string $name Immutable. The resource name of the Context.
   * @param GoogleCloudAiplatformV1Context $postBody
   * @param array $optParams Optional parameters.
   *
   * @opt_param bool allowMissing If set to true, and the Context is not found, a
   * new Context is created.
   * @opt_param string updateMask Optional. A FieldMask indicating which fields
   * should be updated.
   * @return GoogleCloudAiplatformV1Context
   * @throws \Google\Service\Exception
   */
  public function patch($name, GoogleCloudAiplatformV1Context $postBody, $optParams = [])
  {
    $params = ['name' => $name, 'postBody' => $postBody];
    $params = array_merge($params, $optParams);
    return $this->call('patch', [$params], GoogleCloudAiplatformV1Context::class);
  }
  /**
   * Purges Contexts. (contexts.purge)
   *
   * @param string $parent Required. The metadata store to purge Contexts from.
   * Format:
   * `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
   * @param GoogleCloudAiplatformV1PurgeContextsRequest $postBody
   * @param array $optParams Optional parameters.
   * @return GoogleLongrunningOperation
   * @throws \Google\Service\Exception
   */
  public function purge($parent, GoogleCloudAiplatformV1PurgeContextsRequest $postBody, $optParams = [])
  {
    $params = ['parent' => $parent, 'postBody' => $postBody];
    $params = array_merge($params, $optParams);
    return $this->call('purge', [$params], GoogleLongrunningOperation::class);
  }
  /**
   * Retrieves Artifacts and Executions within the specified Context, connected by
   * Event edges and returned as a LineageSubgraph.
   * (contexts.queryContextLineageSubgraph)
   *
   * @param string $context Required. The resource name of the Context whose
   * Artifacts and Executions should be retrieved as a LineageSubgraph. Format: `p
   * rojects/{project}/locations/{location}/metadataStores/{metadatastore}/context
   * s/{context}` The request may error with FAILED_PRECONDITION if the number of
   * Artifacts, the number of Executions, or the number of Events that would be
   * returned for the Context exceeds 1000.
   * @param array $optParams Optional parameters.
   * @return GoogleCloudAiplatformV1LineageSubgraph
   * @throws \Google\Service\Exception
   */
  public function queryContextLineageSubgraph($context, $optParams = [])
  {
    $params = ['context' => $context];
    $params = array_merge($params, $optParams);
    return $this->call('queryContextLineageSubgraph', [$params], GoogleCloudAiplatformV1LineageSubgraph::class);
  }
  /**
   * Remove a set of children contexts from a parent Context. If any of the child
   * Contexts were NOT added to the parent Context, they are simply skipped.
   * (contexts.removeContextChildren)
   *
   * @param string $context Required. The resource name of the parent Context.
   * Format: `projects/{project}/locations/{location}/metadataStores/{metadatastor
   * e}/contexts/{context}`
   * @param GoogleCloudAiplatformV1RemoveContextChildrenRequest $postBody
   * @param array $optParams Optional parameters.
   * @return GoogleCloudAiplatformV1RemoveContextChildrenResponse
   * @throws \Google\Service\Exception
   */
  public function removeContextChildren($context, GoogleCloudAiplatformV1RemoveContextChildrenRequest $postBody, $optParams = [])
  {
    $params = ['context' => $context, 'postBody' => $postBody];
    $params = array_merge($params, $optParams);
    return $this->call('removeContextChildren', [$params], GoogleCloudAiplatformV1RemoveContextChildrenResponse::class);
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(ProjectsLocationsMetadataStoresContexts::class, 'Google_Service_Aiplatform_Resource_ProjectsLocationsMetadataStoresContexts');
