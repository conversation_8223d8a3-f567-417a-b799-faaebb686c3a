<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\AreaInsights;

class Circle extends \Google\Model
{
  protected $latLngType = LatLng::class;
  protected $latLngDataType = '';
  /**
   * @var string
   */
  public $place;
  /**
   * @var int
   */
  public $radius;

  /**
   * @param LatLng
   */
  public function setLatLng(LatLng $latLng)
  {
    $this->latLng = $latLng;
  }
  /**
   * @return LatLng
   */
  public function getLatLng()
  {
    return $this->latLng;
  }
  /**
   * @param string
   */
  public function setPlace($place)
  {
    $this->place = $place;
  }
  /**
   * @return string
   */
  public function getPlace()
  {
    return $this->place;
  }
  /**
   * @param int
   */
  public function setRadius($radius)
  {
    $this->radius = $radius;
  }
  /**
   * @return int
   */
  public function getRadius()
  {
    return $this->radius;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(Circle::class, 'Google_Service_AreaInsights_Circle');
