<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\AreaInsights;

class LocationFilter extends \Google\Model
{
  protected $circleType = Circle::class;
  protected $circleDataType = '';
  protected $customAreaType = CustomArea::class;
  protected $customAreaDataType = '';
  protected $regionType = Region::class;
  protected $regionDataType = '';

  /**
   * @param Circle
   */
  public function setCircle(Circle $circle)
  {
    $this->circle = $circle;
  }
  /**
   * @return Circle
   */
  public function getCircle()
  {
    return $this->circle;
  }
  /**
   * @param CustomArea
   */
  public function setCustomArea(CustomArea $customArea)
  {
    $this->customArea = $customArea;
  }
  /**
   * @return CustomArea
   */
  public function getCustomArea()
  {
    return $this->customArea;
  }
  /**
   * @param Region
   */
  public function setRegion(Region $region)
  {
    $this->region = $region;
  }
  /**
   * @return Region
   */
  public function getRegion()
  {
    return $this->region;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(LocationFilter::class, 'Google_Service_AreaInsights_LocationFilter');
