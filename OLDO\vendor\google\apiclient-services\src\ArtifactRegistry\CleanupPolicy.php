<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\ArtifactRegistry;

class CleanupPolicy extends \Google\Model
{
  /**
   * @var string
   */
  public $action;
  protected $conditionType = CleanupPolicyCondition::class;
  protected $conditionDataType = '';
  /**
   * @var string
   */
  public $id;
  protected $mostRecentVersionsType = CleanupPolicyMostRecentVersions::class;
  protected $mostRecentVersionsDataType = '';

  /**
   * @param string
   */
  public function setAction($action)
  {
    $this->action = $action;
  }
  /**
   * @return string
   */
  public function getAction()
  {
    return $this->action;
  }
  /**
   * @param CleanupPolicyCondition
   */
  public function setCondition(CleanupPolicyCondition $condition)
  {
    $this->condition = $condition;
  }
  /**
   * @return CleanupPolicyCondition
   */
  public function getCondition()
  {
    return $this->condition;
  }
  /**
   * @param string
   */
  public function setId($id)
  {
    $this->id = $id;
  }
  /**
   * @return string
   */
  public function getId()
  {
    return $this->id;
  }
  /**
   * @param CleanupPolicyMostRecentVersions
   */
  public function setMostRecentVersions(CleanupPolicyMostRecentVersions $mostRecentVersions)
  {
    $this->mostRecentVersions = $mostRecentVersions;
  }
  /**
   * @return CleanupPolicyMostRecentVersions
   */
  public function getMostRecentVersions()
  {
    return $this->mostRecentVersions;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(CleanupPolicy::class, 'Google_Service_ArtifactRegistry_CleanupPolicy');
