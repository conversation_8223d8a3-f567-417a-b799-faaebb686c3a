<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Assuredworkloads;

class GoogleCloudAssuredworkloadsV1WorkloadEkmProvisioningResponse extends \Google\Model
{
  /**
   * @var string
   */
  public $ekmProvisioningErrorDomain;
  /**
   * @var string
   */
  public $ekmProvisioningErrorMapping;
  /**
   * @var string
   */
  public $ekmProvisioningState;

  /**
   * @param string
   */
  public function setEkmProvisioningErrorDomain($ekmProvisioningErrorDomain)
  {
    $this->ekmProvisioningErrorDomain = $ekmProvisioningErrorDomain;
  }
  /**
   * @return string
   */
  public function getEkmProvisioningErrorDomain()
  {
    return $this->ekmProvisioningErrorDomain;
  }
  /**
   * @param string
   */
  public function setEkmProvisioningErrorMapping($ekmProvisioningErrorMapping)
  {
    $this->ekmProvisioningErrorMapping = $ekmProvisioningErrorMapping;
  }
  /**
   * @return string
   */
  public function getEkmProvisioningErrorMapping()
  {
    return $this->ekmProvisioningErrorMapping;
  }
  /**
   * @param string
   */
  public function setEkmProvisioningState($ekmProvisioningState)
  {
    $this->ekmProvisioningState = $ekmProvisioningState;
  }
  /**
   * @return string
   */
  public function getEkmProvisioningState()
  {
    return $this->ekmProvisioningState;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudAssuredworkloadsV1WorkloadEkmProvisioningResponse::class, 'Google_Service_Assuredworkloads_GoogleCloudAssuredworkloadsV1WorkloadEkmProvisioningResponse');
