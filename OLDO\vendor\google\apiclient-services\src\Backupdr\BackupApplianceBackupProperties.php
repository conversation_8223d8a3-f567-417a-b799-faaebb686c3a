<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Backupdr;

class BackupApplianceBackupProperties extends \Google\Model
{
  /**
   * @var string
   */
  public $finalizeTime;
  /**
   * @var int
   */
  public $generationId;
  /**
   * @var string
   */
  public $recoveryRangeEndTime;
  /**
   * @var string
   */
  public $recoveryRangeStartTime;

  /**
   * @param string
   */
  public function setFinalizeTime($finalizeTime)
  {
    $this->finalizeTime = $finalizeTime;
  }
  /**
   * @return string
   */
  public function getFinalizeTime()
  {
    return $this->finalizeTime;
  }
  /**
   * @param int
   */
  public function setGenerationId($generationId)
  {
    $this->generationId = $generationId;
  }
  /**
   * @return int
   */
  public function getGenerationId()
  {
    return $this->generationId;
  }
  /**
   * @param string
   */
  public function setRecoveryRangeEndTime($recoveryRangeEndTime)
  {
    $this->recoveryRangeEndTime = $recoveryRangeEndTime;
  }
  /**
   * @return string
   */
  public function getRecoveryRangeEndTime()
  {
    return $this->recoveryRangeEndTime;
  }
  /**
   * @param string
   */
  public function setRecoveryRangeStartTime($recoveryRangeStartTime)
  {
    $this->recoveryRangeStartTime = $recoveryRangeStartTime;
  }
  /**
   * @return string
   */
  public function getRecoveryRangeStartTime()
  {
    return $this->recoveryRangeStartTime;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(BackupApplianceBackupProperties::class, 'Google_Service_Backupdr_BackupApplianceBackupProperties');
