<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Baremetalsolution;

class GoogleCloudBaremetalsolutionV2LogicalInterface extends \Google\Collection
{
  protected $collection_key = 'logicalNetworkInterfaces';
  /**
   * @var int
   */
  public $interfaceIndex;
  protected $logicalNetworkInterfacesType = LogicalNetworkInterface::class;
  protected $logicalNetworkInterfacesDataType = 'array';
  /**
   * @var string
   */
  public $name;

  /**
   * @param int
   */
  public function setInterfaceIndex($interfaceIndex)
  {
    $this->interfaceIndex = $interfaceIndex;
  }
  /**
   * @return int
   */
  public function getInterfaceIndex()
  {
    return $this->interfaceIndex;
  }
  /**
   * @param LogicalNetworkInterface[]
   */
  public function setLogicalNetworkInterfaces($logicalNetworkInterfaces)
  {
    $this->logicalNetworkInterfaces = $logicalNetworkInterfaces;
  }
  /**
   * @return LogicalNetworkInterface[]
   */
  public function getLogicalNetworkInterfaces()
  {
    return $this->logicalNetworkInterfaces;
  }
  /**
   * @param string
   */
  public function setName($name)
  {
    $this->name = $name;
  }
  /**
   * @return string
   */
  public function getName()
  {
    return $this->name;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudBaremetalsolutionV2LogicalInterface::class, 'Google_Service_Baremetalsolution_GoogleCloudBaremetalsolutionV2LogicalInterface');
