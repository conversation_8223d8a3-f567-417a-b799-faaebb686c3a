<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Baremetalsolution;

class OSImage extends \Google\Collection
{
  protected $collection_key = 'supportedNetworkTemplates';
  /**
   * @var string[]
   */
  public $applicableInstanceTypes;
  /**
   * @var string
   */
  public $code;
  /**
   * @var string
   */
  public $description;
  /**
   * @var string
   */
  public $name;
  /**
   * @var string[]
   */
  public $supportedNetworkTemplates;

  /**
   * @param string[]
   */
  public function setApplicableInstanceTypes($applicableInstanceTypes)
  {
    $this->applicableInstanceTypes = $applicableInstanceTypes;
  }
  /**
   * @return string[]
   */
  public function getApplicableInstanceTypes()
  {
    return $this->applicableInstanceTypes;
  }
  /**
   * @param string
   */
  public function setCode($code)
  {
    $this->code = $code;
  }
  /**
   * @return string
   */
  public function getCode()
  {
    return $this->code;
  }
  /**
   * @param string
   */
  public function setDescription($description)
  {
    $this->description = $description;
  }
  /**
   * @return string
   */
  public function getDescription()
  {
    return $this->description;
  }
  /**
   * @param string
   */
  public function setName($name)
  {
    $this->name = $name;
  }
  /**
   * @return string
   */
  public function getName()
  {
    return $this->name;
  }
  /**
   * @param string[]
   */
  public function setSupportedNetworkTemplates($supportedNetworkTemplates)
  {
    $this->supportedNetworkTemplates = $supportedNetworkTemplates;
  }
  /**
   * @return string[]
   */
  public function getSupportedNetworkTemplates()
  {
    return $this->supportedNetworkTemplates;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(OSImage::class, 'Google_Service_Baremetalsolution_OSImage');
