<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Doubleclicksearch;

class ReportRequestFilters extends \Google\Collection
{
  protected $collection_key = 'values';
  protected $columnType = ReportApiColumnSpec::class;
  protected $columnDataType = '';
  /**
   * @var string
   */
  public $operator;
  /**
   * @var array[]
   */
  public $values;

  /**
   * @param ReportApiColumnSpec
   */
  public function setColumn(ReportApiColumnSpec $column)
  {
    $this->column = $column;
  }
  /**
   * @return ReportApiColumnSpec
   */
  public function getColumn()
  {
    return $this->column;
  }
  /**
   * @param string
   */
  public function setOperator($operator)
  {
    $this->operator = $operator;
  }
  /**
   * @return string
   */
  public function getOperator()
  {
    return $this->operator;
  }
  /**
   * @param array[]
   */
  public function setValues($values)
  {
    $this->values = $values;
  }
  /**
   * @return array[]
   */
  public function getValues()
  {
    return $this->values;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(ReportRequestFilters::class, 'Google_Service_Doubleclicksearch_ReportRequestFilters');
