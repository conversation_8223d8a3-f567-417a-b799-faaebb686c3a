<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\DriveActivity;

class FieldValueChange extends \Google\Model
{
  /**
   * @var string
   */
  public $displayName;
  /**
   * @var string
   */
  public $fieldId;
  protected $newValueType = FieldValue::class;
  protected $newValueDataType = '';
  protected $oldValueType = FieldValue::class;
  protected $oldValueDataType = '';

  /**
   * @param string
   */
  public function setDisplayName($displayName)
  {
    $this->displayName = $displayName;
  }
  /**
   * @return string
   */
  public function getDisplayName()
  {
    return $this->displayName;
  }
  /**
   * @param string
   */
  public function setFieldId($fieldId)
  {
    $this->fieldId = $fieldId;
  }
  /**
   * @return string
   */
  public function getFieldId()
  {
    return $this->fieldId;
  }
  /**
   * @param FieldValue
   */
  public function setNewValue(FieldValue $newValue)
  {
    $this->newValue = $newValue;
  }
  /**
   * @return FieldValue
   */
  public function getNewValue()
  {
    return $this->newValue;
  }
  /**
   * @param FieldValue
   */
  public function setOldValue(FieldValue $oldValue)
  {
    $this->oldValue = $oldValue;
  }
  /**
   * @return FieldValue
   */
  public function getOldValue()
  {
    return $this->oldValue;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(FieldValueChange::class, 'Google_Service_DriveActivity_FieldValueChange');
