<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\DriveActivity;

class KnownUser extends \Google\Model
{
  /**
   * @var bool
   */
  public $isCurrentUser;
  /**
   * @var string
   */
  public $personName;

  /**
   * @param bool
   */
  public function setIsCurrentUser($isCurrentUser)
  {
    $this->isCurrentUser = $isCurrentUser;
  }
  /**
   * @return bool
   */
  public function getIsCurrentUser()
  {
    return $this->isCurrentUser;
  }
  /**
   * @param string
   */
  public function setPersonName($personName)
  {
    $this->personName = $personName;
  }
  /**
   * @return string
   */
  public function getPersonName()
  {
    return $this->personName;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(KnownUser::class, 'Google_Service_DriveActivity_KnownUser');
