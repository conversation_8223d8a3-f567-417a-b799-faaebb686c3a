<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\DriveActivity;

class QueryDriveActivityRequest extends \Google\Model
{
  /**
   * @var string
   */
  public $ancestorName;
  protected $consolidationStrategyType = ConsolidationStrategy::class;
  protected $consolidationStrategyDataType = '';
  /**
   * @var string
   */
  public $filter;
  /**
   * @var string
   */
  public $itemName;
  /**
   * @var int
   */
  public $pageSize;
  /**
   * @var string
   */
  public $pageToken;

  /**
   * @param string
   */
  public function setAncestorName($ancestorName)
  {
    $this->ancestorName = $ancestorName;
  }
  /**
   * @return string
   */
  public function getAncestorName()
  {
    return $this->ancestorName;
  }
  /**
   * @param ConsolidationStrategy
   */
  public function setConsolidationStrategy(ConsolidationStrategy $consolidationStrategy)
  {
    $this->consolidationStrategy = $consolidationStrategy;
  }
  /**
   * @return ConsolidationStrategy
   */
  public function getConsolidationStrategy()
  {
    return $this->consolidationStrategy;
  }
  /**
   * @param string
   */
  public function setFilter($filter)
  {
    $this->filter = $filter;
  }
  /**
   * @return string
   */
  public function getFilter()
  {
    return $this->filter;
  }
  /**
   * @param string
   */
  public function setItemName($itemName)
  {
    $this->itemName = $itemName;
  }
  /**
   * @return string
   */
  public function getItemName()
  {
    return $this->itemName;
  }
  /**
   * @param int
   */
  public function setPageSize($pageSize)
  {
    $this->pageSize = $pageSize;
  }
  /**
   * @return int
   */
  public function getPageSize()
  {
    return $this->pageSize;
  }
  /**
   * @param string
   */
  public function setPageToken($pageToken)
  {
    $this->pageToken = $pageToken;
  }
  /**
   * @return string
   */
  public function getPageToken()
  {
    return $this->pageToken;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(QueryDriveActivityRequest::class, 'Google_Service_DriveActivity_QueryDriveActivityRequest');
