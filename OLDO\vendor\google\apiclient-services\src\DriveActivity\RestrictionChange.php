<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\DriveActivity;

class RestrictionChange extends \Google\Model
{
  /**
   * @var string
   */
  public $feature;
  /**
   * @var string
   */
  public $newRestriction;

  /**
   * @param string
   */
  public function setFeature($feature)
  {
    $this->feature = $feature;
  }
  /**
   * @return string
   */
  public function getFeature()
  {
    return $this->feature;
  }
  /**
   * @param string
   */
  public function setNewRestriction($newRestriction)
  {
    $this->newRestriction = $newRestriction;
  }
  /**
   * @return string
   */
  public function getNewRestriction()
  {
    return $this->newRestriction;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(RestrictionChange::class, 'Google_Service_DriveActivity_RestrictionChange');
