<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\DriveLabels;

class GoogleAppsDriveLabelsV2DeltaUpdateLabelRequestCreateSelectionChoiceRequest extends \Google\Model
{
  protected $choiceType = GoogleAppsDriveLabelsV2FieldSelectionOptionsChoice::class;
  protected $choiceDataType = '';
  /**
   * @var string
   */
  public $fieldId;

  /**
   * @param GoogleAppsDriveLabelsV2FieldSelectionOptionsChoice
   */
  public function setChoice(GoogleAppsDriveLabelsV2FieldSelectionOptionsChoice $choice)
  {
    $this->choice = $choice;
  }
  /**
   * @return GoogleAppsDriveLabelsV2FieldSelectionOptionsChoice
   */
  public function getChoice()
  {
    return $this->choice;
  }
  /**
   * @param string
   */
  public function setFieldId($fieldId)
  {
    $this->fieldId = $fieldId;
  }
  /**
   * @return string
   */
  public function getFieldId()
  {
    return $this->fieldId;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleAppsDriveLabelsV2DeltaUpdateLabelRequestCreateSelectionChoiceRequest::class, 'Google_Service_DriveLabels_GoogleAppsDriveLabelsV2DeltaUpdateLabelRequestCreateSelectionChoiceRequest');
