<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\DriveLabels;

class GoogleAppsDriveLabelsV2DeltaUpdateLabelResponse extends \Google\Collection
{
  protected $collection_key = 'responses';
  protected $responsesType = GoogleAppsDriveLabelsV2DeltaUpdateLabelResponseResponse::class;
  protected $responsesDataType = 'array';
  protected $updatedLabelType = GoogleAppsDriveLabelsV2Label::class;
  protected $updatedLabelDataType = '';

  /**
   * @param GoogleAppsDriveLabelsV2DeltaUpdateLabelResponseResponse[]
   */
  public function setResponses($responses)
  {
    $this->responses = $responses;
  }
  /**
   * @return GoogleAppsDriveLabelsV2DeltaUpdateLabelResponseResponse[]
   */
  public function getResponses()
  {
    return $this->responses;
  }
  /**
   * @param GoogleAppsDriveLabelsV2Label
   */
  public function setUpdatedLabel(GoogleAppsDriveLabelsV2Label $updatedLabel)
  {
    $this->updatedLabel = $updatedLabel;
  }
  /**
   * @return GoogleAppsDriveLabelsV2Label
   */
  public function getUpdatedLabel()
  {
    return $this->updatedLabel;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleAppsDriveLabelsV2DeltaUpdateLabelResponse::class, 'Google_Service_DriveLabels_GoogleAppsDriveLabelsV2DeltaUpdateLabelResponse');
