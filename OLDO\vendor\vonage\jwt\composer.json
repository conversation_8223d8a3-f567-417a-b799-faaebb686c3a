{"name": "vonage/jwt", "description": "A standalone package for creating JWTs for Vonage APIs", "type": "library", "require": {"php": "~8.1 || ~8.2 || ~8.3", "lcobucci/jwt": "^4.3.0|^5.0", "ramsey/uuid": "^4.7.5"}, "require-dev": {"phpunit/phpunit": "^8.5|^9.4", "phpstan/phpstan": "^1.10", "squizlabs/php_codesniffer": "^3.5"}, "license": "Apache-2.0", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "PHP Developer Advocate"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "autoload": {"psr-4": {"Vonage\\": "src/"}}, "autoload-dev": {"psr-4": {"Vonage\\": "test/", "VonageTest\\": "test/"}}, "scripts": {"phpstan": "phpstan", "cs-check": "phpcs", "cs-fix": "phpcbf", "test": "phpunit"}}