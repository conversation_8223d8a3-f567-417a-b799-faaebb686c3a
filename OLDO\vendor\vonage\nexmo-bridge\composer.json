{"name": "vonage/nexmo-bridge", "description": "Provides a bridge for using the Vonage PHP SDK with the older Nexmo namespace", "type": "library", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.1 || ^8.0"}, "autoload": {"files": ["src/autoload.php"], "psr-4": {"Vonage\\NexmoBridge\\": "src/"}}, "autoload-dev": {"files": ["test/classes.php"], "psr-4": {"VonageTest\\NexmoBridge\\": "test/", "Vonage\\": "test/TestAsset/Vonage/"}}, "require-dev": {"phpunit/phpunit": "^7.4", "phpstan/phpstan": "^0.12.39", "squizlabs/php_codesniffer": "^3.5"}}